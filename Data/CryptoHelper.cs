using System;
using System.Security.Cryptography;
using System.Text;

namespace CWDECC_3S.Data
{
    /// <summary>
    /// 加密工具類別
    /// </summary>
    public static class CryptoHelper
    {
        /// <summary>
        /// 使用 SHA256 雜湊演算法
        /// </summary>
        /// <param name="input">輸入字串</param>
        /// <returns>雜湊後的字串</returns>
        public static string HashWithSHA256(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// 使用 MD5 雜湊演算法（不建議用於安全用途）
        /// </summary>
        /// <param name="input">輸入字串</param>
        /// <returns>雜湊後的字串</returns>
        public static string HashWithMD5(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            using (var md5 = MD5.Create())
            {
                var hashedBytes = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// 產生隨機鹽值
        /// </summary>
        /// <param name="size">鹽值長度</param>
        /// <returns>鹽值</returns>
        public static string GenerateSalt(int size = 32)
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var saltBytes = new byte[size];
                rng.GetBytes(saltBytes);
                return Convert.ToBase64String(saltBytes);
            }
        }

        /// <summary>
        /// 使用鹽值進行密碼雜湊
        /// </summary>
        /// <param name="password">密碼</param>
        /// <param name="salt">鹽值</param>
        /// <returns>雜湊後的密碼</returns>
        public static string HashPasswordWithSalt(string password, string salt)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(salt))
                return string.Empty;

            return HashWithSHA256(password + salt);
        }

        /// <summary>
        /// 驗證密碼
        /// </summary>
        /// <param name="password">輸入的密碼</param>
        /// <param name="hashedPassword">已雜湊的密碼</param>
        /// <param name="salt">鹽值</param>
        /// <returns>驗證結果</returns>
        public static bool VerifyPassword(string password, string hashedPassword, string salt)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword) || string.IsNullOrEmpty(salt))
                return false;

            var hashOfInput = HashPasswordWithSalt(password, salt);
            return StringComparer.OrdinalIgnoreCase.Compare(hashOfInput, hashedPassword) == 0;
        }

        /// <summary>
        /// AES 加密
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="key">加密金鑰</param>
        /// <returns>加密後的字串</returns>
        public static string EncryptAES(string plainText, string key)
        {
            if (string.IsNullOrEmpty(plainText) || string.IsNullOrEmpty(key))
                return string.Empty;

            byte[] encrypted;
            byte[] keyBytes = Encoding.UTF8.GetBytes(key);

            // Ensure key is 32 bytes (256 bits)
            if (keyBytes.Length < 32)
            {
                Array.Resize(ref keyBytes, 32);
            }
            else if (keyBytes.Length > 32)
            {
                Array.Resize(ref keyBytes, 32);
            }

            using (var aesAlg = Aes.Create())
            {
                aesAlg.Key = keyBytes;
                aesAlg.Mode = CipherMode.CBC;
                aesAlg.Padding = PaddingMode.PKCS7;
                aesAlg.GenerateIV();

                var encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (var msEncrypt = new System.IO.MemoryStream())
                {
                    // Prepend IV to the beginning of the encrypted data
                    msEncrypt.Write(aesAlg.IV, 0, aesAlg.IV.Length);

                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new System.IO.StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }
                    encrypted = msEncrypt.ToArray();
                }
            }

            return Convert.ToBase64String(encrypted);
        }

        /// <summary>
        /// AES 解密
        /// </summary>
        /// <param name="cipherText">密文</param>
        /// <param name="key">解密金鑰</param>
        /// <returns>解密後的字串</returns>
        public static string DecryptAES(string cipherText, string key)
        {
            if (string.IsNullOrEmpty(cipherText) || string.IsNullOrEmpty(key))
                return string.Empty;

            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);

                // Ensure key is 32 bytes (256 bits)
                if (keyBytes.Length < 32)
                {
                    Array.Resize(ref keyBytes, 32);
                }
                else if (keyBytes.Length > 32)
                {
                    Array.Resize(ref keyBytes, 32);
                }

                using (var aesAlg = Aes.Create())
                {
                    aesAlg.Key = keyBytes;
                    aesAlg.Mode = CipherMode.CBC;
                    aesAlg.Padding = PaddingMode.PKCS7;

                    // Extract IV from the beginning of the cipher text
                    byte[] iv = new byte[16];
                    Array.Copy(cipherBytes, 0, iv, 0, 16);
                    aesAlg.IV = iv;

                    var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                    using (var msDecrypt = new System.IO.MemoryStream(cipherBytes, 16, cipherBytes.Length - 16))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new System.IO.StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 產生隨機字串
        /// </summary>
        /// <param name="length">字串長度</param>
        /// <param name="useSpecialChars">是否包含特殊字符</param>
        /// <returns>隨機字串</returns>
        public static string GenerateRandomString(int length = 16, bool useSpecialChars = false)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
            
            string validChars = useSpecialChars ? chars + specialChars : chars;

            using (var rng = RandomNumberGenerator.Create())
            {
                var result = new StringBuilder(length);
                var buffer = new byte[4];

                for (int i = 0; i < length; i++)
                {
                    rng.GetBytes(buffer);
                    var randomIndex = BitConverter.ToUInt32(buffer, 0) % validChars.Length;
                    result.Append(validChars[(int)randomIndex]);
                }

                return result.ToString();
            }
        }

        /// <summary>
        /// 產生 GUID
        /// </summary>
        /// <param name="removeDashes">是否移除破折號</param>
        /// <returns>GUID字串</returns>
        public static string GenerateGuid(bool removeDashes = false)
        {
            var guid = Guid.NewGuid().ToString();
            return removeDashes ? guid.Replace("-", "") : guid;
        }
    }
}