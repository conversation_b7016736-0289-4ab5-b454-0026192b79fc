using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Configuration;
using System.Linq;
using Google.Cloud.Firestore;
using FirebaseAdmin;
using FirebaseAdmin.Auth;
using Google.Apis.Auth.OAuth2;
using Newtonsoft.Json;

namespace CWDECC_3S.Data
{
    /// <summary>
    /// 增強版 Firebase 服務 - 整合 Firestore 和 Authentication
    /// 支援 Service Account 憑證管理和完整的 CRUD 操作
    /// </summary>
    public class EnhancedFirebaseService : IDisposable
    {
        #region 私有成員

        private FirestoreDb _firestoreDb;
        private FirebaseApp _firebaseApp;
        private readonly string _projectId;
        private readonly string _credentialsPath;
        private bool _disposed = false;

        #endregion

        #region 建構函數

        /// <summary>
        /// 預設建構函數 - 從配置讀取設定
        /// </summary>
        public EnhancedFirebaseService()
        {
            _projectId = GetConfigValue("FirebaseProjectId");
            _credentialsPath = GetSecureCredentialsPath();
            
            InitializeFirebase();
        }

        /// <summary>
        /// 指定專案 ID 的建構函數
        /// </summary>
        /// <param name="projectId">Firebase 專案 ID</param>
        public EnhancedFirebaseService(string projectId)
        {
            _projectId = projectId ?? throw new ArgumentNullException(nameof(projectId));
            _credentialsPath = GetSecureCredentialsPath();
            
            InitializeFirebase();
        }

        /// <summary>
        /// 完整建構函數 - 指定專案 ID 和憑證路徑
        /// </summary>
        /// <param name="projectId">Firebase 專案 ID</param>
        /// <param name="credentialsPath">Service Account 憑證檔案路徑</param>
        public EnhancedFirebaseService(string projectId, string credentialsPath)
        {
            _projectId = projectId ?? throw new ArgumentNullException(nameof(projectId));
            _credentialsPath = credentialsPath ?? throw new ArgumentNullException(nameof(credentialsPath));
            
            InitializeFirebase();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化 Firebase 服務
        /// </summary>
        private void InitializeFirebase()
        {
            try
            {
                ValidateConfiguration();
                InitializeFirebaseApp();
                InitializeFirestore();
                
                LogFirebaseEvent("Firebase 服務初始化成功");
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"Firebase 初始化失敗: {ex.Message}");
                throw new FirebaseConnectionException($"Firebase 初始化失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化 Firebase App
        /// </summary>
        private void InitializeFirebaseApp()
        {
            try
            {
                // 檢查是否已有預設應用程式
                if (FirebaseApp.DefaultInstance != null)
                {
                    _firebaseApp = FirebaseApp.DefaultInstance;
                    return;
                }

                // 驗證憑證檔案
                if (!File.Exists(_credentialsPath))
                {
                    throw new FileNotFoundException($"Service Account 憑證檔案不存在: {_credentialsPath}");
                }

                // 驗證憑證格式
                ValidateServiceAccountCredentials(_credentialsPath);

                // 建立 Firebase App
                var credential = GoogleCredential.FromFile(_credentialsPath);
                var app = FirebaseApp.Create(new AppOptions()
                {
                    Credential = credential,
                    ProjectId = _projectId
                });

                _firebaseApp = app;
                LogFirebaseEvent($"Firebase App 初始化成功 - 專案: {_projectId}");
            }
            catch (Exception ex)
            {
                throw new FirebaseCredentialsException($"Firebase App 初始化失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化 Firestore
        /// </summary>
        private void InitializeFirestore()
        {
            try
            {
                var credential = GoogleCredential.FromFile(_credentialsPath);
                _firestoreDb = new FirestoreDbBuilder
                {
                    ProjectId = _projectId,
                    Credential = credential
                }.Build();

                LogFirebaseEvent("Firestore 初始化成功");
            }
            catch (Exception ex)
            {
                throw new FirebaseConnectionException($"Firestore 初始化失敗: {ex.Message}", ex);
            }
        }

        #endregion

        #region CRUD 操作方法

        /// <summary>
        /// 取得集合引用
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        /// <returns>集合引用</returns>
        public CollectionReference GetCollection(string collectionName)
        {
            ValidateCollectionName(collectionName);
            EnsureInitialized();
            
            try
            {
                return _firestoreDb.Collection(collectionName);
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"取得集合失敗: {collectionName} - {ex.Message}");
                throw new FirebaseOperationException($"取得集合 '{collectionName}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件 ID</param>
        /// <returns>文件資料</returns>
        public async Task<T> GetDocument<T>(string collectionName, string documentId) where T : class
        {
            ValidateCollectionName(collectionName);
            ValidateDocumentId(documentId);
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);
                var snapshot = await document.GetSnapshotAsync();

                if (!snapshot.Exists)
                {
                    LogFirebaseEvent($"文件不存在: {collectionName}/{documentId}");
                    return null;
                }

                var result = snapshot.ConvertTo<T>();
                LogFirebaseEvent($"成功取得文件: {collectionName}/{documentId}");
                return result;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"取得文件失敗: {collectionName}/{documentId} - {ex.Message}");
                throw new FirebaseOperationException($"取得文件 '{collectionName}/{documentId}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 新增文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件 ID</param>
        /// <param name="data">要新增的資料</param>
        /// <returns>操作結果</returns>
        public async Task<bool> AddDocument<T>(string collectionName, string documentId, T data)
        {
            ValidateCollectionName(collectionName);
            ValidateDocumentId(documentId);
            ValidateData(data);
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);
                
                // 檢查文件是否已存在
                var existingSnapshot = await document.GetSnapshotAsync();
                if (existingSnapshot.Exists)
                {
                    throw new FirebaseOperationException($"文件 '{collectionName}/{documentId}' 已存在，請使用 UpdateDocument 方法");
                }

                await document.SetAsync(data);
                LogFirebaseEvent($"成功新增文件: {collectionName}/{documentId}");
                return true;
            }
            catch (FirebaseOperationException)
            {
                throw; // 重新拋出自定義例外
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"新增文件失敗: {collectionName}/{documentId} - {ex.Message}");
                throw new FirebaseOperationException($"新增文件 '{collectionName}/{documentId}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 新增文件（自動生成 ID）
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="data">要新增的資料</param>
        /// <returns>新文件的 ID</returns>
        public async Task<string> AddDocument<T>(string collectionName, T data)
        {
            ValidateCollectionName(collectionName);
            ValidateData(data);
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var document = await collection.AddAsync(data);
                
                string documentId = document.Id;
                LogFirebaseEvent($"成功新增文件: {collectionName}/{documentId}");
                return documentId;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"新增文件失敗: {collectionName} - {ex.Message}");
                throw new FirebaseOperationException($"新增文件到 '{collectionName}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件 ID</param>
        /// <param name="data">要更新的資料</param>
        /// <returns>操作結果</returns>
        public async Task<bool> UpdateDocument<T>(string collectionName, string documentId, T data)
        {
            ValidateCollectionName(collectionName);
            ValidateDocumentId(documentId);
            ValidateData(data);
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);

                // 檢查文件是否存在
                var snapshot = await document.GetSnapshotAsync();
                if (!snapshot.Exists)
                {
                    throw new FirebaseOperationException($"文件 '{collectionName}/{documentId}' 不存在，請使用 AddDocument 方法");
                }

                await document.SetAsync(data, SetOptions.Overwrite);
                LogFirebaseEvent($"成功更新文件: {collectionName}/{documentId}");
                return true;
            }
            catch (FirebaseOperationException)
            {
                throw; // 重新拋出自定義例外
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"更新文件失敗: {collectionName}/{documentId} - {ex.Message}");
                throw new FirebaseOperationException($"更新文件 '{collectionName}/{documentId}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 部分更新文件
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件 ID</param>
        /// <param name="updates">要更新的欄位</param>
        /// <returns>操作結果</returns>
        public async Task<bool> UpdateDocument(string collectionName, string documentId, Dictionary<string, object> updates)
        {
            ValidateCollectionName(collectionName);
            ValidateDocumentId(documentId);
            
            if (updates == null || updates.Count == 0)
            {
                throw new ArgumentException("更新資料不能為空");
            }
            
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);

                // 檢查文件是否存在
                var snapshot = await document.GetSnapshotAsync();
                if (!snapshot.Exists)
                {
                    throw new FirebaseOperationException($"文件 '{collectionName}/{documentId}' 不存在");
                }

                await document.UpdateAsync(updates);
                LogFirebaseEvent($"成功部分更新文件: {collectionName}/{documentId}");
                return true;
            }
            catch (FirebaseOperationException)
            {
                throw; // 重新拋出自定義例外
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"部分更新文件失敗: {collectionName}/{documentId} - {ex.Message}");
                throw new FirebaseOperationException($"部分更新文件 '{collectionName}/{documentId}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 刪除文件
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件 ID</param>
        /// <returns>操作結果</returns>
        public async Task<bool> DeleteDocument(string collectionName, string documentId)
        {
            ValidateCollectionName(collectionName);
            ValidateDocumentId(documentId);
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);

                // 檢查文件是否存在
                var snapshot = await document.GetSnapshotAsync();
                if (!snapshot.Exists)
                {
                    LogFirebaseEvent($"嘗試刪除不存在的文件: {collectionName}/{documentId}");
                    return false;
                }

                await document.DeleteAsync();
                LogFirebaseEvent($"成功刪除文件: {collectionName}/{documentId}");
                return true;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"刪除文件失敗: {collectionName}/{documentId} - {ex.Message}");
                throw new FirebaseOperationException($"刪除文件 '{collectionName}/{documentId}' 失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得集合中所有文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <returns>文件列表</returns>
        public async Task<IList<T>> GetAllDocuments<T>(string collectionName) where T : class
        {
            ValidateCollectionName(collectionName);
            EnsureInitialized();

            try
            {
                var collection = _firestoreDb.Collection(collectionName);
                var snapshot = await collection.GetSnapshotAsync();

                var results = new List<T>();
                foreach (var document in snapshot.Documents)
                {
                    results.Add(document.ConvertTo<T>());
                }

                LogFirebaseEvent($"成功取得集合: {collectionName}, 文件數量: {results.Count}");
                return results;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"取得集合失敗: {collectionName} - {ex.Message}");
                throw new FirebaseOperationException($"取得集合 '{collectionName}' 失敗: {ex.Message}", ex);
            }
        }

        #endregion

        #region Firebase Authentication 方法

        /// <summary>
        /// 檢查用戶是否已驗證
        /// </summary>
        /// <param name="idToken">Firebase ID Token</param>
        /// <returns>驗證結果</returns>
        public async Task<bool> IsAuthenticated(string idToken)
        {
            if (string.IsNullOrEmpty(idToken))
            {
                LogFirebaseEvent("驗證失敗: ID Token 為空");
                return false;
            }

            try
            {
                EnsureInitialized();
                
                var auth = FirebaseAuth.DefaultInstance;
                var decodedToken = await auth.VerifyIdTokenAsync(idToken);
                
                if (decodedToken != null)
                {
                    LogFirebaseEvent($"用戶驗證成功: {decodedToken.Uid}");
                    return true;
                }
                
                LogFirebaseEvent("驗證失敗: Token 無效");
                return false;
            }
            catch (FirebaseAuthException ex)
            {
                LogFirebaseEvent($"驗證失敗 (Auth 錯誤): {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"驗證失敗 (系統錯誤): {ex.Message}");
                throw new FirebaseAuthenticationException($"用戶驗證失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得用戶資訊
        /// </summary>
        /// <param name="idToken">Firebase ID Token</param>
        /// <returns>用戶資訊</returns>
        public async Task<FirebaseUserInfo> GetUserInfo(string idToken)
        {
            if (string.IsNullOrEmpty(idToken))
            {
                throw new ArgumentException("ID Token 不能為空");
            }

            try
            {
                EnsureInitialized();
                
                var auth = FirebaseAuth.DefaultInstance;
                var decodedToken = await auth.VerifyIdTokenAsync(idToken);
                
                if (decodedToken == null)
                {
                    throw new FirebaseAuthenticationException("無效的 ID Token");
                }

                var userRecord = await auth.GetUserAsync(decodedToken.Uid);
                
                return new FirebaseUserInfo
                {
                    Uid = userRecord.Uid,
                    Email = userRecord.Email,
                    DisplayName = userRecord.DisplayName,
                    EmailVerified = userRecord.EmailVerified,
                    PhotoUrl = userRecord.PhotoUrl,
                    CreationTimestamp = userRecord.UserMetaData.CreationTimestamp,
                    LastSignInTimestamp = userRecord.UserMetaData.LastSignInTimestamp
                };
            }
            catch (FirebaseAuthException ex)
            {
                LogFirebaseEvent($"取得用戶資訊失敗 (Auth 錯誤): {ex.Message}");
                throw new FirebaseAuthenticationException($"取得用戶資訊失敗: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"取得用戶資訊失敗 (系統錯誤): {ex.Message}");
                throw new FirebaseAuthenticationException($"取得用戶資訊失敗: {ex.Message}", ex);
            }
        }

        #endregion

        #region 連線測試方法

        /// <summary>
        /// 測試 Firebase 連線
        /// </summary>
        /// <returns>連線狀態</returns>
        public async Task<FirebaseConnectionStatus> TestConnection()
        {
            var status = new FirebaseConnectionStatus();
            
            try
            {
                EnsureInitialized();
                
                // 測試 Firestore 連線
                status.FirestoreConnected = await TestFirestoreConnection();
                
                // 測試 Authentication 連線
                status.AuthConnected = await TestAuthConnection();
                
                status.IsConnected = status.FirestoreConnected && status.AuthConnected;
                status.Message = status.IsConnected ? "所有服務連線正常" : "部分服務連線失敗";
                
                LogFirebaseEvent($"連線測試完成: Firestore={status.FirestoreConnected}, Auth={status.AuthConnected}");
            }
            catch (Exception ex)
            {
                status.IsConnected = false;
                status.FirestoreConnected = false;
                status.AuthConnected = false;
                status.Message = $"連線測試失敗: {ex.Message}";
                status.Error = ex;
                
                LogFirebaseEvent($"連線測試失敗: {ex.Message}");
            }
            
            return status;
        }

        /// <summary>
        /// 測試 Firestore 連線
        /// </summary>
        /// <returns>連線狀態</returns>
        private async Task<bool> TestFirestoreConnection()
        {
            try
            {
                if (_firestoreDb == null)
                    return false;

                // 嘗試讀取測試集合
                var testCollection = _firestoreDb.Collection("test_connection");
                var query = testCollection.Limit(1);
                await query.GetSnapshotAsync();
                
                return true;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"Firestore 連線測試失敗: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 測試 Authentication 連線
        /// </summary>
        /// <returns>連線狀態</returns>
        private async Task<bool> TestAuthConnection()
        {
            try
            {
                if (_firebaseApp == null)
                    return false;

                var auth = FirebaseAuth.DefaultInstance;
                
                // 嘗試取得用戶數量（測試 Auth 服務）
                var listUsers = auth.ListUsersAsync(new ListUsersOptions { PageSize = 1 });
                await listUsers.GetAsyncEnumerator().MoveNextAsync();
                
                return true;
            }
            catch (Exception ex)
            {
                LogFirebaseEvent($"Authentication 連線測試失敗: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 驗證和工具方法

        /// <summary>
        /// 取得配置值
        /// </summary>
        /// <param name="key">配置鍵</param>
        /// <returns>配置值</returns>
        private string GetConfigValue(string key)
        {
            var value = Environment.GetEnvironmentVariable(key) ?? ConfigurationManager.AppSettings[key];
            
            if (string.IsNullOrEmpty(value))
            {
                throw new InvalidOperationException($"未找到配置值: {key}。請在環境變數或 Web.config 中設定此值。");
            }
            
            return value;
        }

        /// <summary>
        /// 取得安全的憑證路徑
        /// </summary>
        /// <returns>憑證檔案路徑</returns>
        private string GetSecureCredentialsPath()
        {
            // 優先從環境變數取得
            var envPath = Environment.GetEnvironmentVariable("FIREBASE_CREDENTIALS_PATH");
            if (!string.IsNullOrEmpty(envPath) && File.Exists(envPath))
            {
                return envPath;
            }
            
            // 從配置檔案取得
            var configPath = ConfigurationManager.AppSettings["FirebaseCredentialsPath"];
            if (!string.IsNullOrEmpty(configPath))
            {
                // 支援相對路徑
                if (!Path.IsPathRooted(configPath))
                {
                    var webRoot = System.Web.HttpContext.Current?.Server.MapPath("~/");
                    if (!string.IsNullOrEmpty(webRoot))
                    {
                        configPath = Path.Combine(webRoot, configPath);
                    }
                }
                
                if (File.Exists(configPath))
                {
                    return configPath;
                }
            }
            
            // 預設安全路徑
            var defaultPaths = new[]
            {
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "firebase", "service-account.json"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data", "firebase-service-account.json"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "credentials", "firebase-service-account.json")
            };
            
            foreach (var path in defaultPaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }
            
            throw new FileNotFoundException("找不到 Firebase Service Account 憑證檔案。請設定環境變數 FIREBASE_CREDENTIALS_PATH 或配置 FirebaseCredentialsPath。");
        }

        /// <summary>
        /// 驗證配置
        /// </summary>
        private void ValidateConfiguration()
        {
            if (string.IsNullOrEmpty(_projectId))
            {
                throw new InvalidOperationException("Firebase 專案 ID 未設定");
            }
            
            if (string.IsNullOrEmpty(_credentialsPath))
            {
                throw new InvalidOperationException("Firebase 憑證路徑未設定");
            }
        }

        /// <summary>
        /// 驗證 Service Account 憑證檔案
        /// </summary>
        /// <param name="credentialsPath">憑證檔案路徑</param>
        private void ValidateServiceAccountCredentials(string credentialsPath)
        {
            try
            {
                var jsonContent = File.ReadAllText(credentialsPath);
                var credentials = JsonConvert.DeserializeObject<dynamic>(jsonContent);
                
                if (credentials.type == null || credentials.type != "service_account")
                {
                    throw new InvalidOperationException("憑證檔案格式錯誤：不是有效的 Service Account 憑證");
                }
                
                if (credentials.project_id == null)
                {
                    throw new InvalidOperationException("憑證檔案中缺少 project_id");
                }
                
                if (credentials.private_key == null)
                {
                    throw new InvalidOperationException("憑證檔案中缺少 private_key");
                }
                
                if (credentials.client_email == null)
                {
                    throw new InvalidOperationException("憑證檔案中缺少 client_email");
                }
            }
            catch (JsonException ex)
            {
                throw new InvalidOperationException($"憑證檔案 JSON 格式錯誤: {ex.Message}", ex);
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                throw new InvalidOperationException($"憑證檔案驗證失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 驗證集合名稱
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        private void ValidateCollectionName(string collectionName)
        {
            if (string.IsNullOrWhiteSpace(collectionName))
            {
                throw new ArgumentException("集合名稱不能為空");
            }
            
            if (collectionName.Length > 1500)
            {
                throw new ArgumentException("集合名稱過長（最大 1500 字符）");
            }
        }

        /// <summary>
        /// 驗證文件 ID
        /// </summary>
        /// <param name="documentId">文件 ID</param>
        private void ValidateDocumentId(string documentId)
        {
            if (string.IsNullOrWhiteSpace(documentId))
            {
                throw new ArgumentException("文件 ID 不能為空");
            }
            
            if (documentId.Length > 1500)
            {
                throw new ArgumentException("文件 ID 過長（最大 1500 字符）");
            }
        }

        /// <summary>
        /// 驗證資料
        /// </summary>
        /// <param name="data">資料</param>
        private void ValidateData(object data)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data), "資料不能為 null");
            }
        }

        /// <summary>
        /// 確保服務已初始化
        /// </summary>
        private void EnsureInitialized()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(EnhancedFirebaseService));
            }
            
            if (_firestoreDb == null)
            {
                throw new InvalidOperationException("Firestore 未初始化");
            }
        }

        /// <summary>
        /// 記錄 Firebase 事件
        /// </summary>
        /// <param name="message">事件訊息</param>
        private void LogFirebaseEvent(string message)
        {
            try
            {
                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [FIREBASE] {message}";
                
                // 記錄到檔案
                var logPath = System.Web.HttpContext.Current?.Server.MapPath("~/App_Data/Logs/firebase.log");
                if (!string.IsNullOrEmpty(logPath))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(logPath));
                    File.AppendAllText(logPath, logEntry + Environment.NewLine);
                }
            }
            catch
            {
                // 記錄失敗不應影響主要功能
            }
        }

        #endregion

        #region IDisposable 實作

        /// <summary>
        /// 釋放資源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        /// <param name="disposing">是否正在釋放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _firestoreDb?.Dispose();
                    
                    // Note: FirebaseApp 不需要手動釋放
                    LogFirebaseEvent("Firebase 服務已釋放資源");
                }
                catch (Exception ex)
                {
                    LogFirebaseEvent($"釋放資源時發生錯誤: {ex.Message}");
                }
                
                _disposed = true;
            }
        }

        /// <summary>
        /// 解構函數
        /// </summary>
        ~EnhancedFirebaseService()
        {
            Dispose(false);
        }

        #endregion
    }

    #region 相關類別定義

    /// <summary>
    /// Firebase 用戶資訊
    /// </summary>
    public class FirebaseUserInfo
    {
        public string Uid { get; set; }
        public string Email { get; set; }
        public string DisplayName { get; set; }
        public bool EmailVerified { get; set; }
        public string PhotoUrl { get; set; }
        public DateTime? CreationTimestamp { get; set; }
        public DateTime? LastSignInTimestamp { get; set; }
    }

    /// <summary>
    /// Firebase 連線狀態
    /// </summary>
    public class FirebaseConnectionStatus
    {
        public bool IsConnected { get; set; }
        public bool FirestoreConnected { get; set; }
        public bool AuthConnected { get; set; }
        public string Message { get; set; }
        public Exception Error { get; set; }
    }

    /// <summary>
    /// Firebase 連線例外
    /// </summary>
    public class FirebaseConnectionException : Exception
    {
        public FirebaseConnectionException(string message) : base(message) { }
        public FirebaseConnectionException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Firebase 憑證例外
    /// </summary>
    public class FirebaseCredentialsException : Exception
    {
        public FirebaseCredentialsException(string message) : base(message) { }
        public FirebaseCredentialsException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Firebase 操作例外
    /// </summary>
    public class FirebaseOperationException : Exception
    {
        public FirebaseOperationException(string message) : base(message) { }
        public FirebaseOperationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Firebase 驗證例外
    /// </summary>
    public class FirebaseAuthenticationException : Exception
    {
        public FirebaseAuthenticationException(string message) : base(message) { }
        public FirebaseAuthenticationException(string message, Exception innerException) : base(message, innerException) { }
    }

    #endregion
}