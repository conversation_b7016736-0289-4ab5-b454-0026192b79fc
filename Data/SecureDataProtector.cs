using System;
using System.Configuration;
using System.Security.Cryptography;
using System.Text;
using System.IO;
using System.Linq;

namespace CWDECC_3S.Data
{
    /// <summary>
    /// 企業級 AES-256 數據保護類 - 專為敏感會員資料設計
    /// 遵循 OWASP 加密標準和最佳實踐
    /// </summary>
    public static class SecureDataProtector
    {
        #region 安全常數與配置
        
        private const int AES_KEY_SIZE = 256; // AES-256
        private const int AES_BLOCK_SIZE = 128; // 16 bytes
        private const int IV_SIZE = 16; // 128 bits
        private const int KEY_SIZE = 32; // 256 bits
        private const int SALT_SIZE = 32; // 256 bits
        private const int TAG_SIZE = 16; // 128 bits for GCM (future enhancement)
        private const int MIN_PBKDF2_ITERATIONS = 100000; // OWASP recommended minimum
        
        // 密鑰版本標識符 (2 bytes)
        private const byte CURRENT_KEY_VERSION = 0x01;
        private const byte KEY_VERSION_MARKER = 0xFF;
        
        #endregion

        #region 密鑰管理
        
        /// <summary>
        /// 安全取得主密鑰 - 從環境變數或配置檔案
        /// </summary>
        /// <returns>Base64 編碼的主密鑰</returns>
        private static string GetMasterKey()
        {
            // 優先從環境變數取得 (生產環境)
            string masterKey = Environment.GetEnvironmentVariable("CWDECC_MASTER_KEY");
            
            if (string.IsNullOrEmpty(masterKey))
            {
                // 後備從加密配置檔案取得 (開發環境)
                masterKey = ConfigurationManager.AppSettings["EncryptionMasterKey"];
            }
            
            if (string.IsNullOrEmpty(masterKey))
            {
                throw new InvalidOperationException(
                    "主密鑰未設定。請設定環境變數 CWDECC_MASTER_KEY 或配置檔案 EncryptionMasterKey。" +
                    "生產環境必須使用環境變數。");
            }
            
            // 驗證密鑰格式和長度
            ValidateMasterKey(masterKey);
            
            return masterKey;
        }
        
        /// <summary>
        /// 驗證主密鑰的安全性要求
        /// </summary>
        /// <param name="masterKey">主密鑰</param>
        private static void ValidateMasterKey(string masterKey)
        {
            try
            {
                byte[] keyBytes = Convert.FromBase64String(masterKey);
                
                if (keyBytes.Length < KEY_SIZE)
                {
                    throw new ArgumentException($"主密鑰長度不足。要求至少 {KEY_SIZE} 字節，實際 {keyBytes.Length} 字節。");
                }
                
                // 檢查密鑰熵 - 避免弱密鑰
                if (IsWeakKey(keyBytes))
                {
                    throw new ArgumentException("檢測到弱密鑰。請使用高熵的隨機密鑰。");
                }
            }
            catch (FormatException)
            {
                throw new ArgumentException("主密鑰格式無效。必須是有效的 Base64 字串。");
            }
        }
        
        /// <summary>
        /// 檢測弱密鑰（簡單熵檢查）
        /// </summary>
        /// <param name="keyBytes">密鑰字節陣列</param>
        /// <returns>是否為弱密鑰</returns>
        private static bool IsWeakKey(byte[] keyBytes)
        {
            // 檢查全零或重複字節
            var uniqueBytes = keyBytes.Distinct().Count();
            return uniqueBytes < 8; // 至少要有 8 種不同的字節值
        }
        
        /// <summary>
        /// 使用 PBKDF2 衍生數據加密密鑰
        /// </summary>
        /// <param name="masterKey">主密鑰</param>
        /// <param name="salt">鹽值</param>
        /// <param name="keyVersion">密鑰版本</param>
        /// <returns>衍生的數據加密密鑰</returns>
        private static byte[] DeriveDataKey(string masterKey, byte[] salt, byte keyVersion)
        {
            byte[] masterKeyBytes = Convert.FromBase64String(masterKey);
            
            // 加入版本資訊到鹽值中
            byte[] versionedSalt = new byte[salt.Length + 1];
            Array.Copy(salt, 0, versionedSalt, 0, salt.Length);
            versionedSalt[salt.Length] = keyVersion;
            
            using (var pbkdf2 = new Rfc2898DeriveBytes(masterKeyBytes, versionedSalt, MIN_PBKDF2_ITERATIONS, HashAlgorithmName.SHA256))
            {
                return pbkdf2.GetBytes(KEY_SIZE);
            }
        }
        
        #endregion

        #region 加密/解密核心功能
        
        /// <summary>
        /// 加密敏感數據 - 主要入口點
        /// </summary>
        /// <param name="plainText">明文數據</param>
        /// <returns>Base64 編碼的加密數據</returns>
        public static string Encrypt(string plainText)
        {
            // 輸入驗證
            if (string.IsNullOrEmpty(plainText))
            {
                return string.Empty;
            }
            
            try
            {
                // 記錄加密操作（不記錄敏感數據）
                LogSecurityEvent($"數據加密請求 - 數據長度: {plainText.Length} 字符");
                
                string masterKey = GetMasterKey();
                byte[] result = EncryptInternal(plainText, masterKey, CURRENT_KEY_VERSION);
                
                return Convert.ToBase64String(result);
            }
            catch (Exception ex)
            {
                LogSecurityEvent($"加密失敗: {ex.Message}");
                throw new CryptographicException("數據加密失敗", ex);
            }
        }
        
        /// <summary>
        /// 解密敏感數據 - 主要入口點
        /// </summary>
        /// <param name="cipherText">Base64 編碼的密文</param>
        /// <returns>解密後的明文</returns>
        public static string Decrypt(string cipherText)
        {
            // 輸入驗證
            if (string.IsNullOrEmpty(cipherText))
            {
                return string.Empty;
            }
            
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);
                string masterKey = GetMasterKey();
                
                string result = DecryptInternal(cipherBytes, masterKey);
                
                LogSecurityEvent($"數據解密成功 - 結果長度: {result.Length} 字符");
                return result;
            }
            catch (FormatException)
            {
                LogSecurityEvent("解密失敗: 無效的 Base64 格式");
                throw new CryptographicException("密文格式無效");
            }
            catch (Exception ex)
            {
                LogSecurityEvent($"解密失敗: {ex.Message}");
                throw new CryptographicException("數據解密失敗", ex);
            }
        }
        
        /// <summary>
        /// 內部加密實作
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="masterKey">主密鑰</param>
        /// <param name="keyVersion">密鑰版本</param>
        /// <returns>加密後的字節陣列</returns>
        private static byte[] EncryptInternal(string plainText, string masterKey, byte keyVersion)
        {
            byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
            byte[] salt = GenerateRandomBytes(SALT_SIZE);
            byte[] iv = GenerateRandomBytes(IV_SIZE);
            
            // 衍生數據加密密鑰
            byte[] dataKey = DeriveDataKey(masterKey, salt, keyVersion);
            
            byte[] cipherBytes;
            
            using (var aes = Aes.Create())
            {
                aes.KeySize = AES_KEY_SIZE;
                aes.BlockSize = AES_BLOCK_SIZE;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = dataKey;
                aes.IV = iv;
                
                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new MemoryStream())
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                {
                    csEncrypt.Write(plainBytes, 0, plainBytes.Length);
                    csEncrypt.FlushFinalBlock();
                    cipherBytes = msEncrypt.ToArray();
                }
            }
            
            // 清除敏感資料
            Array.Clear(dataKey, 0, dataKey.Length);
            Array.Clear(plainBytes, 0, plainBytes.Length);
            
            // 組合最終輸出: 版本標記(1) + 密鑰版本(1) + 鹽值(32) + IV(16) + 密文(可變長度)
            return CombineEncryptedData(keyVersion, salt, iv, cipherBytes);
        }
        
        /// <summary>
        /// 內部解密實作
        /// </summary>
        /// <param name="encryptedData">加密的數據</param>
        /// <param name="masterKey">主密鑰</param>
        /// <returns>解密後的明文</returns>
        private static string DecryptInternal(byte[] encryptedData, string masterKey)
        {
            // 解析加密數據結構
            var parsedData = ParseEncryptedData(encryptedData);
            byte[] salt = parsedData.Salt;
            byte[] iv = parsedData.IV;
            byte[] cipherBytes = parsedData.CipherBytes;
            byte keyVersion = parsedData.KeyVersion;
            
            // 衍生數據解密密鑰
            byte[] dataKey = DeriveDataKey(masterKey, salt, keyVersion);
            
            byte[] plainBytes;
            
            using (var aes = Aes.Create())
            {
                aes.KeySize = AES_KEY_SIZE;
                aes.BlockSize = AES_BLOCK_SIZE;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = dataKey;
                aes.IV = iv;
                
                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new MemoryStream(cipherBytes))
                using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (var msPlain = new MemoryStream())
                {
                    csDecrypt.CopyTo(msPlain);
                    plainBytes = msPlain.ToArray();
                }
            }
            
            // 清除敏感資料
            Array.Clear(dataKey, 0, dataKey.Length);
            
            string result = Encoding.UTF8.GetString(plainBytes);
            
            // 清除明文字節陣列
            Array.Clear(plainBytes, 0, plainBytes.Length);
            
            return result;
        }
        
        #endregion

        #region 數據結構處理
        
        /// <summary>
        /// 加密數據解析結果
        /// </summary>
        private struct EncryptedDataResult
        {
            public byte KeyVersion;
            public byte[] Salt;
            public byte[] IV;
            public byte[] CipherBytes;
        }
        
        /// <summary>
        /// 組合加密數據
        /// </summary>
        /// <param name="keyVersion">密鑰版本</param>
        /// <param name="salt">鹽值</param>
        /// <param name="iv">初始向量</param>
        /// <param name="cipherBytes">密文</param>
        /// <returns>組合後的加密數據</returns>
        private static byte[] CombineEncryptedData(byte keyVersion, byte[] salt, byte[] iv, byte[] cipherBytes)
        {
            byte[] result = new byte[1 + 1 + salt.Length + iv.Length + cipherBytes.Length];
            int offset = 0;
            
            // 版本標記
            result[offset++] = KEY_VERSION_MARKER;
            
            // 密鑰版本
            result[offset++] = keyVersion;
            
            // 鹽值
            Array.Copy(salt, 0, result, offset, salt.Length);
            offset += salt.Length;
            
            // IV
            Array.Copy(iv, 0, result, offset, iv.Length);
            offset += iv.Length;
            
            // 密文
            Array.Copy(cipherBytes, 0, result, offset, cipherBytes.Length);
            
            return result;
        }
        
        /// <summary>
        /// 解析加密數據結構
        /// </summary>
        /// <param name="encryptedData">加密數據</param>
        /// <returns>解析結果</returns>
        private static EncryptedDataResult ParseEncryptedData(byte[] encryptedData)
        {
            if (encryptedData.Length < 2 + SALT_SIZE + IV_SIZE + 16) // 最小長度檢查
            {
                throw new CryptographicException("加密數據長度無效");
            }
            
            int offset = 0;
            
            // 檢查版本標記
            if (encryptedData[offset++] != KEY_VERSION_MARKER)
            {
                throw new CryptographicException("無效的加密數據格式");
            }
            
            // 讀取密鑰版本
            byte keyVersion = encryptedData[offset++];
            
            // 讀取鹽值
            byte[] salt = new byte[SALT_SIZE];
            Array.Copy(encryptedData, offset, salt, 0, SALT_SIZE);
            offset += SALT_SIZE;
            
            // 讀取 IV
            byte[] iv = new byte[IV_SIZE];
            Array.Copy(encryptedData, offset, iv, 0, IV_SIZE);
            offset += IV_SIZE;
            
            // 讀取密文
            byte[] cipherBytes = new byte[encryptedData.Length - offset];
            Array.Copy(encryptedData, offset, cipherBytes, 0, cipherBytes.Length);
            
            return new EncryptedDataResult
            {
                KeyVersion = keyVersion,
                Salt = salt,
                IV = iv,
                CipherBytes = cipherBytes
            };
        }
        
        #endregion

        #region 工具方法
        
        /// <summary>
        /// 產生密碼學安全的隨機字節
        /// </summary>
        /// <param name="size">字節數量</param>
        /// <returns>隨機字節陣列</returns>
        private static byte[] GenerateRandomBytes(int size)
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                byte[] bytes = new byte[size];
                rng.GetBytes(bytes);
                return bytes;
            }
        }
        
        /// <summary>
        /// 產生新的主密鑰 (用於初始設定)
        /// </summary>
        /// <returns>Base64 編碼的隨機主密鑰</returns>
        public static string GenerateNewMasterKey()
        {
            byte[] keyBytes = GenerateRandomBytes(KEY_SIZE);
            return Convert.ToBase64String(keyBytes);
        }
        
        /// <summary>
        /// 驗證密文是否可被當前密鑰解密（不實際解密）
        /// </summary>
        /// <param name="cipherText">密文</param>
        /// <returns>是否可解密</returns>
        public static bool CanDecrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return false;
                
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);
                var parsedData = ParseEncryptedData(cipherBytes);
                
                // 檢查版本是否支援
                return parsedData.KeyVersion <= CURRENT_KEY_VERSION;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 取得密文的密鑰版本
        /// </summary>
        /// <param name="cipherText">密文</param>
        /// <returns>密鑰版本</returns>
        public static byte GetCipherKeyVersion(string cipherText)
        {
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);
                var parsedData = ParseEncryptedData(cipherBytes);
                return parsedData.KeyVersion;
            }
            catch
            {
                return 0;
            }
        }
        
        #endregion

        #region 安全日誌
        
        /// <summary>
        /// 記錄安全事件
        /// </summary>
        /// <param name="message">事件訊息</param>
        private static void LogSecurityEvent(string message)
        {
            try
            {
                // 這裡可以整合到企業日誌系統
                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [SECURITY] {message}";
                
                // 簡單的檔案日誌（生產環境應使用更安全的日誌系統）
                var logPath = System.Web.HttpContext.Current?.Server.MapPath("~/App_Data/Logs/security.log");
                if (!string.IsNullOrEmpty(logPath))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(logPath));
                    File.AppendAllText(logPath, logEntry + Environment.NewLine);
                }
            }
            catch
            {
                // 記錄失敗不應影響主要功能
            }
        }
        
        #endregion
    }
}