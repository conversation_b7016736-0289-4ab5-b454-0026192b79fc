using System;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using Microsoft.AspNet.Identity.EntityFramework;
using CWDECC_3S.Models;
using CWDECC_3S.Services;

namespace CWDECC_3S.Data
{
    /// <summary>
    /// 應用程式資料庫上下文 - Entity Framework + ASP.NET Identity + MariaDB
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext() : base("DefaultConnection", throwIfV1Schema: false)
        {
            // 設定資料庫初始化策略
            Database.SetInitializer(new ApplicationDbInitializer());
            
            // 設定 Command Timeout（針對 MariaDB 優化）
            Database.CommandTimeout = 60;
        }

        #region DbSets

        /// <summary>
        /// 審計日誌
        /// </summary>
        public DbSet<AuditLog> AuditLogs { get; set; }

        /// <summary>
        /// 應用程式角色
        /// </summary>
        public DbSet<ApplicationRole> ApplicationRoles { get; set; }

        /// <summary>
        /// 用戶角色關聯
        /// </summary>
        public DbSet<ApplicationUserRole> ApplicationUserRoles { get; set; }

        /// <summary>
        /// IP 封鎖記錄
        /// </summary>
        public DbSet<IPBlockRecord> IPBlockRecords { get; set; }

        /// <summary>
        /// 角色權限
        /// </summary>
        public DbSet<RolePermission> RolePermissions { get; set; }

        /// <summary>
        /// 系統模組
        /// </summary>
        public DbSet<SystemModule> SystemModules { get; set; }

        /// <summary>
        /// 會員資料
        /// </summary>
        public DbSet<Member> Members { get; set; }

        /// <summary>
        /// 活動資料
        /// </summary>
        public DbSet<Activity> Activities { get; set; }

        /// <summary>
        /// 活動報名記錄
        /// </summary>
        public DbSet<ActivityRegistration> ActivityRegistrations { get; set; }

        /// <summary>
        /// 會員活動參與記錄
        /// </summary>
        public DbSet<MemberActivity> MemberActivities { get; set; }

        /// <summary>
        /// 膳食項目
        /// </summary>
        public DbSet<MealItem> MealItems { get; set; }

        /// <summary>
        /// 膳食訂單
        /// </summary>
        public DbSet<MealOrder> MealOrders { get; set; }

        /// <summary>
        /// 膳食每日庫存
        /// </summary>
        public DbSet<MealDailyStock> MealDailyStocks { get; set; }

        /// <summary>
        /// 膳食統計資料
        /// </summary>
        public DbSet<MealStatistics> MealStatisticsRecords { get; set; }

        #endregion

        #region Model Configuration

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 移除複數化表名約定
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();

            // 配置 ASP.NET Identity 表名（符合 MariaDB 命名規範）
            modelBuilder.Entity<ApplicationUser>().ToTable("AspNetUsers");
            modelBuilder.Entity<ApplicationRole>().ToTable("AspNetRoles");
            modelBuilder.Entity<IdentityUserRole>().ToTable("AspNetUserRoles");
            modelBuilder.Entity<IdentityUserLogin>().ToTable("AspNetUserLogins");
            modelBuilder.Entity<IdentityUserClaim>().ToTable("AspNetUserClaims");

            // ApplicationUser 配置
            ConfigureApplicationUser(modelBuilder);

            // ApplicationRole 配置
            ConfigureApplicationRole(modelBuilder);

            // ApplicationUserRole 配置
            ConfigureApplicationUserRole(modelBuilder);

            // AuditLog 配置
            ConfigureAuditLog(modelBuilder);

            // IPBlockRecord 配置
            ConfigureIPBlockRecord(modelBuilder);

            // RolePermission 配置
            ConfigureRolePermission(modelBuilder);

            // SystemModule 配置
            ConfigureSystemModule(modelBuilder);

            // Member 配置
            ConfigureMember(modelBuilder);

            // Activity 配置
            ConfigureActivity(modelBuilder);

            // ActivityRegistration 配置
            ConfigureActivityRegistration(modelBuilder);

            // MemberActivity 配置
            ConfigureMemberActivity(modelBuilder);

            // MealItem 配置
            ConfigureMealItem(modelBuilder);

            // MealOrder 配置
            ConfigureMealOrder(modelBuilder);

            // MealDailyStock 配置
            ConfigureMealDailyStock(modelBuilder);

            // MealStatistics 配置
            ConfigureMealStatistics(modelBuilder);
        }

        /// <summary>
        /// 配置 ApplicationUser 實體
        /// </summary>
        private void ConfigureApplicationUser(DbModelBuilder modelBuilder)
        {
            var userEntity = modelBuilder.Entity<ApplicationUser>();

            // 索引配置
            userEntity.HasIndex(u => u.UserName).HasName("IX_UserName").IsUnique();
            userEntity.HasIndex(u => u.Email).HasName("IX_Email");
            userEntity.HasIndex(u => u.HKID).HasName("IX_HKID");
            userEntity.HasIndex(u => u.Role).HasName("IX_Role");
            userEntity.HasIndex(u => u.IsEnabled).HasName("IX_IsEnabled");
            userEntity.HasIndex(u => u.CreatedDate).HasName("IX_CreatedDate");
            userEntity.HasIndex(u => u.LastActivityDate).HasName("IX_LastActivityDate");

            // 欄位配置
            userEntity.Property(u => u.Id).HasMaxLength(128).IsRequired();
            userEntity.Property(u => u.UserName).HasMaxLength(256).IsRequired();
            userEntity.Property(u => u.Email).HasMaxLength(256).IsRequired();
            userEntity.Property(u => u.PasswordHash).HasMaxLength(512);
            userEntity.Property(u => u.SecurityStamp).HasMaxLength(512);
            userEntity.Property(u => u.PhoneNumber).HasMaxLength(50);
            
            // 個人資料欄位
            userEntity.Property(u => u.DisplayName).HasMaxLength(100);
            userEntity.Property(u => u.HKID).HasMaxLength(20);
            userEntity.Property(u => u.Gender).HasMaxLength(10);
            userEntity.Property(u => u.Address).HasMaxLength(500);
            userEntity.Property(u => u.EmergencyContact).HasMaxLength(200);
            userEntity.Property(u => u.EmergencyPhone).HasMaxLength(50);
            
            // 角色和權限欄位
            userEntity.Property(u => u.Role).HasMaxLength(50);
            userEntity.Property(u => u.Department).HasMaxLength(100);
            userEntity.Property(u => u.Position).HasMaxLength(100);
            
            // 系統欄位
            userEntity.Property(u => u.CreatedBy).HasMaxLength(128);
            userEntity.Property(u => u.ModifiedBy).HasMaxLength(128);
            userEntity.Property(u => u.LastLoginIP).HasMaxLength(45);
            userEntity.Property(u => u.Remarks).HasMaxLength(1000);
            
            // 密碼重設欄位
            userEntity.Property(u => u.PasswordResetToken).HasMaxLength(512);
            
            // 審計欄位
            userEntity.Property(u => u.LastFailedLoginIP).HasMaxLength(45);

            // 預設值設定
            userEntity.Property(u => u.IsEnabled).HasDefaultValue(true);
            userEntity.Property(u => u.LockoutEnabled).HasDefaultValue(true);
            userEntity.Property(u => u.AccessFailedCount).HasDefaultValue(0);
            userEntity.Property(u => u.LoginCount).HasDefaultValue(0);
            userEntity.Property(u => u.FailedLoginCount).HasDefaultValue(0);
            userEntity.Property(u => u.TwoFactorEnabled).HasDefaultValue(false);
            userEntity.Property(u => u.EmailConfirmed).HasDefaultValue(false);
            userEntity.Property(u => u.PhoneNumberConfirmed).HasDefaultValue(false);
            userEntity.Property(u => u.MustChangePassword).HasDefaultValue(false);
        }

        /// <summary>
        /// 配置 ApplicationRole 實體
        /// </summary>
        private void ConfigureApplicationRole(DbModelBuilder modelBuilder)
        {
            var roleEntity = modelBuilder.Entity<ApplicationRole>();

            // 索引配置
            roleEntity.HasIndex(r => r.Name).HasName("IX_RoleName").IsUnique();
            roleEntity.HasIndex(r => r.IsActive).HasName("IX_IsActive");

            // 欄位配置
            roleEntity.Property(r => r.Id).HasMaxLength(128).IsRequired();
            roleEntity.Property(r => r.Name).HasMaxLength(256).IsRequired();
            roleEntity.Property(r => r.Description).HasMaxLength(1000);

            // 預設值設定
            roleEntity.Property(r => r.IsActive).HasDefaultValue(true);
        }

        /// <summary>
        /// 配置 ApplicationUserRole 實體
        /// </summary>
        private void ConfigureApplicationUserRole(DbModelBuilder modelBuilder)
        {
            var userRoleEntity = modelBuilder.Entity<ApplicationUserRole>();

            // 主鍵配置
            userRoleEntity.HasKey(ur => new { ur.UserId, ur.RoleId });

            // 索引配置
            userRoleEntity.HasIndex(ur => ur.UserId).HasName("IX_UserRole_UserId");
            userRoleEntity.HasIndex(ur => ur.RoleId).HasName("IX_UserRole_RoleId");
            userRoleEntity.HasIndex(ur => ur.AssignedDate).HasName("IX_AssignedDate");

            // 欄位配置
            userRoleEntity.Property(ur => ur.UserId).HasMaxLength(128).IsRequired();
            userRoleEntity.Property(ur => ur.RoleId).HasMaxLength(128).IsRequired();
            userRoleEntity.Property(ur => ur.AssignedBy).HasMaxLength(128);

            // 外鍵關係配置
            userRoleEntity.HasRequired(ur => ur.User)
                .WithMany()
                .HasForeignKey(ur => ur.UserId)
                .WillCascadeOnDelete(true);

            userRoleEntity.HasRequired(ur => ur.Role)
                .WithMany()
                .HasForeignKey(ur => ur.RoleId)
                .WillCascadeOnDelete(true);
        }

        /// <summary>
        /// 配置 AuditLog 實體
        /// </summary>
        private void ConfigureAuditLog(DbModelBuilder modelBuilder)
        {
            var auditEntity = modelBuilder.Entity<AuditLog>();

            // 主鍵配置
            auditEntity.HasKey(a => a.Id);
            auditEntity.Property(a => a.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            auditEntity.HasIndex(a => a.UserId).HasName("IX_AuditLog_UserId");
            auditEntity.HasIndex(a => a.Action).HasName("IX_AuditLog_Action");
            auditEntity.HasIndex(a => a.IPAddress).HasName("IX_AuditLog_IPAddress");
            auditEntity.HasIndex(a => a.Timestamp).HasName("IX_AuditLog_Timestamp");
            auditEntity.HasIndex(a => a.Result).HasName("IX_AuditLog_Result");
            auditEntity.HasIndex(a => a.SessionId).HasName("IX_AuditLog_SessionId");

            // 複合索引
            auditEntity.HasIndex(a => new { a.UserId, a.Timestamp }).HasName("IX_AuditLog_User_Time");
            auditEntity.HasIndex(a => new { a.Action, a.Timestamp }).HasName("IX_AuditLog_Action_Time");

            // 欄位配置
            auditEntity.Property(a => a.UserId).HasMaxLength(128);
            auditEntity.Property(a => a.UserName).HasMaxLength(256);
            auditEntity.Property(a => a.Action).HasMaxLength(50).IsRequired();
            auditEntity.Property(a => a.Entity).HasMaxLength(100);
            auditEntity.Property(a => a.EntityId).HasMaxLength(128);
            auditEntity.Property(a => a.IPAddress).HasMaxLength(45);
            auditEntity.Property(a => a.UserAgent).HasMaxLength(500);
            auditEntity.Property(a => a.Details).HasColumnType("text");
            auditEntity.Property(a => a.Result).HasMaxLength(20);
            auditEntity.Property(a => a.SessionId).HasMaxLength(100);

            // 外鍵關係配置（可選）
            auditEntity.HasOptional(a => a.User)
                .WithMany()
                .HasForeignKey(a => a.UserId)
                .WillCascadeOnDelete(false);

            // 預設值設定
            auditEntity.Property(a => a.Timestamp).HasDefaultValueSql("UTC_TIMESTAMP()");
            auditEntity.Property(a => a.Result).HasDefaultValue("SUCCESS");
        }

        /// <summary>
        /// 配置 IPBlockRecord 實體
        /// </summary>
        private void ConfigureIPBlockRecord(DbModelBuilder modelBuilder)
        {
            var blockEntity = modelBuilder.Entity<IPBlockRecord>();

            // 主鍵配置
            blockEntity.HasKey(b => b.Id);
            blockEntity.Property(b => b.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            blockEntity.HasIndex(b => b.IPAddress).HasName("IX_IPBlock_IP");
            blockEntity.HasIndex(b => b.BlockedAt).HasName("IX_IPBlock_BlockedAt");
            blockEntity.HasIndex(b => b.BlockedUntil).HasName("IX_IPBlock_BlockedUntil");
            blockEntity.HasIndex(b => b.IsActive).HasName("IX_IPBlock_IsActive");

            // 複合索引
            blockEntity.HasIndex(b => new { b.IPAddress, b.IsActive }).HasName("IX_IPBlock_IP_Active");
            blockEntity.HasIndex(b => new { b.BlockedUntil, b.IsActive }).HasName("IX_IPBlock_Until_Active");

            // 欄位配置
            blockEntity.Property(b => b.IPAddress).HasMaxLength(45).IsRequired();
            blockEntity.Property(b => b.Reason).HasMaxLength(500);
            blockEntity.Property(b => b.CreatedBy).HasMaxLength(128);
            blockEntity.Property(b => b.UnblockedBy).HasMaxLength(128);

            // 預設值設定
            blockEntity.Property(b => b.IsActive).HasDefaultValue(true);
            blockEntity.Property(b => b.BlockedAt).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            blockEntity.ToTable("IPBlockRecords");
        }

        /// <summary>
        /// 配置 RolePermission 實體
        /// </summary>
        private void ConfigureRolePermission(DbModelBuilder modelBuilder)
        {
            var permissionEntity = modelBuilder.Entity<RolePermission>();

            // 主鍵配置
            permissionEntity.HasKey(rp => rp.Id);
            permissionEntity.Property(rp => rp.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            permissionEntity.HasIndex(rp => new { rp.RoleId, rp.ModuleName }).HasName("IX_RolePermission_Role_Module").IsUnique();
            permissionEntity.HasIndex(rp => rp.RoleId).HasName("IX_RolePermission_RoleId");
            permissionEntity.HasIndex(rp => rp.ModuleName).HasName("IX_RolePermission_ModuleName");
            permissionEntity.HasIndex(rp => rp.IsActive).HasName("IX_RolePermission_IsActive");
            permissionEntity.HasIndex(rp => rp.CreatedDate).HasName("IX_RolePermission_CreatedDate");

            // 欄位配置
            permissionEntity.Property(rp => rp.RoleId).HasMaxLength(128).IsRequired();
            permissionEntity.Property(rp => rp.ModuleName).HasMaxLength(100).IsRequired();
            permissionEntity.Property(rp => rp.CreatedBy).HasMaxLength(128);
            permissionEntity.Property(rp => rp.ModifiedBy).HasMaxLength(128);
            permissionEntity.Property(rp => rp.Remarks).HasMaxLength(500);

            // 外鍵關係配置
            permissionEntity.HasRequired(rp => rp.Role)
                .WithMany()
                .HasForeignKey(rp => rp.RoleId)
                .WillCascadeOnDelete(true);

            permissionEntity.HasRequired(rp => rp.Module)
                .WithMany(m => m.RolePermissions)
                .HasForeignKey(rp => rp.ModuleName)
                .WillCascadeOnDelete(true);

            // 預設值設定
            permissionEntity.Property(rp => rp.CanRead).HasDefaultValue(false);
            permissionEntity.Property(rp => rp.CanCreate).HasDefaultValue(false);
            permissionEntity.Property(rp => rp.CanUpdate).HasDefaultValue(false);
            permissionEntity.Property(rp => rp.CanDelete).HasDefaultValue(false);
            permissionEntity.Property(rp => rp.IsActive).HasDefaultValue(true);
            permissionEntity.Property(rp => rp.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            permissionEntity.ToTable("RolePermissions");
        }

        /// <summary>
        /// 配置 SystemModule 實體
        /// </summary>
        private void ConfigureSystemModule(DbModelBuilder modelBuilder)
        {
            var moduleEntity = modelBuilder.Entity<SystemModule>();

            // 主鍵配置
            moduleEntity.HasKey(sm => sm.ModuleName);

            // 索引配置
            moduleEntity.HasIndex(sm => sm.Category).HasName("IX_SystemModule_Category");
            moduleEntity.HasIndex(sm => sm.IsActive).HasName("IX_SystemModule_IsActive");
            moduleEntity.HasIndex(sm => sm.SortOrder).HasName("IX_SystemModule_SortOrder");
            moduleEntity.HasIndex(sm => sm.CreatedDate).HasName("IX_SystemModule_CreatedDate");

            // 複合索引
            moduleEntity.HasIndex(sm => new { sm.Category, sm.SortOrder }).HasName("IX_SystemModule_Category_Sort");
            moduleEntity.HasIndex(sm => new { sm.IsActive, sm.SortOrder }).HasName("IX_SystemModule_Active_Sort");

            // 欄位配置
            moduleEntity.Property(sm => sm.ModuleName).HasMaxLength(100).IsRequired();
            moduleEntity.Property(sm => sm.DisplayName).HasMaxLength(200).IsRequired();
            moduleEntity.Property(sm => sm.Description).HasMaxLength(500);
            moduleEntity.Property(sm => sm.Category).HasMaxLength(50).IsRequired();
            moduleEntity.Property(sm => sm.Icon).HasMaxLength(50);
            moduleEntity.Property(sm => sm.ModuleUrl).HasMaxLength(200);
            moduleEntity.Property(sm => sm.CreatedBy).HasMaxLength(128);
            moduleEntity.Property(sm => sm.ModifiedBy).HasMaxLength(128);
            moduleEntity.Property(sm => sm.Remarks).HasMaxLength(1000);

            // 預設值設定
            moduleEntity.Property(sm => sm.SortOrder).HasDefaultValue(0);
            moduleEntity.Property(sm => sm.IsActive).HasDefaultValue(true);
            moduleEntity.Property(sm => sm.RequireAuthentication).HasDefaultValue(true);
            moduleEntity.Property(sm => sm.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            moduleEntity.ToTable("SystemModules");
        }

        /// <summary>
        /// 配置 Member 實體
        /// </summary>
        private void ConfigureMember(DbModelBuilder modelBuilder)
        {
            var memberEntity = modelBuilder.Entity<Member>();

            // 主鍵配置
            memberEntity.HasKey(m => m.Id);
            memberEntity.Property(m => m.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            memberEntity.HasIndex(m => m.MemberNumber).HasName("IX_Member_Number").IsUnique();
            memberEntity.HasIndex(m => m.FullName).HasName("IX_Member_FullName");
            memberEntity.HasIndex(m => m.HKID).HasName("IX_Member_HKID");
            memberEntity.HasIndex(m => m.MemberType).HasName("IX_Member_Type");
            memberEntity.HasIndex(m => m.MembershipStatus).HasName("IX_Member_Status");
            memberEntity.HasIndex(m => m.JoinDate).HasName("IX_Member_JoinDate");
            memberEntity.HasIndex(m => m.MembershipEndDate).HasName("IX_Member_EndDate");
            memberEntity.HasIndex(m => m.IsActive).HasName("IX_Member_IsActive");
            memberEntity.HasIndex(m => m.CreatedDate).HasName("IX_Member_CreatedDate");

            // 複合索引
            memberEntity.HasIndex(m => new { m.MembershipStatus, m.MembershipEndDate }).HasName("IX_Member_Status_EndDate");
            memberEntity.HasIndex(m => new { m.MemberType, m.IsActive }).HasName("IX_Member_Type_Active");

            // 欄位配置
            memberEntity.Property(m => m.MemberNumber).HasMaxLength(20).IsRequired();
            memberEntity.Property(m => m.FullName).HasMaxLength(100).IsRequired();
            memberEntity.Property(m => m.Gender).HasMaxLength(10).IsRequired();
            memberEntity.Property(m => m.HKID).HasMaxLength(20);
            memberEntity.Property(m => m.PhoneEncrypted).HasMaxLength(1000);
            memberEntity.Property(m => m.EmailEncrypted).HasMaxLength(1000);
            memberEntity.Property(m => m.AddressEncrypted).HasMaxLength(2000);
            memberEntity.Property(m => m.MemberType).HasMaxLength(20);
            memberEntity.Property(m => m.MembershipStatus).HasMaxLength(20).IsRequired();
            memberEntity.Property(m => m.PaymentMethod).HasMaxLength(20);
            memberEntity.Property(m => m.EmergencyContactName).HasMaxLength(100);
            memberEntity.Property(m => m.EmergencyContactPhone).HasMaxLength(50);
            memberEntity.Property(m => m.PhotoPath).HasMaxLength(500);
            memberEntity.Property(m => m.BarcodePath).HasMaxLength(500);
            memberEntity.Property(m => m.Remarks).HasMaxLength(1000);
            memberEntity.Property(m => m.CreatedBy).HasMaxLength(128);
            memberEntity.Property(m => m.ModifiedBy).HasMaxLength(128);

            // 預設值設定
            memberEntity.Property(m => m.IsActive).HasDefaultValue(true);
            memberEntity.Property(m => m.MembershipStatus).HasDefaultValue("Active");
            memberEntity.Property(m => m.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            memberEntity.ToTable("Members");
        }

        /// <summary>
        /// 配置 Activity 實體
        /// </summary>
        private void ConfigureActivity(DbModelBuilder modelBuilder)
        {
            var activityEntity = modelBuilder.Entity<Activity>();

            // 主鍵配置
            activityEntity.HasKey(a => a.Id);
            activityEntity.Property(a => a.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            activityEntity.HasIndex(a => a.ActivityName).HasName("IX_Activity_Name");
            activityEntity.HasIndex(a => a.ActivityDate).HasName("IX_Activity_Date");
            activityEntity.HasIndex(a => a.Status).HasName("IX_Activity_Status");
            activityEntity.HasIndex(a => a.TargetAudience).HasName("IX_Activity_Audience");
            activityEntity.HasIndex(a => a.IsActive).HasName("IX_Activity_IsActive");
            activityEntity.HasIndex(a => a.CreatedDate).HasName("IX_Activity_CreatedDate");

            // 複合索引
            activityEntity.HasIndex(a => new { a.ActivityDate, a.Status }).HasName("IX_Activity_Date_Status");
            activityEntity.HasIndex(a => new { a.Status, a.IsActive }).HasName("IX_Activity_Status_Active");

            // 欄位配置
            activityEntity.Property(a => a.ActivityName).HasMaxLength(100).IsRequired();
            activityEntity.Property(a => a.Description).HasMaxLength(1000);
            activityEntity.Property(a => a.Status).HasMaxLength(20).IsRequired();
            activityEntity.Property(a => a.TargetAudience).HasMaxLength(50);
            activityEntity.Property(a => a.Venue).HasMaxLength(200);
            activityEntity.Property(a => a.ContactPerson).HasMaxLength(100);
            activityEntity.Property(a => a.ContactPhone).HasMaxLength(50);
            activityEntity.Property(a => a.Remarks).HasMaxLength(1000);
            activityEntity.Property(a => a.CreatedBy).HasMaxLength(128).IsRequired();
            activityEntity.Property(a => a.UpdatedBy).HasMaxLength(128);

            // 預設值設定
            activityEntity.Property(a => a.IsActive).HasDefaultValue(true);
            activityEntity.Property(a => a.CurrentParticipants).HasDefaultValue(0);
            activityEntity.Property(a => a.Status).HasDefaultValue("Registration");
            activityEntity.Property(a => a.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            activityEntity.ToTable("Activities");
        }

        /// <summary>
        /// 配置 ActivityRegistration 實體
        /// </summary>
        private void ConfigureActivityRegistration(DbModelBuilder modelBuilder)
        {
            var registrationEntity = modelBuilder.Entity<ActivityRegistration>();

            // 主鍵配置
            registrationEntity.HasKey(ar => ar.Id);
            registrationEntity.Property(ar => ar.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            registrationEntity.HasIndex(ar => ar.ActivityId).HasName("IX_ActivityRegistration_ActivityId");
            registrationEntity.HasIndex(ar => ar.MemberId).HasName("IX_ActivityRegistration_MemberId");
            registrationEntity.HasIndex(ar => ar.RegistrationStatus).HasName("IX_ActivityRegistration_Status");
            registrationEntity.HasIndex(ar => ar.RegistrationDate).HasName("IX_ActivityRegistration_Date");
            registrationEntity.HasIndex(ar => ar.PaymentDate).HasName("IX_ActivityRegistration_PaymentDate");

            // 複合索引
            registrationEntity.HasIndex(ar => new { ar.ActivityId, ar.MemberId }).HasName("IX_ActivityRegistration_Activity_Member").IsUnique();
            registrationEntity.HasIndex(ar => new { ar.ActivityId, ar.RegistrationStatus }).HasName("IX_ActivityRegistration_Activity_Status");

            // 欄位配置
            registrationEntity.Property(ar => ar.RegistrationStatus).HasMaxLength(20).IsRequired();
            registrationEntity.Property(ar => ar.PaymentMethod).HasMaxLength(20);
            registrationEntity.Property(ar => ar.SpecialRequirements).HasMaxLength(500);
            registrationEntity.Property(ar => ar.Remarks).HasMaxLength(500);
            registrationEntity.Property(ar => ar.ProcessedBy).HasMaxLength(128);
            registrationEntity.Property(ar => ar.ConfirmedBy).HasMaxLength(128);

            // 外鍵關係配置
            registrationEntity.HasRequired(ar => ar.Activity)
                .WithMany(a => a.ActivityRegistrations)
                .HasForeignKey(ar => ar.ActivityId)
                .WillCascadeOnDelete(true);

            registrationEntity.HasRequired(ar => ar.Member)
                .WithMany()
                .HasForeignKey(ar => ar.MemberId)
                .WillCascadeOnDelete(false);

            // 預設值設定
            registrationEntity.Property(ar => ar.RegistrationStatus).HasDefaultValue("Registered");
            registrationEntity.Property(ar => ar.RegistrationDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            registrationEntity.ToTable("ActivityRegistrations");
        }

        /// <summary>
        /// 配置 MemberActivity 實體
        /// </summary>
        private void ConfigureMemberActivity(DbModelBuilder modelBuilder)
        {
            var memberActivityEntity = modelBuilder.Entity<MemberActivity>();

            // 主鍵配置
            memberActivityEntity.HasKey(ma => ma.Id);
            memberActivityEntity.Property(ma => ma.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            memberActivityEntity.HasIndex(ma => ma.MemberId).HasName("IX_MemberActivity_MemberId");
            memberActivityEntity.HasIndex(ma => ma.ActivityId).HasName("IX_MemberActivity_ActivityId");
            memberActivityEntity.HasIndex(ma => ma.ParticipationDate).HasName("IX_MemberActivity_ParticipationDate");
            memberActivityEntity.HasIndex(ma => ma.Status).HasName("IX_MemberActivity_Status");
            memberActivityEntity.HasIndex(ma => ma.RecordDate).HasName("IX_MemberActivity_RecordDate");

            // 複合索引
            memberActivityEntity.HasIndex(ma => new { ma.MemberId, ma.ActivityId }).HasName("IX_MemberActivity_Member_Activity").IsUnique();
            memberActivityEntity.HasIndex(ma => new { ma.ActivityId, ma.Status }).HasName("IX_MemberActivity_Activity_Status");

            // 欄位配置
            memberActivityEntity.Property(ma => ma.Status).HasMaxLength(20);
            memberActivityEntity.Property(ma => ma.Remarks).HasMaxLength(500);
            memberActivityEntity.Property(ma => ma.RecordedBy).HasMaxLength(128);

            // 外鍵關係配置
            memberActivityEntity.HasRequired(ma => ma.Member)
                .WithMany(m => m.MemberActivities)
                .HasForeignKey(ma => ma.MemberId)
                .WillCascadeOnDelete(true);

            memberActivityEntity.HasRequired(ma => ma.Activity)
                .WithMany()
                .HasForeignKey(ma => ma.ActivityId)
                .WillCascadeOnDelete(true);

            // 預設值設定
            memberActivityEntity.Property(ma => ma.RecordDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            memberActivityEntity.ToTable("MemberActivities");
        }

        /// <summary>
        /// 配置 MealItem 實體
        /// </summary>
        private void ConfigureMealItem(DbModelBuilder modelBuilder)
        {
            var mealItemEntity = modelBuilder.Entity<MealItem>();

            // 主鍵配置
            mealItemEntity.HasKey(mi => mi.Id);
            mealItemEntity.Property(mi => mi.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            mealItemEntity.HasIndex(mi => mi.ItemName).HasName("IX_MealItem_ItemName");
            mealItemEntity.HasIndex(mi => mi.Category).HasName("IX_MealItem_Category");
            mealItemEntity.HasIndex(mi => mi.IsActive).HasName("IX_MealItem_IsActive");
            mealItemEntity.HasIndex(mi => mi.CreatedDate).HasName("IX_MealItem_CreatedDate");

            // 複合索引
            mealItemEntity.HasIndex(mi => new { mi.Category, mi.IsActive }).HasName("IX_MealItem_Category_Active");

            // 欄位配置
            mealItemEntity.Property(mi => mi.ItemName).HasMaxLength(100).IsRequired();
            mealItemEntity.Property(mi => mi.Description).HasMaxLength(500);
            mealItemEntity.Property(mi => mi.AllergenInfo).HasMaxLength(200);
            mealItemEntity.Property(mi => mi.CreatedBy).HasMaxLength(128);
            mealItemEntity.Property(mi => mi.ModifiedBy).HasMaxLength(128);

            // 預設值設定
            mealItemEntity.Property(mi => mi.IsActive).HasDefaultValue(true);
            mealItemEntity.Property(mi => mi.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            mealItemEntity.ToTable("MealItems");
        }

        /// <summary>
        /// 配置 MealOrder 實體
        /// </summary>
        private void ConfigureMealOrder(DbModelBuilder modelBuilder)
        {
            var mealOrderEntity = modelBuilder.Entity<MealOrder>();

            // 主鍵配置
            mealOrderEntity.HasKey(mo => mo.Id);
            mealOrderEntity.Property(mo => mo.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            mealOrderEntity.HasIndex(mo => mo.OrderNumber).HasName("IX_MealOrder_OrderNumber").IsUnique();
            mealOrderEntity.HasIndex(mo => mo.MemberId).HasName("IX_MealOrder_MemberId");
            mealOrderEntity.HasIndex(mo => mo.MealItemId).HasName("IX_MealOrder_MealItemId");
            mealOrderEntity.HasIndex(mo => mo.MealDate).HasName("IX_MealOrder_MealDate");
            mealOrderEntity.HasIndex(mo => mo.OrderStatus).HasName("IX_MealOrder_OrderStatus");
            mealOrderEntity.HasIndex(mo => mo.PaymentStatus).HasName("IX_MealOrder_PaymentStatus");
            mealOrderEntity.HasIndex(mo => mo.OrderDate).HasName("IX_MealOrder_OrderDate");

            // 複合索引
            mealOrderEntity.HasIndex(mo => new { mo.MealDate, mo.OrderStatus }).HasName("IX_MealOrder_Date_Status");
            mealOrderEntity.HasIndex(mo => new { mo.MemberId, mo.MealDate }).HasName("IX_MealOrder_Member_Date");
            mealOrderEntity.HasIndex(mo => new { mo.MealItemId, mo.MealDate }).HasName("IX_MealOrder_Item_Date");

            // 欄位配置
            mealOrderEntity.Property(mo => mo.OrderNumber).HasMaxLength(30).IsRequired();
            mealOrderEntity.Property(mo => mo.SpecialRequests).HasMaxLength(500);
            mealOrderEntity.Property(mo => mo.Remarks).HasMaxLength(500);
            mealOrderEntity.Property(mo => mo.OrderedBy).HasMaxLength(128);
            mealOrderEntity.Property(mo => mo.UpdatedBy).HasMaxLength(128);

            // 外鍵關係配置
            mealOrderEntity.HasRequired(mo => mo.Member)
                .WithMany()
                .HasForeignKey(mo => mo.MemberId)
                .WillCascadeOnDelete(false);

            mealOrderEntity.HasRequired(mo => mo.MealItem)
                .WithMany()
                .HasForeignKey(mo => mo.MealItemId)
                .WillCascadeOnDelete(false);

            // 預設值設定
            mealOrderEntity.Property(mo => mo.OrderStatus).HasDefaultValue(OrderStatus.Ordered);
            mealOrderEntity.Property(mo => mo.PaymentStatus).HasDefaultValue(PaymentStatus.Pending);
            mealOrderEntity.Property(mo => mo.PaymentMethod).HasDefaultValue(PaymentMethod.Cash);
            mealOrderEntity.Property(mo => mo.CreatedAt).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            mealOrderEntity.ToTable("MealOrders");
        }

        /// <summary>
        /// 配置 MealDailyStock 實體
        /// </summary>
        private void ConfigureMealDailyStock(DbModelBuilder modelBuilder)
        {
            var stockEntity = modelBuilder.Entity<MealDailyStock>();

            // 主鍵配置
            stockEntity.HasKey(s => s.Id);
            stockEntity.Property(s => s.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            stockEntity.HasIndex(s => s.MealItemId).HasName("IX_MealDailyStock_MealItemId");
            stockEntity.HasIndex(s => s.StockDate).HasName("IX_MealDailyStock_StockDate");
            stockEntity.HasIndex(s => s.CreatedDate).HasName("IX_MealDailyStock_CreatedDate");

            // 複合索引
            stockEntity.HasIndex(s => new { s.MealItemId, s.StockDate }).HasName("IX_MealDailyStock_Item_Date").IsUnique();

            // 欄位配置
            stockEntity.Property(s => s.CreatedBy).HasMaxLength(128);
            stockEntity.Property(s => s.ModifiedBy).HasMaxLength(128);

            // 外鍵關係配置
            stockEntity.HasRequired(s => s.MealItem)
                .WithMany()
                .HasForeignKey(s => s.MealItemId)
                .WillCascadeOnDelete(true);

            // 預設值設定
            stockEntity.Property(s => s.InitialQuantity).HasDefaultValue(0);
            stockEntity.Property(s => s.OrderedQuantity).HasDefaultValue(0);
            stockEntity.Property(s => s.AvailableQuantity).HasDefaultValue(0);
            stockEntity.Property(s => s.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            stockEntity.ToTable("MealDailyStocks");
        }

        /// <summary>
        /// 配置 MealStatistics 實體
        /// </summary>
        private void ConfigureMealStatistics(DbModelBuilder modelBuilder)
        {
            var statsEntity = modelBuilder.Entity<MealStatistics>();

            // 主鍵配置
            statsEntity.HasKey(ms => ms.Id);
            statsEntity.Property(ms => ms.Id).HasDatabaseGeneratedOption(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity);

            // 索引配置
            statsEntity.HasIndex(ms => ms.MealItemId).HasName("IX_MealStatistics_MealItemId");
            statsEntity.HasIndex(ms => ms.StatisticsDate).HasName("IX_MealStatistics_StatisticsDate");
            statsEntity.HasIndex(ms => ms.CreatedDate).HasName("IX_MealStatistics_CreatedDate");

            // 複合索引
            statsEntity.HasIndex(ms => new { ms.MealItemId, ms.StatisticsDate }).HasName("IX_MealStatistics_Item_Date").IsUnique();

            // 欄位配置
            statsEntity.Property(ms => ms.ItemName).HasMaxLength(100).IsRequired();
            statsEntity.Property(ms => ms.Category).HasMaxLength(20).IsRequired();
            statsEntity.Property(ms => ms.CreatedBy).HasMaxLength(128);

            // 外鍵關係配置
            statsEntity.HasRequired(ms => ms.MealItem)
                .WithMany()
                .HasForeignKey(ms => ms.MealItemId)
                .WillCascadeOnDelete(true);

            // 預設值設定
            statsEntity.Property(ms => ms.OrderedQuantity).HasDefaultValue(0);
            statsEntity.Property(ms => ms.AvailableQuantity).HasDefaultValue(0);
            statsEntity.Property(ms => ms.Revenue).HasDefaultValue(0m);
            statsEntity.Property(ms => ms.CreatedDate).HasDefaultValueSql("UTC_TIMESTAMP()");

            // 表格名稱
            statsEntity.ToTable("MealStatistics");
        }

        #endregion

        #region 靜態工廠方法

        /// <summary>
        /// 建立新的資料庫上下文實例
        /// </summary>
        public static ApplicationDbContext Create()
        {
            return new ApplicationDbContext();
        }

        #endregion
    }

    /// <summary>
    /// 資料庫初始化器 - 用於建立初始資料
    /// </summary>
    public class ApplicationDbInitializer : CreateDatabaseIfNotExists<ApplicationDbContext>
    {
        protected override void Seed(ApplicationDbContext context)
        {
            try
            {
                // 建立預設角色
                CreateDefaultRoles(context);

                // 建立預設管理員帳戶
                CreateDefaultAdministrator(context);

                // 建立預設系統模組
                CreateDefaultModules(context);

                // 建立預設權限配置
                CreateDefaultPermissions(context);

                context.SaveChanges();
            }
            catch (Exception ex)
            {
                // 記錄初始化錯誤
                System.Diagnostics.Trace.WriteLine($"資料庫初始化錯誤: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 建立預設角色
        /// </summary>
        private void CreateDefaultRoles(ApplicationDbContext context)
        {
            var roles = new[]
            {
                new ApplicationRole("Administrator") { Description = "系統管理員，擁有完整系統權限" },
                new ApplicationRole("StaffMember") { Description = "職員，負責日常行政工作" },
                new ApplicationRole("Teacher") { Description = "導師，負責教學和會員輔導" },
                new ApplicationRole("Volunteer") { Description = "義工，參與中心活動協助" },
                new ApplicationRole("Member") { Description = "會員，享用中心服務" },
                new ApplicationRole("Guest") { Description = "訪客，有限的系統存取權限" }
            };

            foreach (var role in roles)
            {
                if (!context.ApplicationRoles.Any(r => r.Name == role.Name))
                {
                    context.ApplicationRoles.Add(role);
                }
            }
        }

        /// <summary>
        /// 建立預設管理員帳戶
        /// </summary>
        private void CreateDefaultAdministrator(ApplicationDbContext context)
        {
            const string defaultAdminEmail = "<EMAIL>";
            
            // 檢查是否已存在管理員帳戶
            if (!context.Users.Any(u => u.Email == defaultAdminEmail))
            {
                var adminUser = new ApplicationUser(defaultAdminEmail)
                {
                    Email = defaultAdminEmail,
                    DisplayName = "系統管理員",
                    Role = "Administrator",
                    IsEnabled = true,
                    EmailConfirmed = true,
                    CreatedBy = "SYSTEM",
                    CreatedDate = DateTime.UtcNow,
                    LastActivityDate = DateTime.UtcNow
                };

                context.Users.Add(adminUser);

                // 建立審計日誌
                var auditLog = new AuditLog
                {
                    Action = "SYSTEM_INIT",
                    UserId = adminUser.Id,
                    UserName = adminUser.UserName,
                    Details = "系統初始化 - 建立預設管理員帳戶",
                    IPAddress = "127.0.0.1",
                    Result = "SUCCESS",
                    Timestamp = DateTime.UtcNow
                };

                context.AuditLogs.Add(auditLog);
            }
        }

        /// <summary>
        /// 建立預設系統模組
        /// </summary>
        private void CreateDefaultModules(ApplicationDbContext context)
        {
            var defaultModules = DefaultModules.GetDefaultModules();
            
            foreach (var module in defaultModules)
            {
                if (!context.SystemModules.Any(m => m.ModuleName == module.ModuleName))
                {
                    module.CreatedBy = "SYSTEM";
                    context.SystemModules.Add(module);
                }
            }
        }

        /// <summary>
        /// 建立預設權限配置
        /// </summary>
        private void CreateDefaultPermissions(ApplicationDbContext context)
        {
            // 管理員擁有所有權限
            var adminRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == "Administrator");
            if (adminRole != null)
            {
                var modules = context.SystemModules.Where(m => m.IsActive).ToList();
                foreach (var module in modules)
                {
                    if (!context.RolePermissions.Any(rp => rp.RoleId == adminRole.Id && rp.ModuleName == module.ModuleName))
                    {
                        var permission = new RolePermission
                        {
                            RoleId = adminRole.Id,
                            ModuleName = module.ModuleName,
                            CanRead = true,
                            CanCreate = true,
                            CanUpdate = true,
                            CanDelete = true,
                            CreatedBy = "SYSTEM",
                            CreatedDate = DateTime.UtcNow,
                            IsActive = true
                        };
                        context.RolePermissions.Add(permission);
                    }
                }
            }

            // 職員權限配置
            var staffRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == "StaffMember");
            if (staffRole != null)
            {
                var staffModules = new Dictionary<string, (bool read, bool create, bool update, bool delete)>
                {
                    { "Dashboard", (true, false, false, false) },
                    { "Members", (true, true, true, false) },
                    { "MemberRegistration", (true, true, true, false) },
                    { "MemberProfiles", (true, true, true, false) },
                    { "Activities", (true, true, true, false) },
                    { "ActivityScheduling", (true, true, true, false) },
                    { "ActivityRegistration", (true, true, true, false) },
                    { "MealOrdering", (true, true, true, false) },
                    { "MealManagement", (true, true, true, true) },
                    { "Reports", (true, false, false, false) },
                    { "MemberReports", (true, false, false, false) },
                    { "ActivityReports", (true, false, false, false) }
                };

                foreach (var moduleConfig in staffModules)
                {
                    if (context.SystemModules.Any(m => m.ModuleName == moduleConfig.Key && m.IsActive) &&
                        !context.RolePermissions.Any(rp => rp.RoleId == staffRole.Id && rp.ModuleName == moduleConfig.Key))
                    {
                        var permission = new RolePermission
                        {
                            RoleId = staffRole.Id,
                            ModuleName = moduleConfig.Key,
                            CanRead = moduleConfig.Value.read,
                            CanCreate = moduleConfig.Value.create,
                            CanUpdate = moduleConfig.Value.update,
                            CanDelete = moduleConfig.Value.delete,
                            CreatedBy = "SYSTEM",
                            CreatedDate = DateTime.UtcNow,
                            IsActive = true
                        };
                        context.RolePermissions.Add(permission);
                    }
                }
            }

            // 導師權限配置
            var teacherRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == "Teacher");
            if (teacherRole != null)
            {
                var teacherModules = new Dictionary<string, (bool read, bool create, bool update, bool delete)>
                {
                    { "Dashboard", (true, false, false, false) },
                    { "Members", (true, false, false, false) },
                    { "MemberProfiles", (true, false, true, false) },
                    { "Activities", (true, true, true, false) },
                    { "ActivityScheduling", (true, true, true, false) },
                    { "ActivityRegistration", (true, true, true, false) },
                    { "MealOrdering", (true, true, false, false) },
                    { "MealManagement", (true, false, false, false) },
                    { "ActivityReports", (true, false, false, false) }
                };

                foreach (var moduleConfig in teacherModules)
                {
                    if (context.SystemModules.Any(m => m.ModuleName == moduleConfig.Key && m.IsActive) &&
                        !context.RolePermissions.Any(rp => rp.RoleId == teacherRole.Id && rp.ModuleName == moduleConfig.Key))
                    {
                        var permission = new RolePermission
                        {
                            RoleId = teacherRole.Id,
                            ModuleName = moduleConfig.Key,
                            CanRead = moduleConfig.Value.read,
                            CanCreate = moduleConfig.Value.create,
                            CanUpdate = moduleConfig.Value.update,
                            CanDelete = moduleConfig.Value.delete,
                            CreatedBy = "SYSTEM",
                            CreatedDate = DateTime.UtcNow,
                            IsActive = true
                        };
                        context.RolePermissions.Add(permission);
                    }
                }
            }

            // 義工和會員只有基本讀取權限
            var volunteerRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == "Volunteer");
            var memberRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == "Member");
            
            var basicRoles = new[] { volunteerRole, memberRole }.Where(r => r != null);
            var basicModules = new[] { "Dashboard", "Activities", "MemberProfiles", "MealOrdering" };

            foreach (var role in basicRoles)
            {
                foreach (var moduleName in basicModules)
                {
                    if (context.SystemModules.Any(m => m.ModuleName == moduleName && m.IsActive) &&
                        !context.RolePermissions.Any(rp => rp.RoleId == role.Id && rp.ModuleName == moduleName))
                    {
                        var permission = new RolePermission
                        {
                            RoleId = role.Id,
                            ModuleName = moduleName,
                            CanRead = true,
                            CanCreate = false,
                            CanUpdate = (moduleName == "MemberProfiles" && role.Name == "Member"), // 會員可編輯自己的檔案
                            CanDelete = false,
                            CreatedBy = "SYSTEM",
                            CreatedDate = DateTime.UtcNow,
                            IsActive = true
                        };
                        context.RolePermissions.Add(permission);
                    }
                }
            }
        }
    }
}