using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Configuration;
using Google.Cloud.Firestore;
using System.Linq;

namespace CWDECC_3S.Data
{
    /// <summary>
    /// Firebase Firestore 服務類別
    /// </summary>
    public class FirebaseService
    {
        private FirestoreDb _firestoreDb;
        private readonly string _projectId;

        public FirebaseService()
        {
            _projectId = ConfigurationManager.AppSettings["FirebaseProjectId"];
            InitializeFirestore();
        }

        public FirebaseService(string projectId)
        {
            _projectId = projectId;
            InitializeFirestore();
        }

        private void InitializeFirestore()
        {
            try
            {
                // Initialize Firestore with project ID
                _firestoreDb = FirestoreDb.Create(_projectId);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"初始化 Firestore 時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 測試 Firebase 連線
        /// </summary>
        /// <returns>連線成功返回 true</returns>
        public bool TestConnection()
        {
            try
            {
                if (_firestoreDb == null)
                    return false;

                // Try to get a test collection to verify connection
                var testCollection = _firestoreDb.Collection("test");
                var query = testCollection.Limit(1);
                var snapshot = query.GetSnapshotAsync().Result;
                
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 新增文件到指定集合
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件ID</param>
        /// <param name="data">要新增的資料</param>
        /// <returns>操作結果</returns>
        public async Task<bool> AddAsync<T>(string collectionName, string documentId, T data)
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName) || string.IsNullOrEmpty(documentId) || data == null)
                    return false;

                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);
                
                await document.SetAsync(data);
                return true;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"新增文件時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根據ID取得文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件ID</param>
        /// <returns>文件資料</returns>
        public async Task<T> GetByIdAsync<T>(string collectionName, string documentId) where T : class
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName) || string.IsNullOrEmpty(documentId))
                    return null;

                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);
                var snapshot = await document.GetSnapshotAsync();

                if (snapshot.Exists)
                {
                    return snapshot.ConvertTo<T>();
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"取得文件時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得集合中的所有文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <returns>文件列表</returns>
        public async Task<IEnumerable<T>> GetAllAsync<T>(string collectionName) where T : class
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName))
                    return new List<T>();

                var collection = _firestoreDb.Collection(collectionName);
                var snapshot = await collection.GetSnapshotAsync();

                return snapshot.Documents.Select(doc => doc.ConvertTo<T>()).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"取得集合資料時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件ID</param>
        /// <param name="data">要更新的資料</param>
        /// <returns>操作結果</returns>
        public async Task<bool> UpdateAsync<T>(string collectionName, string documentId, T data)
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName) || string.IsNullOrEmpty(documentId) || data == null)
                    return false;

                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);

                // Check if document exists
                var snapshot = await document.GetSnapshotAsync();
                if (!snapshot.Exists)
                    return false;

                await document.SetAsync(data, SetOptions.Overwrite);
                return true;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"更新文件時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 刪除文件
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件ID</param>
        /// <returns>操作結果</returns>
        public async Task<bool> DeleteAsync(string collectionName, string documentId)
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName) || string.IsNullOrEmpty(documentId))
                    return false;

                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);

                // Check if document exists
                var snapshot = await document.GetSnapshotAsync();
                if (!snapshot.Exists)
                    return false;

                await document.DeleteAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"刪除文件時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 查詢文件
        /// </summary>
        /// <typeparam name="T">文件類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="fieldName">欄位名稱</param>
        /// <param name="fieldValue">欄位值</param>
        /// <returns>符合條件的文件列表</returns>
        public async Task<IEnumerable<T>> QueryAsync<T>(string collectionName, string fieldName, object fieldValue) where T : class
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName) || string.IsNullOrEmpty(fieldName))
                    return new List<T>();

                var collection = _firestoreDb.Collection(collectionName);
                var query = collection.WhereEqualTo(fieldName, fieldValue);
                var snapshot = await query.GetSnapshotAsync();

                return snapshot.Documents.Select(doc => doc.ConvertTo<T>()).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"查詢文件時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批次操作
        /// </summary>
        /// <param name="operations">操作列表</param>
        /// <returns>操作結果</returns>
        public async Task<bool> BatchOperationAsync(IEnumerable<Func<WriteBatch, WriteBatch>> operations)
        {
            try
            {
                if (operations == null || !operations.Any())
                    return false;

                var batch = _firestoreDb.StartBatch();

                foreach (var operation in operations)
                {
                    batch = operation(batch);
                }

                await batch.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"批次操作時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得集合文件數量
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        /// <returns>文件數量</returns>
        public async Task<int> GetDocumentCountAsync(string collectionName)
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName))
                    return 0;

                var collection = _firestoreDb.Collection(collectionName);
                var snapshot = await collection.GetSnapshotAsync();

                return snapshot.Count;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"取得文件數量時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 檢查文件是否存在
        /// </summary>
        /// <param name="collectionName">集合名稱</param>
        /// <param name="documentId">文件ID</param>
        /// <returns>文件是否存在</returns>
        public async Task<bool> DocumentExistsAsync(string collectionName, string documentId)
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName) || string.IsNullOrEmpty(documentId))
                    return false;

                var collection = _firestoreDb.Collection(collectionName);
                var document = collection.Document(documentId);
                var snapshot = await document.GetSnapshotAsync();

                return snapshot.Exists;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"檢查文件是否存在時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        public void Dispose()
        {
            _firestoreDb?.Dispose();
        }
    }
}