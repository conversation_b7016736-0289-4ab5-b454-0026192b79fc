using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Globalization;

namespace CWDECC_3S.Data
{
    /// <summary>
    /// 驗證工具類別
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// 驗證電子郵件格式
        /// </summary>
        /// <param name="email">電子郵件地址</param>
        /// <returns>是否有效</returns>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // Use EmailAddressAttribute for validation
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 驗證香港手機號碼格式
        /// </summary>
        /// <param name="phoneNumber">手機號碼</param>
        /// <returns>是否有效</returns>
        public static bool IsValidHKPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove all spaces, dashes, and parentheses
            var cleanedNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace("+", "");

            // Hong Kong phone number patterns
            // Mobile: 5xxx-xxxx, 6xxx-xxxx, 7xxx-xxxx, 8xxx-xxxx, 9xxx-xxxx
            // Landline: 2xxx-xxxx, 3xxx-xxxx
            var hkMobilePattern = @"^(852)?(5|6|7|8|9)\d{7}$";
            var hkLandlinePattern = @"^(852)?(2|3)\d{7}$";

            return Regex.IsMatch(cleanedNumber, hkMobilePattern) || Regex.IsMatch(cleanedNumber, hkLandlinePattern);
        }

        /// <summary>
        /// 驗證香港身份證號碼格式
        /// </summary>
        /// <param name="hkid">香港身份證號碼</param>
        /// <returns>是否有效</returns>
        public static bool IsValidHKID(string hkid)
        {
            if (string.IsNullOrWhiteSpace(hkid))
                return false;

            // Remove spaces and convert to uppercase
            var cleanedHKID = hkid.Replace(" ", "").Replace("(", "").Replace(")", "").ToUpper();

            // HKID pattern: 1-2 letters + 6 digits + 1 check digit (digit or A)
            var hkidPattern = @"^[A-Z]{1,2}\d{6}[0-9A]$";

            if (!Regex.IsMatch(cleanedHKID, hkidPattern))
                return false;

            // Validate check digit
            return ValidateHKIDCheckDigit(cleanedHKID);
        }

        /// <summary>
        /// 驗證香港身份證校驗位
        /// </summary>
        /// <param name="hkid">香港身份證號碼</param>
        /// <returns>校驗位是否正確</returns>
        private static bool ValidateHKIDCheckDigit(string hkid)
        {
            try
            {
                var chars = hkid.ToCharArray();
                var checkDigit = chars[chars.Length - 1];
                var weights = new int[] { 8, 7, 6, 5, 4, 3, 2 };
                var sum = 0;

                // Calculate weighted sum
                for (int i = 0; i < chars.Length - 1; i++)
                {
                    if (char.IsLetter(chars[i]))
                    {
                        // Convert letter to number (A=10, B=11, ..., Z=35)
                        sum += (chars[i] - 'A' + 10) * weights[i];
                    }
                    else
                    {
                        sum += (chars[i] - '0') * weights[i];
                    }
                }

                var remainder = sum % 11;
                var expectedCheckDigit = remainder == 0 ? '0' : 
                                       remainder == 1 ? 'A' : 
                                       (char)('0' + (11 - remainder));

                return checkDigit == expectedCheckDigit;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 驗證密碼強度
        /// </summary>
        /// <param name="password">密碼</param>
        /// <param name="minLength">最小長度</param>
        /// <param name="requireUppercase">是否需要大寫字母</param>
        /// <param name="requireLowercase">是否需要小寫字母</param>
        /// <param name="requireDigits">是否需要數字</param>
        /// <param name="requireSpecialChars">是否需要特殊字符</param>
        /// <returns>密碼強度驗證結果</returns>
        public static PasswordValidationResult ValidatePasswordStrength(string password, 
            int minLength = 8, 
            bool requireUppercase = true, 
            bool requireLowercase = true, 
            bool requireDigits = true, 
            bool requireSpecialChars = true)
        {
            var result = new PasswordValidationResult { IsValid = true, ErrorMessages = new System.Collections.Generic.List<string>() };

            if (string.IsNullOrEmpty(password))
            {
                result.IsValid = false;
                result.ErrorMessages.Add("密碼不能為空");
                return result;
            }

            if (password.Length < minLength)
            {
                result.IsValid = false;
                result.ErrorMessages.Add($"密碼長度至少需要 {minLength} 個字符");
            }

            if (requireUppercase && !Regex.IsMatch(password, @"[A-Z]"))
            {
                result.IsValid = false;
                result.ErrorMessages.Add("密碼必須包含至少一個大寫字母");
            }

            if (requireLowercase && !Regex.IsMatch(password, @"[a-z]"))
            {
                result.IsValid = false;
                result.ErrorMessages.Add("密碼必須包含至少一個小寫字母");
            }

            if (requireDigits && !Regex.IsMatch(password, @"\d"))
            {
                result.IsValid = false;
                result.ErrorMessages.Add("密碼必須包含至少一個數字");
            }

            if (requireSpecialChars && !Regex.IsMatch(password, @"[!@#$%^&*()_+=\[{\]};:<>|./?,-]"))
            {
                result.IsValid = false;
                result.ErrorMessages.Add("密碼必須包含至少一個特殊字符");
            }

            return result;
        }

        /// <summary>
        /// 驗證日期格式
        /// </summary>
        /// <param name="dateString">日期字串</param>
        /// <param name="format">日期格式</param>
        /// <returns>是否有效的日期</returns>
        public static bool IsValidDate(string dateString, string format = "yyyy-MM-dd")
        {
            if (string.IsNullOrWhiteSpace(dateString))
                return false;

            return DateTime.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out _);
        }

        /// <summary>
        /// 驗證年齡範圍
        /// </summary>
        /// <param name="dateOfBirth">出生日期</param>
        /// <param name="minAge">最小年齡</param>
        /// <param name="maxAge">最大年齡</param>
        /// <returns>年齡是否在有效範圍內</returns>
        public static bool IsValidAge(DateTime? dateOfBirth, int minAge = 0, int maxAge = 120)
        {
            if (!dateOfBirth.HasValue)
                return false;

            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Value.Year;

            if (dateOfBirth.Value.Date > today.AddYears(-age))
                age--;

            return age >= minAge && age <= maxAge;
        }

        /// <summary>
        /// 驗證字串長度範圍
        /// </summary>
        /// <param name="text">文字</param>
        /// <param name="minLength">最小長度</param>
        /// <param name="maxLength">最大長度</param>
        /// <returns>長度是否在有效範圍內</returns>
        public static bool IsValidLength(string text, int minLength = 0, int maxLength = int.MaxValue)
        {
            if (text == null)
                return minLength == 0;

            return text.Length >= minLength && text.Length <= maxLength;
        }

        /// <summary>
        /// 驗證是否為數字
        /// </summary>
        /// <param name="text">文字</param>
        /// <param name="allowDecimals">是否允許小數</param>
        /// <returns>是否為有效數字</returns>
        public static bool IsNumeric(string text, bool allowDecimals = true)
        {
            if (string.IsNullOrWhiteSpace(text))
                return false;

            if (allowDecimals)
                return decimal.TryParse(text, out _);
            else
                return int.TryParse(text, out _);
        }

        /// <summary>
        /// 驗證 URL 格式
        /// </summary>
        /// <param name="url">URL</param>
        /// <returns>是否有效的 URL</returns>
        public static bool IsValidUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return false;

            return Uri.TryCreate(url, UriKind.Absolute, out Uri result) && 
                   (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }

        /// <summary>
        /// 清理和驗證 HTML 輸入（基本 XSS 防護）
        /// </summary>
        /// <param name="input">輸入字串</param>
        /// <returns>清理後的字串</returns>
        public static string SanitizeHtmlInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Remove potentially dangerous HTML tags and scripts
            var cleaned = Regex.Replace(input, @"<script[^>]*>.*?</script>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
            cleaned = Regex.Replace(cleaned, @"<[^>]+>", "");
            cleaned = System.Web.HttpUtility.HtmlEncode(cleaned);

            return cleaned;
        }

        /// <summary>
        /// 驗證中文字符
        /// </summary>
        /// <param name="text">文字</param>
        /// <returns>是否包含中文字符</returns>
        public static bool ContainsChinese(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            return Regex.IsMatch(text, @"[\u4e00-\u9fff]");
        }

        /// <summary>
        /// 驗證是否只包含中文字符和常見標點符號
        /// </summary>
        /// <param name="text">文字</param>
        /// <returns>是否只包含中文字符</returns>
        public static bool IsChineseOnly(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            return Regex.IsMatch(text, @"^[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\s]+$");
        }
    }

    /// <summary>
    /// 密碼驗證結果
    /// </summary>
    public class PasswordValidationResult
    {
        public bool IsValid { get; set; }
        public System.Collections.Generic.List<string> ErrorMessages { get; set; } = new System.Collections.Generic.List<string>();
    }
}