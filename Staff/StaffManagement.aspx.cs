using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Microsoft.AspNet.Identity;
using CWDECC_3S.Models;
using CWDECC_3S.Services;

namespace CWDECC_3S.Staff
{
    /// <summary>
    /// 職員管理頁面 - 管理職員與導師資料、合約與服務記錄
    /// </summary>
    public partial class StaffManagement : System.Web.UI.Page
    {
        private StaffService _staffService;
        private PermissionService _permissionService;
        private string _currentUserId;

        protected void Page_Init(object sender, EventArgs e)
        {
            _staffService = new StaffService();
            _permissionService = new PermissionService();
            _currentUserId = User.Identity.GetUserId();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限 - 只有主管和 HR 角色可以操作
                if (!_permissionService.HasRolePermission(_currentUserId, "Administrator") &&
                    !_permissionService.HasRolePermission(_currentUserId, "StaffMember"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    InitializeControls();
                    LoadStatistics();
                    LoadStaffData();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"頁面載入錯誤: {ex.Message}", "danger");
            }
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            RegisterScripts();
        }

        protected void Page_Unload(object sender, EventArgs e)
        {
            _staffService?.Dispose();
            _permissionService?.Dispose();
        }

        #region 初始化

        /// <summary>
        /// 初始化控制項
        /// </summary>
        private void InitializeControls()
        {
            // 初始化下拉選單
            InitializeDropDownLists();

            // 設定分頁大小
            gvStaff.PageSize = 20;

            // 設定權限控制
            SetPermissionControls();
        }

        /// <summary>
        /// 初始化下拉選單
        /// </summary>
        private void InitializeDropDownLists()
        {
            // 職員類型
            var staffTypes = StaffConstants.StaffTypes.GetAll();
            ddlStaffType.Items.Add(new ListItem("全部", ""));
            ddlStaffTypeModal.Items.Add(new ListItem("請選擇", ""));
            foreach (var type in staffTypes)
            {
                ddlStaffType.Items.Add(new ListItem(type.Value, type.Key));
                ddlStaffTypeModal.Items.Add(new ListItem(type.Value, type.Key));
            }

            // 聘用類型
            var employmentTypes = StaffConstants.EmploymentTypes.GetAll();
            ddlEmploymentType.Items.Add(new ListItem("全部", ""));
            ddlEmploymentTypeModal.Items.Add(new ListItem("請選擇", ""));
            foreach (var type in employmentTypes)
            {
                ddlEmploymentType.Items.Add(new ListItem(type.Value, type.Key));
                ddlEmploymentTypeModal.Items.Add(new ListItem(type.Value, type.Key));
            }

            // 在職狀態
            var employmentStatuses = StaffConstants.EmploymentStatuses.GetAll();
            ddlEmploymentStatus.Items.Add(new ListItem("全部", ""));
            ddlEmploymentStatusModal.Items.Add(new ListItem("在職", "Active"));
            foreach (var status in employmentStatuses)
            {
                ddlEmploymentStatus.Items.Add(new ListItem(status.Value, status.Key));
                ddlEmploymentStatusModal.Items.Add(new ListItem(status.Value, status.Key));
            }

            // 性別
            var genders = StaffConstants.Genders.GetAll();
            ddlGender.Items.Add(new ListItem("請選擇", ""));
            foreach (var gender in genders)
            {
                ddlGender.Items.Add(new ListItem(gender.Value, gender.Key));
            }

            // 部門 - 從資料庫載入
            LoadDepartments();
        }

        /// <summary>
        /// 載入部門列表
        /// </summary>
        private async void LoadDepartments()
        {
            try
            {
                var departments = await _staffService.GetDepartmentsAsync();
                ddlDepartment.Items.Add(new ListItem("全部", ""));
                foreach (var dept in departments)
                {
                    ddlDepartment.Items.Add(new ListItem(dept, dept));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入部門失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 設定權限控制
        /// </summary>
        private void SetPermissionControls()
        {
            // 檢查是否有新增權限
            bool canCreate = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                           _permissionService.HasRolePermission(_currentUserId, "StaffMember");
            btnAddStaff.Visible = canCreate;

            // 檢查是否有刪除權限
            bool canDelete = _permissionService.HasRolePermission(_currentUserId, "Administrator");

            // 檢查是否有更新合約權限
            bool canUpdateContract = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                                   _permissionService.HasRolePermission(_currentUserId, "StaffMember");
            btnUpdateContracts.Visible = canUpdateContract;
        }

        #endregion

        #region 資料載入

        /// <summary>
        /// 載入統計資訊
        /// </summary>
        private async void LoadStatistics()
        {
            try
            {
                var stats = await _staffService.GetStaffStatisticsAsync();

                lblTotalStaff.Text = stats.TotalStaff.ToString();
                lblActiveStaff.Text = stats.ActiveStaff.ToString();
                lblTeachers.Text = stats.Teachers.ToString();
                lblGeneralStaff.Text = stats.GeneralStaff.ToString();
                lblExpiringSoon.Text = stats.ContractsExpiringSoon.ToString();
                lblExpired.Text = stats.ExpiredContracts.ToString();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入統計資訊失敗: {ex.Message}", "warning");
            }
        }

        /// <summary>
        /// 載入職員資料
        /// </summary>
        private async void LoadStaffData()
        {
            try
            {
                var criteria = BuildSearchCriteria();
                var (staffList, totalCount) = await _staffService.SearchStaffAsync(criteria);

                ViewState["TotalRecords"] = totalCount;
                lblRecordCount.Text = totalCount.ToString();

                gvStaff.DataSource = staffList;
                gvStaff.DataBind();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入職員資料失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 建立搜尋條件
        /// </summary>
        /// <returns>搜尋條件物件</returns>
        private StaffSearchCriteria BuildSearchCriteria()
        {
            return new StaffSearchCriteria
            {
                Keyword = txtKeyword.Text.Trim(),
                StaffType = ddlStaffType.SelectedValue,
                EmploymentType = ddlEmploymentType.SelectedValue,
                EmploymentStatus = ddlEmploymentStatus.SelectedValue,
                Department = ddlDepartment.SelectedValue,
                IsActive = true, // 預設只顯示啟用的職員
                PageNumber = gvStaff.PageIndex + 1,
                PageSize = gvStaff.PageSize,
                SortField = ViewState["SortField"]?.ToString() ?? "FullName",
                SortDirection = ViewState["SortDirection"]?.ToString() ?? "ASC"
            };
        }

        #endregion

        #region 按鈕事件

        protected void btnAddStaff_Click(object sender, EventArgs e)
        {
            try
            {
                // 清除表單
                ClearStaffModal();
                lblModalTitle.Text = "新增職員";
                hdnStaffId.Value = "";

                // 設定預設值
                txtHireDate.Text = DateTime.Today.ToString("yyyy-MM-dd");
                ddlEmploymentStatusModal.SelectedValue = "Active";
                chkIsActive.Checked = true;

                ScriptManager.RegisterStartupScript(this, GetType(), "showStaffModal", "showStaffModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"開啟新增職員視窗失敗: {ex.Message}", "danger");
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            gvStaff.PageIndex = 0; // 重置到第一頁
            LoadStaffData();
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            // 清除搜尋條件
            txtKeyword.Text = "";
            ddlStaffType.SelectedIndex = 0;
            ddlEmploymentType.SelectedIndex = 0;
            ddlEmploymentStatus.SelectedIndex = 0;
            ddlDepartment.SelectedIndex = 0;

            gvStaff.PageIndex = 0;
            LoadStatistics();
            LoadStaffData();

            ShowMessage("頁面已重新整理", "success");
        }

        protected async void btnUpdateContracts_Click(object sender, EventArgs e)
        {
            try
            {
                var updatedCount = await _staffService.UpdateAllContractStatusesAsync(_currentUserId);
                
                if (updatedCount > 0)
                {
                    ShowMessage($"已更新 {updatedCount} 個職員的合約狀態", "success");
                    LoadStatistics();
                    LoadStaffData();
                }
                else
                {
                    ShowMessage("沒有需要更新的合約狀態", "info");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"更新合約狀態失敗: {ex.Message}", "danger");
            }
        }

        protected async void btnSaveStaff_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                    return;

                var staff = BuildStaffFromForm();
                StaffOperationResult result;

                if (string.IsNullOrEmpty(hdnStaffId.Value))
                {
                    // 新增職員
                    result = await _staffService.CreateStaffAsync(staff, _currentUserId);
                }
                else
                {
                    // 更新職員
                    staff.Id = int.Parse(hdnStaffId.Value);
                    result = await _staffService.UpdateStaffAsync(staff, _currentUserId);
                }

                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    LoadStatistics();
                    LoadStaffData();
                    ClearStaffModal();
                }
                else
                {
                    var errors = string.Join("<br/>", result.Errors);
                    ShowMessage($"{result.Message}<br/>{errors}", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"儲存職員資料失敗: {ex.Message}", "danger");
            }
        }

        protected async void btnGenerateCode_Click(object sender, EventArgs e)
        {
            try
            {
                var staffType = ddlStaffTypeModal.SelectedValue;
                if (string.IsNullOrEmpty(staffType))
                {
                    ShowMessage("請先選擇職員類型", "warning");
                    return;
                }

                var employeeCode = await _staffService.GenerateNextEmployeeCodeAsync(staffType);
                txtEmployeeCode.Text = employeeCode;
            }
            catch (Exception ex)
            {
                ShowMessage($"產生員工編號失敗: {ex.Message}", "danger");
            }
        }

        protected async void btnConfirmRenew_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                    return;

                int staffId = int.Parse(hdnRenewStaffId.Value);
                DateTime startDate = DateTime.Parse(txtRenewStartDate.Text);
                DateTime endDate = DateTime.Parse(txtRenewEndDate.Text);

                var result = await _staffService.RenewContractAsync(staffId, startDate, endDate, _currentUserId);

                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    LoadStatistics();
                    LoadStaffData();
                    
                    // 清除續約表單
                    hdnRenewStaffId.Value = "";
                    txtRenewStartDate.Text = "";
                    txtRenewEndDate.Text = "";
                    lblRenewStaffName.Text = "";
                }
                else
                {
                    ShowMessage(result.Message, "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"續約失敗: {ex.Message}", "danger");
            }
        }

        protected void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: 實作 Excel 匯出功能
                ShowMessage("Excel 匯出功能開發中", "info");
            }
            catch (Exception ex)
            {
                ShowMessage($"匯出 Excel 失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region GridView 事件

        protected async void gvStaff_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int staffId = int.Parse(e.CommandArgument.ToString());

                switch (e.CommandName)
                {
                    case "ViewProfile":
                        Response.Redirect($"~/Staff/StaffProfile.aspx?id={staffId}");
                        break;

                    case "EditStaff":
                        await EditStaff(staffId);
                        break;

                    case "RenewContract":
                        await ShowRenewContract(staffId);
                        break;

                    case "DeleteStaff":
                        await DeleteStaff(staffId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"操作失敗: {ex.Message}", "danger");
            }
        }

        protected void gvStaff_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                try
                {
                    var staff = (Models.Staff)e.Row.DataItem;

                    // 權限控制
                    bool canEdit = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                                  _permissionService.HasRolePermission(_currentUserId, "StaffMember");
                    bool canDelete = _permissionService.HasRolePermission(_currentUserId, "Administrator");

                    var btnEdit = e.Row.FindControl("btnEdit") as LinkButton;
                    var btnDelete = e.Row.FindControl("btnDelete") as LinkButton;
                    var btnRenew = e.Row.FindControl("btnRenew") as LinkButton;

                    if (btnEdit != null) btnEdit.Visible = canEdit;
                    if (btnDelete != null) btnDelete.Visible = canDelete;

                    // 顯示續約按鈕條件：合約即將到期或已到期的在職職員
                    if (btnRenew != null)
                    {
                        btnRenew.Visible = canEdit && staff.EmploymentStatus == "Active" && 
                                         (staff.IsContractExpiringSoon || staff.IsContractExpired);
                    }

                    // 設定合約到期提醒的樣式
                    if (staff.IsContractExpired)
                    {
                        e.Row.CssClass += " table-danger";
                    }
                    else if (staff.IsContractExpiringSoon)
                    {
                        e.Row.CssClass += " table-warning";
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"RowDataBound 錯誤: {ex.Message}");
                }
            }
        }

        protected void gvStaff_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvStaff.PageIndex = e.NewPageIndex;
            LoadStaffData();
        }

        protected void gvStaff_Sorting(object sender, GridViewSortEventArgs e)
        {
            string sortField = e.SortExpression;
            string sortDirection = "ASC";

            // 切換排序方向
            if (ViewState["SortField"]?.ToString() == sortField)
            {
                sortDirection = ViewState["SortDirection"]?.ToString() == "ASC" ? "DESC" : "ASC";
            }

            ViewState["SortField"] = sortField;
            ViewState["SortDirection"] = sortDirection;

            gvStaff.PageIndex = 0;
            LoadStaffData();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 編輯職員
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        private async System.Threading.Tasks.Task EditStaff(int staffId)
        {
            var staff = await _staffService.GetStaffByIdAsync(staffId);
            if (staff == null)
            {
                ShowMessage("找不到指定的職員資料", "warning");
                return;
            }

            // 填入表單
            PopulateStaffModal(staff);
            lblModalTitle.Text = "編輯職員";
            hdnStaffId.Value = staff.Id.ToString();

            ScriptManager.RegisterStartupScript(this, GetType(), "showStaffModal", "showStaffModal();", true);
        }

        /// <summary>
        /// 顯示續約視窗
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        private async System.Threading.Tasks.Task ShowRenewContract(int staffId)
        {
            var staff = await _staffService.GetStaffByIdAsync(staffId);
            if (staff == null)
            {
                ShowMessage("找不到指定的職員資料", "warning");
                return;
            }

            hdnRenewStaffId.Value = staff.Id.ToString();
            lblRenewStaffName.Text = staff.DisplayName;

            // 設定預設的續約日期
            var startDate = staff.ContractEndDate?.AddDays(1) ?? DateTime.Today;
            var endDate = startDate.AddYears(1);

            txtRenewStartDate.Text = startDate.ToString("yyyy-MM-dd");
            txtRenewEndDate.Text = endDate.ToString("yyyy-MM-dd");

            ScriptManager.RegisterStartupScript(this, GetType(), "showRenewModal", "showRenewModal();", true);
        }

        /// <summary>
        /// 刪除職員
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        private async System.Threading.Tasks.Task DeleteStaff(int staffId)
        {
            var result = await _staffService.DeleteStaffAsync(staffId, _currentUserId);

            if (result.Success)
            {
                ShowMessage(result.Message, "success");
                LoadStatistics();
                LoadStaffData();
            }
            else
            {
                ShowMessage(result.Message, "danger");
            }
        }

        /// <summary>
        /// 從表單建立職員物件
        /// </summary>
        /// <returns>職員物件</returns>
        private Models.Staff BuildStaffFromForm()
        {
            var staff = new Models.Staff
            {
                EmployeeCode = txtEmployeeCode.Text.Trim(),
                FullName = txtFullName.Text.Trim(),
                Gender = ddlGender.SelectedValue,
                HKID = txtHKID.Text.Trim(),
                Phone = txtPhone.Text.Trim(), // 會自動加密
                Email = txtEmail.Text.Trim(), // 會自動加密
                Address = txtAddress.Text.Trim(), // 會自動加密
                Department = txtDepartment.Text.Trim(),
                Position = txtPosition.Text.Trim(),
                StaffType = ddlStaffTypeModal.SelectedValue,
                EmploymentType = ddlEmploymentTypeModal.SelectedValue,
                EmploymentStatus = ddlEmploymentStatusModal.SelectedValue,
                HireDate = DateTime.Parse(txtHireDate.Text),
                EmergencyContact = txtEmergencyContact.Text.Trim(),
                EmergencyPhone = txtEmergencyPhone.Text.Trim(),
                Specialization = txtSpecialization.Text.Trim(),
                BankAccount = txtBankAccount.Text.Trim(),
                Remarks = txtRemarks.Text.Trim(),
                IsActive = chkIsActive.Checked
            };

            // 出生日期
            if (!string.IsNullOrEmpty(txtDateOfBirth.Text))
            {
                staff.DateOfBirth = DateTime.Parse(txtDateOfBirth.Text);
            }

            // 合約日期
            if (!string.IsNullOrEmpty(txtContractStartDate.Text))
            {
                staff.ContractStartDate = DateTime.Parse(txtContractStartDate.Text);
            }

            if (!string.IsNullOrEmpty(txtContractEndDate.Text))
            {
                staff.ContractEndDate = DateTime.Parse(txtContractEndDate.Text);
            }

            // 薪資
            if (!string.IsNullOrEmpty(txtBasicSalary.Text))
            {
                staff.BasicSalary = decimal.Parse(txtBasicSalary.Text);
            }

            if (!string.IsNullOrEmpty(txtHourlyRate.Text))
            {
                staff.HourlyRate = decimal.Parse(txtHourlyRate.Text);
            }

            return staff;
        }

        /// <summary>
        /// 填入職員資料到表單
        /// </summary>
        /// <param name="staff">職員物件</param>
        private void PopulateStaffModal(Models.Staff staff)
        {
            txtEmployeeCode.Text = staff.EmployeeCode;
            txtFullName.Text = staff.FullName;
            ddlGender.SelectedValue = staff.Gender ?? "";
            txtDateOfBirth.Text = staff.DateOfBirth?.ToString("yyyy-MM-dd") ?? "";
            txtHKID.Text = staff.HKID;
            txtPhone.Text = staff.Phone; // 會自動解密
            txtEmail.Text = staff.Email; // 會自動解密
            txtAddress.Text = staff.Address; // 會自動解密
            txtDepartment.Text = staff.Department ?? "";
            txtPosition.Text = staff.Position ?? "";
            ddlStaffTypeModal.SelectedValue = staff.StaffType;
            ddlEmploymentTypeModal.SelectedValue = staff.EmploymentType;
            ddlEmploymentStatusModal.SelectedValue = staff.EmploymentStatus;
            txtHireDate.Text = staff.HireDate.ToString("yyyy-MM-dd");
            txtContractStartDate.Text = staff.ContractStartDate?.ToString("yyyy-MM-dd") ?? "";
            txtContractEndDate.Text = staff.ContractEndDate?.ToString("yyyy-MM-dd") ?? "";
            txtBasicSalary.Text = staff.BasicSalary?.ToString("0.00") ?? "";
            txtHourlyRate.Text = staff.HourlyRate?.ToString("0.00") ?? "";
            txtEmergencyContact.Text = staff.EmergencyContact ?? "";
            txtEmergencyPhone.Text = staff.EmergencyPhone ?? "";
            txtSpecialization.Text = staff.Specialization ?? "";
            txtBankAccount.Text = staff.BankAccount ?? "";
            txtRemarks.Text = staff.Remarks ?? "";
            chkIsActive.Checked = staff.IsActive;
        }

        /// <summary>
        /// 清除職員表單
        /// </summary>
        private void ClearStaffModal()
        {
            txtEmployeeCode.Text = "";
            txtFullName.Text = "";
            ddlGender.SelectedIndex = 0;
            txtDateOfBirth.Text = "";
            txtHKID.Text = "";
            txtPhone.Text = "";
            txtEmail.Text = "";
            txtAddress.Text = "";
            txtDepartment.Text = "";
            txtPosition.Text = "";
            ddlStaffTypeModal.SelectedIndex = 0;
            ddlEmploymentTypeModal.SelectedIndex = 0;
            ddlEmploymentStatusModal.SelectedIndex = 0;
            txtHireDate.Text = "";
            txtContractStartDate.Text = "";
            txtContractEndDate.Text = "";
            txtBasicSalary.Text = "";
            txtHourlyRate.Text = "";
            txtEmergencyContact.Text = "";
            txtEmergencyPhone.Text = "";
            txtSpecialization.Text = "";
            txtBankAccount.Text = "";
            txtRemarks.Text = "";
            chkIsActive.Checked = true;
        }

        /// <summary>
        /// 顯示訊息
        /// </summary>
        /// <param name="message">訊息內容</param>
        /// <param name="type">訊息類型</param>
        private void ShowMessage(string message, string type)
        {
            string alertClass = $"alert alert-{type} alert-dismissible fade show";
            pnlMessage.CssClass = alertClass;
            ltlMessage.Text = message;
            pnlMessage.Visible = true;
        }

        /// <summary>
        /// 註冊前端腳本
        /// </summary>
        private void RegisterScripts()
        {
            // 註冊必要的 JavaScript 庫
            if (!Page.ClientScript.IsClientScriptIncludeRegistered("SweetAlert2"))
            {
                string script = "<script src='https://cdn.jsdelivr.net/npm/sweetalert2@11'></script>";
                Page.ClientScript.RegisterClientScriptBlock(GetType(), "SweetAlert2", script, false);
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 取得職員類型標籤顏色
        /// </summary>
        public string GetStaffTypeBadgeColor(string staffType)
        {
            switch (staffType)
            {
                case "Teacher": return "primary";
                case "Staff": return "info";
                case "Administrator": return "success";
                default: return "secondary";
            }
        }

        /// <summary>
        /// 取得職員類型顯示文字
        /// </summary>
        public string GetStaffTypeDisplayText(string staffType)
        {
            var types = StaffConstants.StaffTypes.GetAll();
            return types.ContainsKey(staffType) ? types[staffType] : staffType;
        }

        /// <summary>
        /// 取得聘用類型顯示文字
        /// </summary>
        public string GetEmploymentTypeDisplayText(string employmentType)
        {
            var types = StaffConstants.EmploymentTypes.GetAll();
            return types.ContainsKey(employmentType) ? types[employmentType] : employmentType;
        }

        /// <summary>
        /// 取得在職狀態標籤顏色
        /// </summary>
        public string GetEmploymentStatusBadgeColor(string status)
        {
            switch (status)
            {
                case "Active": return "success";
                case "Inactive": return "warning";
                case "Expired": return "danger";
                case "Terminated": return "dark";
                default: return "secondary";
            }
        }

        /// <summary>
        /// 取得在職狀態顯示文字
        /// </summary>
        public string GetEmploymentStatusDisplayText(string status)
        {
            var statuses = StaffConstants.EmploymentStatuses.GetAll();
            return statuses.ContainsKey(status) ? statuses[status] : status;
        }

        /// <summary>
        /// 格式化合約到期日期
        /// </summary>
        public string FormatContractEndDate(object contractEndDate)
        {
            if (contractEndDate == null || contractEndDate == DBNull.Value)
                return "<span class='text-muted'>無期限</span>";

            var endDate = (DateTime)contractEndDate;
            var daysUntilExpiry = (endDate - DateTime.Today).Days;

            if (daysUntilExpiry < 0)
            {
                return $"<span class='text-danger fw-bold'>{endDate:yyyy-MM-dd}<br/><small>已過期 {Math.Abs(daysUntilExpiry)} 天</small></span>";
            }
            else if (daysUntilExpiry <= 30)
            {
                return $"<span class='text-warning fw-bold'>{endDate:yyyy-MM-dd}<br/><small>{daysUntilExpiry} 天後到期</small></span>";
            }
            else
            {
                return $"<span class='text-success'>{endDate:yyyy-MM-dd}</span>";
            }
        }

        #endregion
    }
}