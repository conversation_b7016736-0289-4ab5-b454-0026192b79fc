using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Microsoft.AspNet.Identity;
using CWDECC_3S.Models;
using CWDECC_3S.Services;

namespace CWDECC_3S.Staff
{
    /// <summary>
    /// 職員個人檔案頁面 - 顯示職員詳細資料、合約資訊、活動記錄和工時統計
    /// </summary>
    public partial class StaffProfile : System.Web.UI.Page
    {
        private StaffService _staffService;
        private PermissionService _permissionService;
        private ActivityService _activityService;
        private string _currentUserId;
        private int _staffId;
        private Models.Staff _currentStaff;

        protected void Page_Init(object sender, EventArgs e)
        {
            _staffService = new StaffService();
            _permissionService = new PermissionService();
            _activityService = new ActivityService();
            _currentUserId = User.Identity.GetUserId();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限 - 只有主管和 HR 角色可以操作
                if (!_permissionService.HasRolePermission(_currentUserId, "Administrator") &&
                    !_permissionService.HasRolePermission(_currentUserId, "StaffMember"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                // 取得職員 ID
                if (!int.TryParse(Request.QueryString["id"], out _staffId) || _staffId <= 0)
                {
                    ShowMessage("無效的職員 ID", "danger");
                    Response.Redirect("~/Staff/StaffManagement.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    InitializeControls();
                    LoadStaffProfile();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"頁面載入錯誤: {ex.Message}", "danger");
            }
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            RegisterScripts();
        }

        protected void Page_Unload(object sender, EventArgs e)
        {
            _staffService?.Dispose();
            _permissionService?.Dispose();
            _activityService?.Dispose();
        }

        #region 初始化

        /// <summary>
        /// 初始化控制項
        /// </summary>
        private void InitializeControls()
        {
            // 初始化統計月份下拉選單
            InitializeStatsMonthDropDown();

            // 初始化活動和角色下拉選單
            InitializeActivityDropDowns();

            // 設定權限控制
            SetPermissionControls();
        }

        /// <summary>
        /// 初始化統計月份下拉選單
        /// </summary>
        private void InitializeStatsMonthDropDown()
        {
            ddlStatsMonth.Items.Clear();
            
            // 添加最近12個月
            for (int i = 0; i < 12; i++)
            {
                var date = DateTime.Today.AddMonths(-i);
                var value = date.ToString("yyyy-MM");
                var text = date.ToString("yyyy年MM月");
                ddlStatsMonth.Items.Add(new ListItem(text, value));
            }

            // 預設選擇當月
            ddlStatsMonth.SelectedValue = DateTime.Today.ToString("yyyy-MM");
        }

        /// <summary>
        /// 初始化活動相關下拉選單
        /// </summary>
        private async void InitializeActivityDropDowns()
        {
            try
            {
                // 載入活動列表
                var activities = await _activityService.GetActiveActivitiesAsync();
                ddlActivity.Items.Add(new ListItem("請選擇", ""));
                foreach (var activity in activities)
                {
                    ddlActivity.Items.Add(new ListItem(activity.Title, activity.Id.ToString()));
                }

                // 載入參與角色
                var roles = StaffConstants.ActivityRoles.GetAll();
                ddlRole.Items.Add(new ListItem("請選擇", ""));
                foreach (var role in roles)
                {
                    ddlRole.Items.Add(new ListItem(role.Value, role.Key));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化活動下拉選單失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 設定權限控制
        /// </summary>
        private void SetPermissionControls()
        {
            // 檢查是否有編輯權限
            bool canEdit = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                          _permissionService.HasRolePermission(_currentUserId, "StaffMember");
            
            btnEdit.Visible = canEdit;
            btnAddActivity.Visible = canEdit;
            btnRenewContract.Visible = canEdit;
        }

        #endregion

        #region 資料載入

        /// <summary>
        /// 載入職員檔案
        /// </summary>
        private async void LoadStaffProfile()
        {
            try
            {
                _currentStaff = await _staffService.GetStaffByIdAsync(_staffId);
                if (_currentStaff == null)
                {
                    ShowMessage("找不到指定的職員資料", "warning");
                    Response.Redirect("~/Staff/StaffManagement.aspx");
                    return;
                }

                // 載入基本資料
                LoadBasicInformation();
                
                // 載入聯絡資料
                LoadContactInformation();
                
                // 載入合約資訊
                LoadContractInformation();
                
                // 載入薪資資訊
                LoadSalaryInformation();
                
                // 載入工時統計
                LoadHoursStatistics();
                
                // 載入活動記錄
                LoadRecentActivities();
                
                // 檢查並顯示狀態警告
                CheckAndShowStatusWarnings();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入職員資料失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 載入基本資料
        /// </summary>
        private void LoadBasicInformation()
        {
            // 頁面標題
            lblStaffName.Text = _currentStaff.DisplayName;
            lblStaffCode.Text = $"({_currentStaff.EmployeeCode})";
            
            // 基本資料卡片
            lblProfileName.Text = _currentStaff.FullName;
            lblProfileCode.Text = _currentStaff.EmployeeCode;
            
            // 頭像字母
            lblAvatarInitial.Text = string.IsNullOrEmpty(_currentStaff.FullName) ? "?" : 
                _currentStaff.FullName.Substring(0, 1).ToUpper();
            
            // 在職狀態標籤
            var statusClass = GetEmploymentStatusBadgeColor(_currentStaff.EmploymentStatus);
            lblEmploymentStatusBadge.Text = _currentStaff.StatusDisplayText;
            lblEmploymentStatusBadge.CssClass = $"badge bg-{statusClass}";
            
            // 基本資訊
            lblGender.Text = _currentStaff.Gender == "M" ? "男" : "女";
            lblAge.Text = _currentStaff.Age?.ToString() + " 歲" ?? "未提供";
            lblHKID.Text = _currentStaff.HKID;
            
            // 職員類型標籤
            var staffTypeClass = GetStaffTypeBadgeColor(_currentStaff.StaffType);
            lblStaffTypeBadge.Text = _currentStaff.StaffTypeDisplayText;
            lblStaffTypeBadge.CssClass = $"badge bg-{staffTypeClass}";
            
            lblEmploymentType.Text = _currentStaff.EmploymentTypeDisplayText;
            lblDepartment.Text = _currentStaff.Department ?? "未指定";
            lblPosition.Text = _currentStaff.Position ?? "未指定";
            lblServiceYears.Text = $"{_currentStaff.ServiceYears:F1} 年";
        }

        /// <summary>
        /// 載入聯絡資料
        /// </summary>
        private void LoadContactInformation()
        {
            // 解密敏感資料
            lblPhone.Text = string.IsNullOrEmpty(_currentStaff.Phone) ? "未提供" : _currentStaff.Phone;
            lblEmail.Text = string.IsNullOrEmpty(_currentStaff.Email) ? "未提供" : _currentStaff.Email;
            lblAddress.Text = string.IsNullOrEmpty(_currentStaff.Address) ? "未提供" : _currentStaff.Address;
            
            // 顯示快速聯絡按鈕
            lnkCallPhone.Visible = !string.IsNullOrEmpty(_currentStaff.Phone);
            lnkSendEmail.Visible = !string.IsNullOrEmpty(_currentStaff.Email);
            
            // 緊急聯絡人
            lblEmergencyContact.Text = _currentStaff.EmergencyContact ?? "未提供";
            lblEmergencyPhone.Text = _currentStaff.EmergencyPhone ?? "未提供";
        }

        /// <summary>
        /// 載入合約資訊
        /// </summary>
        private void LoadContractInformation()
        {
            lblHireDate.Text = _currentStaff.HireDate.ToString("yyyy-MM-dd");
            lblContractStartDate.Text = _currentStaff.ContractStartDate?.ToString("yyyy-MM-dd") ?? "未設定";
            lblContractEndDate.Text = _currentStaff.ContractEndDate?.ToString("yyyy-MM-dd") ?? "無期限";
            
            // 合約狀態標籤
            if (_currentStaff.ContractEndDate.HasValue)
            {
                var contractStatus = GetContractStatusInfo(_currentStaff.ContractEndDate.Value);
                lblContractStatus.Text = contractStatus.Text;
                lblContractStatus.CssClass = $"badge bg-{contractStatus.BadgeColor}";
                lblContractStatus.Visible = true;
                
                // 計算合約天數和剩餘天數
                if (_currentStaff.ContractStartDate.HasValue)
                {
                    var totalDays = (_currentStaff.ContractEndDate.Value - _currentStaff.ContractStartDate.Value).Days;
                    lblContractDays.Text = totalDays + " 天";
                }
                else
                {
                    lblContractDays.Text = "未知";
                }
                
                var remainingDays = (_currentStaff.ContractEndDate.Value - DateTime.Today).Days;
                if (remainingDays >= 0)
                {
                    lblRemainingDays.Text = remainingDays + " 天";
                }
                else
                {
                    lblRemainingDays.Text = $"已過期 {Math.Abs(remainingDays)} 天";
                }
            }
            else
            {
                lblContractStatus.Visible = false;
                lblContractDays.Text = "無期限";
                lblRemainingDays.Text = "無期限";
            }
            
            // 顯示續約按鈕
            btnRenewContract.Visible = (_currentStaff.IsContractExpiringSoon || _currentStaff.IsContractExpired) && 
                                     _currentStaff.EmploymentStatus == "Active";
            
            // 合約提醒
            ShowContractAlert();
        }

        /// <summary>
        /// 載入薪資資訊
        /// </summary>
        private void LoadSalaryInformation()
        {
            lblBasicSalary.Text = _currentStaff.BasicSalary?.ToString("C") ?? "未設定";
            lblHourlyRate.Text = _currentStaff.HourlyRate?.ToString("C") ?? "未設定";
            lblBankAccount.Text = string.IsNullOrEmpty(_currentStaff.BankAccount) ? "未提供" : 
                MaskBankAccount(_currentStaff.BankAccount);
            
            // 專長和備註
            lblSpecialization.Text = string.IsNullOrEmpty(_currentStaff.Specialization) ? "未提供" : _currentStaff.Specialization;
            lblRemarks.Text = string.IsNullOrEmpty(_currentStaff.Remarks) ? "無備註" : _currentStaff.Remarks;
        }

        /// <summary>
        /// 載入工時統計
        /// </summary>
        private async void LoadHoursStatistics()
        {
            try
            {
                var selectedMonth = DateTime.ParseExact(ddlStatsMonth.SelectedValue, "yyyy-MM", CultureInfo.InvariantCulture);
                var fromDate = new DateTime(selectedMonth.Year, selectedMonth.Month, 1);
                var toDate = fromDate.AddMonths(1).AddDays(-1);
                
                var stats = await _staffService.GetStaffHoursStatisticsAsync(_staffId, fromDate, toDate);
                
                lblTotalTeachingHours.Text = stats.TotalTeachingHours.ToString("0.0");
                lblTotalPreparationHours.Text = stats.TotalPreparationHours.ToString("0.0");
                lblTotalActivities.Text = stats.TotalActivities.ToString();
                lblTotalFees.Text = stats.TotalFees.ToString("C");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入工時統計失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 載入近期活動記錄
        /// </summary>
        private async void LoadRecentActivities()
        {
            try
            {
                var activities = await _staffService.GetStaffActivitiesAsync(_staffId, DateTime.Today.AddMonths(-3));
                var recentActivities = activities.Take(5).ToList();
                
                if (recentActivities.Any())
                {
                    rptRecentActivities.DataSource = recentActivities;
                    rptRecentActivities.DataBind();
                    pnlNoActivities.Visible = false;
                }
                else
                {
                    pnlNoActivities.Visible = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入活動記錄失敗: {ex.Message}");
                pnlNoActivities.Visible = true;
            }
        }

        #endregion

        #region 按鈕事件

        protected void btnEdit_Click(object sender, EventArgs e)
        {
            Response.Redirect($"~/Staff/StaffManagement.aspx?edit={_staffId}");
        }

        protected void btnBack_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Staff/StaffManagement.aspx");
        }

        protected async void btnRenewContract_Click(object sender, EventArgs e)
        {
            try
            {
                // 設定預設的續約日期
                var startDate = _currentStaff.ContractEndDate?.AddDays(1) ?? DateTime.Today;
                var endDate = startDate.AddYears(1);
                
                var result = await _staffService.RenewContractAsync(_staffId, startDate, endDate, _currentUserId);
                
                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    LoadStaffProfile(); // 重新載入資料
                }
                else
                {
                    ShowMessage(result.Message, "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"續約失敗: {ex.Message}", "danger");
            }
        }

        protected void ddlStatsMonth_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadHoursStatistics();
        }

        protected void lnkViewAllActivities_Click(object sender, EventArgs e)
        {
            pnlAllActivities.Visible = true;
            LoadAllActivities();
        }

        protected void btnHideActivities_Click(object sender, EventArgs e)
        {
            pnlAllActivities.Visible = false;
        }

        protected void btnAddActivity_Click(object sender, EventArgs e)
        {
            ClearActivityModal();
            ScriptManager.RegisterStartupScript(this, GetType(), "showActivityModal", "showActivityModal();", true);
        }

        protected async void btnSaveActivity_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                    return;
                
                var staffActivity = new StaffActivity
                {
                    StaffId = _staffId,
                    ActivityId = int.Parse(ddlActivity.SelectedValue),
                    Role = ddlRole.SelectedValue,
                    TeachingHours = string.IsNullOrEmpty(txtTeachingHours.Text) ? 0 : decimal.Parse(txtTeachingHours.Text),
                    PreparationHours = string.IsNullOrEmpty(txtPreparationHours.Text) ? 0 : decimal.Parse(txtPreparationHours.Text),
                    Fee = string.IsNullOrEmpty(txtFee.Text) ? (decimal?)null : decimal.Parse(txtFee.Text),
                    Remarks = txtActivityRemarks.Text.Trim()
                };
                
                var result = await _staffService.AddStaffActivityAsync(staffActivity, _currentUserId);
                
                if (result.Success)
                {
                    ShowMessage("活動記錄新增成功", "success");
                    ClearActivityModal();
                    LoadRecentActivities();
                    LoadHoursStatistics();
                }
                else
                {
                    ShowMessage(result.Message, "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"新增活動記錄失敗: {ex.Message}", "danger");
            }
        }

        protected void btnViewTimesheet_Click(object sender, EventArgs e)
        {
            Response.Redirect($"~/Staff/StaffTimesheet.aspx?id={_staffId}");
        }

        protected void btnGenerateReport_Click(object sender, EventArgs e)
        {
            Response.Redirect($"~/Staff/StaffReport.aspx?id={_staffId}");
        }

        #endregion

        #region GridView 事件

        /// <summary>
        /// 載入完整活動記錄
        /// </summary>
        private async void LoadAllActivities()
        {
            try
            {
                var activities = await _staffService.GetStaffActivitiesAsync(_staffId);
                
                gvAllActivities.DataSource = activities;
                gvAllActivities.DataBind();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入活動記錄失敗: {ex.Message}", "danger");
            }
        }

        protected void gvAllActivities_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            // TODO: 實作編輯活動記錄功能
            if (e.CommandName == "EditActivity")
            {
                int activityRecordId = int.Parse(e.CommandArgument.ToString());
                // 編輯活動記錄的邏輯
            }
        }

        protected void gvAllActivities_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            // 資料綁定時的額外處理
        }

        protected void gvAllActivities_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvAllActivities.PageIndex = e.NewPageIndex;
            LoadAllActivities();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 檢查並顯示狀態警告
        /// </summary>
        private void CheckAndShowStatusWarnings()
        {
            var warnings = new List<string>();
            
            if (_currentStaff.IsContractExpired)
            {
                warnings.Add($"合約已於 {_currentStaff.ContractEndDate?.ToString("yyyy-MM-dd")} 到期，請盡快處理續約或離職手續。");
            }
            else if (_currentStaff.IsContractExpiringSoon)
            {
                var daysUntilExpiry = (_currentStaff.ContractEndDate.Value - DateTime.Today).Days;
                warnings.Add($"合約將於 {daysUntilExpiry} 天後到期（{_currentStaff.ContractEndDate?.ToString("yyyy-MM-dd")}），請準備續約事宜。");
            }
            
            if (_currentStaff.EmploymentStatus != "Active")
            {
                warnings.Add($"職員狀態為「{_currentStaff.StatusDisplayText}」，請確認是否需要更新狀態。");
            }
            
            if (warnings.Any())
            {
                pnlStatusWarning.Visible = true;
                ltlStatusWarning.Text = string.Join("<br/>", warnings);
            }
        }

        /// <summary>
        /// 顯示合約提醒
        /// </summary>
        private void ShowContractAlert()
        {
            if (!_currentStaff.ContractEndDate.HasValue)
                return;
                
            var daysUntilExpiry = (_currentStaff.ContractEndDate.Value - DateTime.Today).Days;
            
            if (daysUntilExpiry < 0)
            {
                pnlContractAlert.Visible = true;
                pnlContractAlert.CssClass = "alert alert-sm alert-danger";
                ltlContractAlert.Text = $"<i class='fas fa-exclamation-triangle me-1'></i>合約已過期 {Math.Abs(daysUntilExpiry)} 天";
            }
            else if (daysUntilExpiry <= 30)
            {
                pnlContractAlert.Visible = true;
                pnlContractAlert.CssClass = "alert alert-sm alert-warning";
                ltlContractAlert.Text = $"<i class='fas fa-clock me-1'></i>合約將於 {daysUntilExpiry} 天後到期";
            }
        }

        /// <summary>
        /// 取得合約狀態資訊
        /// </summary>
        private (string Text, string BadgeColor) GetContractStatusInfo(DateTime endDate)
        {
            var daysUntilExpiry = (endDate - DateTime.Today).Days;
            
            if (daysUntilExpiry < 0)
            {
                return ("已過期", "danger");
            }
            else if (daysUntilExpiry <= 30)
            {
                return ("即將到期", "warning");
            }
            else
            {
                return ("有效", "success");
            }
        }

        /// <summary>
        /// 遮罩銀行帳戶號碼
        /// </summary>
        private string MaskBankAccount(string bankAccount)
        {
            if (string.IsNullOrEmpty(bankAccount) || bankAccount.Length <= 4)
                return bankAccount;
                
            var masked = bankAccount.Substring(0, 4) + new string('*', bankAccount.Length - 4);
            return masked;
        }

        /// <summary>
        /// 清除活動記錄表單
        /// </summary>
        private void ClearActivityModal()
        {
            hdnActivityRecordId.Value = "";
            ddlActivity.SelectedIndex = 0;
            ddlRole.SelectedIndex = 0;
            txtTeachingHours.Text = "";
            txtPreparationHours.Text = "";
            txtFee.Text = "";
            txtActivityRemarks.Text = "";
        }

        /// <summary>
        /// 顯示訊息
        /// </summary>
        private void ShowMessage(string message, string type)
        {
            string alertClass = $"alert alert-{type} alert-dismissible fade show";
            pnlMessage.CssClass = alertClass;
            ltlMessage.Text = message;
            pnlMessage.Visible = true;
        }

        /// <summary>
        /// 註冊前端腳本
        /// </summary>
        private void RegisterScripts()
        {
            // 註冊必要的 JavaScript 庫
            if (!Page.ClientScript.IsClientScriptIncludeRegistered("SweetAlert2"))
            {
                string script = "<script src='https://cdn.jsdelivr.net/npm/sweetalert2@11'></script>";
                Page.ClientScript.RegisterClientScriptBlock(GetType(), "SweetAlert2", script, false);
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 取得職員類型標籤顏色
        /// </summary>
        public string GetStaffTypeBadgeColor(string staffType)
        {
            switch (staffType)
            {
                case "Teacher": return "primary";
                case "Staff": return "info";
                case "Administrator": return "success";
                default: return "secondary";
            }
        }

        /// <summary>
        /// 取得在職狀態標籤顏色
        /// </summary>
        public string GetEmploymentStatusBadgeColor(string status)
        {
            switch (status)
            {
                case "Active": return "success";
                case "Inactive": return "warning";
                case "Expired": return "danger";
                case "Terminated": return "dark";
                default: return "secondary";
            }
        }

        /// <summary>
        /// 取得活動角色顯示文字
        /// </summary>
        public string GetActivityRoleDisplayText(string role)
        {
            var roles = StaffConstants.ActivityRoles.GetAll();
            return roles.ContainsKey(role) ? roles[role] : role;
        }

        /// <summary>
        /// 取得活動角色標籤顏色
        /// </summary>
        public string GetActivityRoleBadgeColor(string role)
        {
            switch (role)
            {
                case "Teacher": return "primary";
                case "Assistant": return "info";
                case "Coordinator": return "success";
                default: return "secondary";
            }
        }

        #endregion
    }
}
