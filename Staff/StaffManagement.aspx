<%@ Page Title="職員管理" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="StaffManagement.aspx.cs" Inherits="CWDECC_3S.Staff.StaffManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-users text-primary me-2"></i>職員管理
                </h2>
                <p class="text-muted">管理職員與導師資料、合約與服務記錄</p>
            </div>
            <div>
                <asp:Button ID="btnAddStaff" runat="server" Text="新增職員" CssClass="btn btn-success"
                    OnClick="btnAddStaff_Click" />
                <asp:Button ID="btnUpdateContracts" runat="server" Text="更新合約狀態" CssClass="btn btn-warning"
                    OnClick="btnUpdateContracts_Click" ToolTip="檢查並更新所有到期的合約狀態" />
                <asp:Button ID="btnRefresh" runat="server" Text="重新整理" CssClass="btn btn-outline-primary"
                    OnClick="btnRefresh_Click" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <!-- 統計卡片 -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">總職員數</h6>
                                <h4><asp:Label ID="lblTotalStaff" runat="server" Text="0"></asp:Label></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">在職職員</h6>
                                <h4><asp:Label ID="lblActiveStaff" runat="server" Text="0"></asp:Label></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-check fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">導師</h6>
                                <h4><asp:Label ID="lblTeachers" runat="server" Text="0"></asp:Label></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chalkboard-teacher fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-white bg-secondary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">職員</h6>
                                <h4><asp:Label ID="lblGeneralStaff" runat="server" Text="0"></asp:Label></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-tie fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">即將到期</h6>
                                <h4><asp:Label ID="lblExpiringSoon" runat="server" Text="0"></asp:Label></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">已到期</h6>
                                <h4><asp:Label ID="lblExpired" runat="server" Text="0"></asp:Label></h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜尋和篩選 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>搜尋和篩選
                    <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#searchPanel" aria-expanded="false" aria-controls="searchPanel">
                        <i class="fas fa-filter"></i> 篩選器
                    </button>
                </h5>
            </div>
            <div class="collapse show" id="searchPanel">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="<%= txtKeyword.ClientID %>" class="form-label">關鍵字</label>
                            <asp:TextBox ID="txtKeyword" runat="server" CssClass="form-control" 
                                placeholder="姓名、員工編號、部門..." />
                        </div>
                        <div class="col-md-2">
                            <label for="<%= ddlStaffType.ClientID %>" class="form-label">職員類型</label>
                            <asp:DropDownList ID="ddlStaffType" runat="server" CssClass="form-select">
                            </asp:DropDownList>
                        </div>
                        <div class="col-md-2">
                            <label for="<%= ddlEmploymentType.ClientID %>" class="form-label">聘用類型</label>
                            <asp:DropDownList ID="ddlEmploymentType" runat="server" CssClass="form-select">
                            </asp:DropDownList>
                        </div>
                        <div class="col-md-2">
                            <label for="<%= ddlEmploymentStatus.ClientID %>" class="form-label">在職狀態</label>
                            <asp:DropDownList ID="ddlEmploymentStatus" runat="server" CssClass="form-select">
                            </asp:DropDownList>
                        </div>
                        <div class="col-md-2">
                            <label for="<%= ddlDepartment.ClientID %>" class="form-label">部門</label>
                            <asp:DropDownList ID="ddlDepartment" runat="server" CssClass="form-select">
                            </asp:DropDownList>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <asp:Button ID="btnSearch" runat="server" Text="搜尋" CssClass="btn btn-primary"
                                    OnClick="btnSearch_Click" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 職員列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>職員列表
                    <span class="badge bg-secondary ms-2">
                        <asp:Label ID="lblRecordCount" runat="server" Text="0"></asp:Label>
                    </span>
                </h5>
                <div>
                    <asp:Button ID="btnExportExcel" runat="server" Text="匯出 Excel" CssClass="btn btn-outline-success btn-sm"
                        OnClick="btnExportExcel_Click" />
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <asp:GridView ID="gvStaff" runat="server" CssClass="table table-striped table-hover"
                        AutoGenerateColumns="False" DataKeyNames="Id" EmptyDataText="沒有找到符合條件的職員資料"
                        OnRowCommand="gvStaff_RowCommand" OnRowDataBound="gvStaff_RowDataBound"
                        OnPageIndexChanging="gvStaff_PageIndexChanging" OnSorting="gvStaff_Sorting"
                        AllowPaging="True" PageSize="20" AllowSorting="True">
                        <Columns>
                            <asp:BoundField DataField="EmployeeCode" HeaderText="員工編號" SortExpression="EmployeeCode">
                                <HeaderStyle Width="10%" />
                            </asp:BoundField>
                            <asp:BoundField DataField="FullName" HeaderText="姓名" SortExpression="FullName">
                                <HeaderStyle Width="12%" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="職員類型" SortExpression="StaffType">
                                <ItemTemplate>
                                    <span class='badge bg-<%# GetStaffTypeBadgeColor(Eval("StaffType").ToString()) %>'>
                                        <%# GetStaffTypeDisplayText(Eval("StaffType").ToString()) %>
                                    </span>
                                </ItemTemplate>
                                <HeaderStyle Width="8%" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="Department" HeaderText="部門" SortExpression="Department">
                                <HeaderStyle Width="10%" />
                            </asp:BoundField>
                            <asp:BoundField DataField="Position" HeaderText="職位" SortExpression="Position">
                                <HeaderStyle Width="10%" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="聘用類型" SortExpression="EmploymentType">
                                <ItemTemplate>
                                    <%# GetEmploymentTypeDisplayText(Eval("EmploymentType").ToString()) %>
                                </ItemTemplate>
                                <HeaderStyle Width="8%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="在職狀態" SortExpression="EmploymentStatus">
                                <ItemTemplate>
                                    <span class='badge bg-<%# GetEmploymentStatusBadgeColor(Eval("EmploymentStatus").ToString()) %>'>
                                        <%# GetEmploymentStatusDisplayText(Eval("EmploymentStatus").ToString()) %>
                                    </span>
                                </ItemTemplate>
                                <HeaderStyle Width="8%" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="HireDate" HeaderText="入職日期" SortExpression="HireDate" 
                                DataFormatString="{0:yyyy-MM-dd}">
                                <HeaderStyle Width="10%" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="合約到期">
                                <ItemTemplate>
                                    <%# FormatContractEndDate(Eval("ContractEndDate")) %>
                                </ItemTemplate>
                                <HeaderStyle Width="10%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="操作">
                                <ItemTemplate>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <asp:LinkButton ID="btnView" runat="server" 
                                            CommandName="ViewProfile" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-info btn-sm" 
                                            ToolTip="查看檔案">
                                            <i class="fas fa-eye"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnEdit" runat="server" 
                                            CommandName="EditStaff" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-warning btn-sm" 
                                            ToolTip="編輯">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnRenew" runat="server" 
                                            CommandName="RenewContract" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-success btn-sm" 
                                            ToolTip="續約" Visible="false">
                                            <i class="fas fa-file-contract"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnDelete" runat="server" 
                                            CommandName="DeleteStaff" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-danger btn-sm" 
                                            ToolTip="刪除"
                                            OnClientClick="return confirm('確定要刪除此職員嗎？');">
                                            <i class="fas fa-trash"></i>
                                        </asp:LinkButton>
                                    </div>
                                </ItemTemplate>
                                <HeaderStyle Width="14%" />
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="table-dark" />
                        <EmptyDataRowStyle CssClass="text-center text-muted" />
                        <PagerStyle CssClass="pagination-wrapper" />
                        <PagerTemplate>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="pagination-info">
                                    顯示第 
                                    <span class="fw-bold"><%# ((GridView)Container.NamingContainer).PageIndex * ((GridView)Container.NamingContainer).PageSize + 1 %></span>
                                    -
                                    <span class="fw-bold"><%# Math.Min((((GridView)Container.NamingContainer).PageIndex + 1) * ((GridView)Container.NamingContainer).PageSize, Convert.ToInt32(ViewState["TotalRecords"] ?? 0)) %></span>
                                    項，共 
                                    <span class="fw-bold"><%# ViewState["TotalRecords"] ?? 0 %></span> 項
                                </div>
                                <nav aria-label="分頁導航">
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item <%# ((GridView)Container.NamingContainer).PageIndex == 0 ? "disabled" : "" %>">
                                            <asp:LinkButton ID="lnkFirst" runat="server" CssClass="page-link" 
                                                CommandName="Page" CommandArgument="First" Text="第一頁" />
                                        </li>
                                        <li class="page-item <%# ((GridView)Container.NamingContainer).PageIndex == 0 ? "disabled" : "" %>">
                                            <asp:LinkButton ID="lnkPrev" runat="server" CssClass="page-link" 
                                                CommandName="Page" CommandArgument="Prev" Text="上一頁" />
                                        </li>
                                        <li class="page-item active">
                                            <span class="page-link">
                                                第 <%# ((GridView)Container.NamingContainer).PageIndex + 1 %> 頁
                                            </span>
                                        </li>
                                        <li class="page-item <%# ((GridView)Container.NamingContainer).PageIndex >= ((GridView)Container.NamingContainer).PageCount - 1 ? "disabled" : "" %>">
                                            <asp:LinkButton ID="lnkNext" runat="server" CssClass="page-link" 
                                                CommandName="Page" CommandArgument="Next" Text="下一頁" />
                                        </li>
                                        <li class="page-item <%# ((GridView)Container.NamingContainer).PageIndex >= ((GridView)Container.NamingContainer).PageCount - 1 ? "disabled" : "" %>">
                                            <asp:LinkButton ID="lnkLast" runat="server" CssClass="page-link" 
                                                CommandName="Page" CommandArgument="Last" Text="最後一頁" />
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </PagerTemplate>
                    </asp:GridView>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/編輯職員模態視窗 -->
    <div class="modal fade" id="staffModal" tabindex="-1" aria-labelledby="staffModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="staffModalLabel">
                        <asp:Label ID="lblModalTitle" runat="server" Text="新增職員"></asp:Label>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hdnStaffId" runat="server" />
                    
                    <div class="row">
                        <!-- 基本資料 -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">基本資料</h6>
                            
                            <div class="mb-3">
                                <label for="<%= txtEmployeeCode.ClientID %>" class="form-label">員工編號 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtEmployeeCode" runat="server" CssClass="form-control" 
                                        placeholder="系統自動產生或手動輸入" MaxLength="50"></asp:TextBox>
                                    <asp:Button ID="btnGenerateCode" runat="server" Text="產生" CssClass="btn btn-outline-secondary"
                                        OnClick="btnGenerateCode_Click" CausesValidation="false" />
                                </div>
                                <asp:RequiredFieldValidator ID="rfvEmployeeCode" runat="server" 
                                    ControlToValidate="txtEmployeeCode" ErrorMessage="員工編號不能為空"
                                    CssClass="text-danger small" Display="Dynamic" ValidationGroup="StaffModal"></asp:RequiredFieldValidator>
                            </div>

                            <div class="mb-3">
                                <label for="<%= txtFullName.ClientID %>" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtFullName" runat="server" CssClass="form-control" 
                                    placeholder="請輸入姓名" MaxLength="100"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvFullName" runat="server" 
                                    ControlToValidate="txtFullName" ErrorMessage="姓名不能為空"
                                    CssClass="text-danger small" Display="Dynamic" ValidationGroup="StaffModal"></asp:RequiredFieldValidator>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= ddlGender.ClientID %>" class="form-label">性別 <span class="text-danger">*</span></label>
                                        <asp:DropDownList ID="ddlGender" runat="server" CssClass="form-select">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtDateOfBirth.ClientID %>" class="form-label">出生日期</label>
                                        <asp:TextBox ID="txtDateOfBirth" runat="server" CssClass="form-control" 
                                            TextMode="Date"></asp:TextBox>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="<%= txtHKID.ClientID %>" class="form-label">身份證號碼 <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtHKID" runat="server" CssClass="form-control" 
                                    placeholder="A123456(7)" MaxLength="20"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvHKID" runat="server" 
                                    ControlToValidate="txtHKID" ErrorMessage="身份證號碼不能為空"
                                    CssClass="text-danger small" Display="Dynamic" ValidationGroup="StaffModal"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <!-- 聯絡資料 -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">聯絡資料</h6>
                            
                            <div class="mb-3">
                                <label for="<%= txtPhone.ClientID %>" class="form-label">電話號碼</label>
                                <asp:TextBox ID="txtPhone" runat="server" CssClass="form-control" 
                                    placeholder="12345678" MaxLength="50"></asp:TextBox>
                                <small class="text-muted">此欄位將使用 AES-256 加密儲存</small>
                            </div>

                            <div class="mb-3">
                                <label for="<%= txtEmail.ClientID %>" class="form-label">電子郵件</label>
                                <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" 
                                    placeholder="<EMAIL>" MaxLength="100"></asp:TextBox>
                                <small class="text-muted">此欄位將使用 AES-256 加密儲存</small>
                            </div>

                            <div class="mb-3">
                                <label for="<%= txtAddress.ClientID %>" class="form-label">住址</label>
                                <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" 
                                    TextMode="MultiLine" Rows="3" MaxLength="500"
                                    placeholder="請輸入詳細住址"></asp:TextBox>
                                <small class="text-muted">此欄位將使用 AES-256 加密儲存</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtEmergencyContact.ClientID %>" class="form-label">緊急聯絡人</label>
                                        <asp:TextBox ID="txtEmergencyContact" runat="server" CssClass="form-control" 
                                            placeholder="聯絡人姓名" MaxLength="100"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtEmergencyPhone.ClientID %>" class="form-label">緊急聯絡電話</label>
                                        <asp:TextBox ID="txtEmergencyPhone" runat="server" CssClass="form-control" 
                                            placeholder="電話號碼" MaxLength="50"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr />

                    <div class="row">
                        <!-- 職位資料 -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">職位資料</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= ddlStaffTypeModal.ClientID %>" class="form-label">職員類型 <span class="text-danger">*</span></label>
                                        <asp:DropDownList ID="ddlStaffTypeModal" runat="server" CssClass="form-select">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= ddlEmploymentTypeModal.ClientID %>" class="form-label">聘用類型 <span class="text-danger">*</span></label>
                                        <asp:DropDownList ID="ddlEmploymentTypeModal" runat="server" CssClass="form-select">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtDepartment.ClientID %>" class="form-label">部門</label>
                                        <asp:TextBox ID="txtDepartment" runat="server" CssClass="form-control" 
                                            placeholder="部門名稱" MaxLength="50"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtPosition.ClientID %>" class="form-label">職位</label>
                                        <asp:TextBox ID="txtPosition" runat="server" CssClass="form-control" 
                                            placeholder="職位名稱" MaxLength="100"></asp:TextBox>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="<%= txtSpecialization.ClientID %>" class="form-label">專長領域</label>
                                <asp:TextBox ID="txtSpecialization" runat="server" CssClass="form-control" 
                                    TextMode="MultiLine" Rows="2" MaxLength="200"
                                    placeholder="例如：電腦應用、語言教學、運動訓練等"></asp:TextBox>
                            </div>
                        </div>

                        <!-- 合約資料 -->
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">合約資料</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtHireDate.ClientID %>" class="form-label">入職日期 <span class="text-danger">*</span></label>
                                        <asp:TextBox ID="txtHireDate" runat="server" CssClass="form-control" 
                                            TextMode="Date"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvHireDate" runat="server" 
                                            ControlToValidate="txtHireDate" ErrorMessage="入職日期不能為空"
                                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="StaffModal"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= ddlEmploymentStatusModal.ClientID %>" class="form-label">在職狀態</label>
                                        <asp:DropDownList ID="ddlEmploymentStatusModal" runat="server" CssClass="form-select">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtContractStartDate.ClientID %>" class="form-label">合約開始日期</label>
                                        <asp:TextBox ID="txtContractStartDate" runat="server" CssClass="form-control" 
                                            TextMode="Date"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtContractEndDate.ClientID %>" class="form-label">合約結束日期</label>
                                        <asp:TextBox ID="txtContractEndDate" runat="server" CssClass="form-control" 
                                            TextMode="Date"></asp:TextBox>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtBasicSalary.ClientID %>" class="form-label">基本薪資</label>
                                        <div class="input-group">
                                            <span class="input-group-text">HK$</span>
                                            <asp:TextBox ID="txtBasicSalary" runat="server" CssClass="form-control" 
                                                placeholder="0.00" TextMode="Number" step="0.01"></asp:TextBox>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="<%= txtHourlyRate.ClientID %>" class="form-label">時薪</label>
                                        <div class="input-group">
                                            <span class="input-group-text">HK$</span>
                                            <asp:TextBox ID="txtHourlyRate" runat="server" CssClass="form-control" 
                                                placeholder="0.00" TextMode="Number" step="0.01"></asp:TextBox>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="<%= txtBankAccount.ClientID %>" class="form-label">銀行帳戶</label>
                                <asp:TextBox ID="txtBankAccount" runat="server" CssClass="form-control" 
                                    placeholder="銀行名稱和帳戶號碼" MaxLength="100"></asp:TextBox>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="<%= txtRemarks.ClientID %>" class="form-label">備註</label>
                        <asp:TextBox ID="txtRemarks" runat="server" CssClass="form-control" 
                            TextMode="MultiLine" Rows="3" MaxLength="1000"
                            placeholder="其他相關資訊"></asp:TextBox>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <asp:CheckBox ID="chkIsActive" runat="server" CssClass="form-check-input" Checked="true" />
                            <label class="form-check-label" for="<%= chkIsActive.ClientID %>">
                                啟用此職員記錄
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnSaveStaff" runat="server" Text="儲存" CssClass="btn btn-primary"
                        OnClick="btnSaveStaff_Click" ValidationGroup="StaffModal" />
                </div>
            </div>
        </div>
    </div>

    <!-- 續約模態視窗 -->
    <div class="modal fade" id="renewModal" tabindex="-1" aria-labelledby="renewModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="renewModalLabel">
                        合約續約：<asp:Label ID="lblRenewStaffName" runat="server" Text="" CssClass="text-primary"></asp:Label>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hdnRenewStaffId" runat="server" />
                    
                    <div class="mb-3">
                        <label for="<%= txtRenewStartDate.ClientID %>" class="form-label">新合約開始日期 <span class="text-danger">*</span></label>
                        <asp:TextBox ID="txtRenewStartDate" runat="server" CssClass="form-control" 
                            TextMode="Date"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvRenewStartDate" runat="server" 
                            ControlToValidate="txtRenewStartDate" ErrorMessage="合約開始日期不能為空"
                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="RenewModal"></asp:RequiredFieldValidator>
                    </div>

                    <div class="mb-3">
                        <label for="<%= txtRenewEndDate.ClientID %>" class="form-label">新合約結束日期 <span class="text-danger">*</span></label>
                        <asp:TextBox ID="txtRenewEndDate" runat="server" CssClass="form-control" 
                            TextMode="Date"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvRenewEndDate" runat="server" 
                            ControlToValidate="txtRenewEndDate" ErrorMessage="合約結束日期不能為空"
                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="RenewModal"></asp:RequiredFieldValidator>
                        <asp:CompareValidator ID="cvRenewDates" runat="server" 
                            ControlToValidate="txtRenewEndDate" ControlToCompare="txtRenewStartDate"
                            ErrorMessage="合約結束日期必須晚於開始日期" Type="Date" Operator="GreaterThan"
                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="RenewModal"></asp:CompareValidator>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提醒：</strong>續約後職員狀態將自動更新為「在職」。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnConfirmRenew" runat="server" Text="確認續約" CssClass="btn btn-success"
                        OnClick="btnConfirmRenew_Click" ValidationGroup="RenewModal" />
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            // 初始化 DataTables
            if ($('#<%= gvStaff.ClientID %>').length && $('#<%= gvStaff.ClientID %> tbody tr').length > 0) {
                $('#<%= gvStaff.ClientID %>').DataTable({
                    "paging": false,
                    "searching": false,
                    "ordering": false,
                    "info": false,
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese-traditional.json"
                    }
                });
            }

            // 處理模態視窗顯示
            window.showStaffModal = function() {
                $('#staffModal').modal('show');
            };

            window.showRenewModal = function() {
                $('#renewModal').modal('show');
            };

            // 自動設定今天為預設入職日期
            if ($('#<%= txtHireDate.ClientID %>').val() === '') {
                var today = new Date().toISOString().split('T')[0];
                $('#<%= txtHireDate.ClientID %>').val(today);
            }

            // 職員類型變更時自動產生員工編號
            $('#<%= ddlStaffTypeModal.ClientID %>').change(function() {
                if ($('#<%= txtEmployeeCode.ClientID %>').val() === '') {
                    $('#<%= btnGenerateCode.ClientID %>').click();
                }
            });
        });

        // 顯示載入指示器
        function showLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }

        // 確認刪除
        function confirmDelete(staffName) {
            return confirm('確定要刪除職員「' + staffName + '」嗎？\n\n注意：此操作將會停用職員記錄，不會完全刪除。');
        }
    </script>
</asp:Content>