<%@ Page Title="職員檔案" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="StaffProfile.aspx.cs" Inherits="CWDECC_3S.Staff.StaffProfile" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題和操作按鈕 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="~/Staff/StaffManagement.aspx" runat="server">職員管理</a></li>
                        <li class="breadcrumb-item active" aria-current="page">職員檔案</li>
                    </ol>
                </nav>
                <h2>
                    <i class="fas fa-user text-primary me-2"></i>
                    <asp:Label ID="lblStaffName" runat="server" Text="職員檔案"></asp:Label>
                    <asp:Label ID="lblStaffCode" runat="server" CssClass="text-muted ms-2"></asp:Label>
                </h2>
            </div>
            <div>
                <asp:Button ID="btnEdit" runat="server" Text="編輯資料" CssClass="btn btn-warning"
                    OnClick="btnEdit_Click" />
                <asp:Button ID="btnBack" runat="server" Text="返回列表" CssClass="btn btn-outline-secondary"
                    OnClick="btnBack_Click" CausesValidation="false" />
                <asp:Button ID="btnPrint" runat="server" Text="列印檔案" CssClass="btn btn-outline-primary"
                    OnClientClick="window.print(); return false;" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <!-- 職員狀態警告 -->
        <asp:Panel ID="pnlStatusWarning" runat="server" Visible="false" CssClass="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <asp:Literal ID="ltlStatusWarning" runat="server"></asp:Literal>
        </asp:Panel>

        <div class="row">
            <!-- 左側：基本資料 -->
            <div class="col-lg-4">
                <!-- 基本資料卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-id-card me-2"></i>基本資料
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="avatar-placeholder bg-primary text-white rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                                 style="width: 100px; height: 100px; font-size: 2rem;">
                                <asp:Label ID="lblAvatarInitial" runat="server" Text="?"></asp:Label>
                            </div>
                            <h5 class="mt-2 mb-0">
                                <asp:Label ID="lblProfileName" runat="server" Text=""></asp:Label>
                            </h5>
                            <p class="text-muted">
                                <asp:Label ID="lblProfileCode" runat="server" Text=""></asp:Label>
                            </p>
                            <asp:Label ID="lblEmploymentStatusBadge" runat="server" CssClass="badge"></asp:Label>
                        </div>

                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" style="width: 35%;">性別</td>
                                <td><asp:Label ID="lblGender" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">年齡</td>
                                <td><asp:Label ID="lblAge" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">身份證</td>
                                <td><asp:Label ID="lblHKID" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">職員類型</td>
                                <td><asp:Label ID="lblStaffTypeBadge" runat="server" CssClass="badge"></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">聘用類型</td>
                                <td><asp:Label ID="lblEmploymentType" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">部門</td>
                                <td><asp:Label ID="lblDepartment" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">職位</td>
                                <td><asp:Label ID="lblPosition" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">服務年資</td>
                                <td><asp:Label ID="lblServiceYears" runat="server" Text=""></asp:Label></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 聯絡資料卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-address-book me-2"></i>聯絡資料
                            <small class="text-muted">（已加密保護）</small>
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" style="width: 25%;">
                                    <i class="fas fa-phone text-primary me-1"></i>電話
                                </td>
                                <td>
                                    <asp:Label ID="lblPhone" runat="server" Text=""></asp:Label>
                                    <asp:LinkButton ID="lnkCallPhone" runat="server" CssClass="ms-2 text-success" Visible="false">
                                        <i class="fas fa-phone-alt"></i>
                                    </asp:LinkButton>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">
                                    <i class="fas fa-envelope text-primary me-1"></i>電郵
                                </td>
                                <td>
                                    <asp:Label ID="lblEmail" runat="server" Text=""></asp:Label>
                                    <asp:LinkButton ID="lnkSendEmail" runat="server" CssClass="ms-2 text-info" Visible="false">
                                        <i class="fas fa-paper-plane"></i>
                                    </asp:LinkButton>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">
                                    <i class="fas fa-map-marker-alt text-primary me-1"></i>住址
                                </td>
                                <td><asp:Label ID="lblAddress" runat="server" Text=""></asp:Label></td>
                            </tr>
                        </table>

                        <hr />

                        <h6 class="text-muted mb-2">緊急聯絡人</h6>
                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" style="width: 25%;">姓名</td>
                                <td><asp:Label ID="lblEmergencyContact" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">電話</td>
                                <td><asp:Label ID="lblEmergencyPhone" runat="server" Text=""></asp:Label></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 中間：合約和薪資資訊 -->
            <div class="col-lg-4">
                <!-- 合約資訊卡片 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-contract me-2"></i>合約資訊
                        </h5>
                        <asp:Button ID="btnRenewContract" runat="server" Text="續約" CssClass="btn btn-sm btn-outline-success"
                            OnClick="btnRenewContract_Click" Visible="false" />
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" style="width: 35%;">入職日期</td>
                                <td><asp:Label ID="lblHireDate" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">合約開始</td>
                                <td><asp:Label ID="lblContractStartDate" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">合約結束</td>
                                <td>
                                    <asp:Label ID="lblContractEndDate" runat="server" Text=""></asp:Label>
                                    <asp:Label ID="lblContractStatus" runat="server" CssClass="badge ms-2"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">合約天數</td>
                                <td><asp:Label ID="lblContractDays" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">剩餘天數</td>
                                <td><asp:Label ID="lblRemainingDays" runat="server" Text=""></asp:Label></td>
                            </tr>
                        </table>

                        <asp:Panel ID="pnlContractAlert" runat="server" Visible="false" CssClass="alert alert-sm">
                            <asp:Literal ID="ltlContractAlert" runat="server"></asp:Literal>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 薪資資訊卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>薪資資訊
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" style="width: 35%;">基本薪資</td>
                                <td><asp:Label ID="lblBasicSalary" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">時薪</td>
                                <td><asp:Label ID="lblHourlyRate" runat="server" Text=""></asp:Label></td>
                            </tr>
                            <tr>
                                <td class="text-muted">銀行帳戶</td>
                                <td><asp:Label ID="lblBankAccount" runat="server" Text=""></asp:Label></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 專長和備註 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star me-2"></i>專長和備註
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6 class="text-muted">專長領域</h6>
                        <p><asp:Label ID="lblSpecialization" runat="server" Text=""></asp:Label></p>

                        <h6 class="text-muted">備註</h6>
                        <p><asp:Label ID="lblRemarks" runat="server" Text=""></asp:Label></p>
                    </div>
                </div>
            </div>

            <!-- 右側：活動記錄和工時統計 -->
            <div class="col-lg-4">
                <!-- 工時統計卡片 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>工時統計
                        </h5>
                        <div>
                            <asp:DropDownList ID="ddlStatsMonth" runat="server" CssClass="form-select form-select-sm"
                                OnSelectedIndexChanged="ddlStatsMonth_SelectedIndexChanged" AutoPostBack="true">
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary mb-0">
                                        <asp:Label ID="lblTotalTeachingHours" runat="server" Text="0"></asp:Label>
                                    </h4>
                                    <small class="text-muted">授課時數</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info mb-0">
                                    <asp:Label ID="lblTotalPreparationHours" runat="server" Text="0"></asp:Label>
                                </h4>
                                <small class="text-muted">準備時數</small>
                            </div>
                        </div>

                        <hr />

                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-success mb-0">
                                        <asp:Label ID="lblTotalActivities" runat="server" Text="0"></asp:Label>
                                    </h4>
                                    <small class="text-muted">參與活動</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning mb-0">
                                    <asp:Label ID="lblTotalFees" runat="server" Text="$0"></asp:Label>
                                </h4>
                                <small class="text-muted">總費用</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 近期活動記錄 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>近期活動記錄
                        </h5>
                        <asp:LinkButton ID="lnkViewAllActivities" runat="server" CssClass="btn btn-sm btn-outline-primary"
                            OnClick="lnkViewAllActivities_Click">
                            查看全部
                        </asp:LinkButton>
                    </div>
                    <div class="card-body">
                        <asp:Repeater ID="rptRecentActivities" runat="server">
                            <HeaderTemplate>
                                <div class="timeline">
                            </HeaderTemplate>
                            <ItemTemplate>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1"><%# Eval("Activity.Title") %></h6>
                                        <p class="text-muted small mb-1">
                                            <%# Eval("Activity.StartDate", "{0:yyyy-MM-dd}") %> | 
                                            <%# GetActivityRoleDisplayText(Eval("Role").ToString()) %>
                                        </p>
                                        <div class="d-flex justify-content-between text-sm">
                                            <span><i class="fas fa-clock me-1"></i>授課: <%# Eval("TeachingHours") %>h</span>
                                            <span><i class="fas fa-prep me-1"></i>準備: <%# Eval("PreparationHours") %>h</span>
                                        </div>
                                    </div>
                                </div>
                            </ItemTemplate>
                            <FooterTemplate>
                                </div>
                            </FooterTemplate>
                        </asp:Repeater>

                        <asp:Panel ID="pnlNoActivities" runat="server" Visible="false" CssClass="text-center text-muted py-3">
                            <i class="fas fa-calendar-times fa-2x mb-2"></i>
                            <p>尚未參與任何活動</p>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools me-2"></i>快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnAddActivity" runat="server" Text="新增活動記錄" CssClass="btn btn-outline-primary btn-sm"
                                OnClick="btnAddActivity_Click" />
                            <asp:Button ID="btnViewTimesheet" runat="server" Text="查看工時記錄" CssClass="btn btn-outline-info btn-sm"
                                OnClick="btnViewTimesheet_Click" />
                            <asp:Button ID="btnGenerateReport" runat="server" Text="產生工時報告" CssClass="btn btn-outline-success btn-sm"
                                OnClick="btnGenerateReport_Click" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 詳細活動記錄表格 -->
        <asp:Panel ID="pnlAllActivities" runat="server" Visible="false">
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>完整活動記錄
                        <asp:Button ID="btnHideActivities" runat="server" Text="收起" CssClass="btn btn-sm btn-outline-secondary float-end"
                            OnClick="btnHideActivities_Click" CausesValidation="false" />
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <asp:GridView ID="gvAllActivities" runat="server" CssClass="table table-striped table-hover"
                            AutoGenerateColumns="False" EmptyDataText="沒有活動記錄"
                            OnRowCommand="gvAllActivities_RowCommand" OnRowDataBound="gvAllActivities_RowDataBound"
                            AllowPaging="True" PageSize="10" OnPageIndexChanging="gvAllActivities_PageIndexChanging">
                            <Columns>
                                <asp:BoundField DataField="Activity.Title" HeaderText="活動名稱" SortExpression="Activity.Title">
                                    <HeaderStyle Width="25%" />
                                </asp:BoundField>
                                <asp:BoundField DataField="Activity.StartDate" HeaderText="活動日期" SortExpression="Activity.StartDate" 
                                    DataFormatString="{0:yyyy-MM-dd}">
                                    <HeaderStyle Width="12%" />
                                </asp:BoundField>
                                <asp:TemplateField HeaderText="參與角色">
                                    <ItemTemplate>
                                        <span class='badge bg-<%# GetActivityRoleBadgeColor(Eval("Role").ToString()) %>'>
                                            <%# GetActivityRoleDisplayText(Eval("Role").ToString()) %>
                                        </span>
                                    </ItemTemplate>
                                    <HeaderStyle Width="10%" />
                                </asp:TemplateField>
                                <asp:BoundField DataField="TeachingHours" HeaderText="授課時數" DataFormatString="{0:0.0}h">
                                    <HeaderStyle Width="10%" />
                                </asp:BoundField>
                                <asp:BoundField DataField="PreparationHours" HeaderText="準備時數" DataFormatString="{0:0.0}h">
                                    <HeaderStyle Width="10%" />
                                </asp:BoundField>
                                <asp:BoundField DataField="Fee" HeaderText="費用" DataFormatString="HK${0:0.00}">
                                    <HeaderStyle Width="10%" />
                                </asp:BoundField>
                                <asp:BoundField DataField="RecordDate" HeaderText="記錄時間" DataFormatString="{0:yyyy-MM-dd}">
                                    <HeaderStyle Width="12%" />
                                </asp:BoundField>
                                <asp:TemplateField HeaderText="操作">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="btnEditActivity" runat="server" 
                                            CommandName="EditActivity" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-warning btn-sm" 
                                            ToolTip="編輯">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="11%" />
                                </asp:TemplateField>
                            </Columns>
                            <HeaderStyle CssClass="table-dark" />
                            <EmptyDataRowStyle CssClass="text-center text-muted" />
                            <PagerStyle CssClass="pagination-wrapper" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
        </asp:Panel>
    </div>

    <!-- 新增活動記錄模態視窗 -->
    <div class="modal fade" id="activityModal" tabindex="-1" aria-labelledby="activityModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="activityModalLabel">新增活動記錄</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hdnActivityRecordId" runat="server" />
                    
                    <div class="mb-3">
                        <label for="<%= ddlActivity.ClientID %>" class="form-label">活動 <span class="text-danger">*</span></label>
                        <asp:DropDownList ID="ddlActivity" runat="server" CssClass="form-select">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ID="rfvActivity" runat="server" 
                            ControlToValidate="ddlActivity" InitialValue="" ErrorMessage="請選擇活動"
                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                    </div>

                    <div class="mb-3">
                        <label for="<%= ddlRole.ClientID %>" class="form-label">參與角色 <span class="text-danger">*</span></label>
                        <asp:DropDownList ID="ddlRole" runat="server" CssClass="form-select">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ID="rfvRole" runat="server" 
                            ControlToValidate="ddlRole" InitialValue="" ErrorMessage="請選擇參與角色"
                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="<%= txtTeachingHours.ClientID %>" class="form-label">授課時數</label>
                                <asp:TextBox ID="txtTeachingHours" runat="server" CssClass="form-control" 
                                    TextMode="Number" step="0.1" min="0" placeholder="0.0"></asp:TextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="<%= txtPreparationHours.ClientID %>" class="form-label">準備時數</label>
                                <asp:TextBox ID="txtPreparationHours" runat="server" CssClass="form-control" 
                                    TextMode="Number" step="0.1" min="0" placeholder="0.0"></asp:TextBox>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="<%= txtFee.ClientID %>" class="form-label">費用</label>
                        <div class="input-group">
                            <span class="input-group-text">HK$</span>
                            <asp:TextBox ID="txtFee" runat="server" CssClass="form-control" 
                                TextMode="Number" step="0.01" min="0" placeholder="0.00"></asp:TextBox>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="<%= txtActivityRemarks.ClientID %>" class="form-label">備註</label>
                        <asp:TextBox ID="txtActivityRemarks" runat="server" CssClass="form-control" 
                            TextMode="MultiLine" Rows="3" MaxLength="500"
                            placeholder="活動相關備註"></asp:TextBox>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnSaveActivity" runat="server" Text="儲存" CssClass="btn btn-primary"
                        OnClick="btnSaveActivity_Click" ValidationGroup="ActivityModal" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-placeholder {
            font-weight: bold;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -38px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -32px;
            top: 17px;
            bottom: -20px;
            width: 1px;
            background-color: #dee2e6;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border-left: 3px solid #007bff;
        }
        
        @media print {
            .btn, .breadcrumb, .alert {
                display: none !important;
            }
            
            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 處理模態視窗顯示
            window.showActivityModal = function() {
                $('#activityModal').modal('show');
            };

            // 電話和電郵的點擊處理
            $('#<%= lnkCallPhone.ClientID %>').click(function(e) {
                e.preventDefault();
                var phone = $('#<%= lblPhone.ClientID %>').text();
                if (phone) {
                    window.location.href = 'tel:' + phone;
                }
            });

            $('#<%= lnkSendEmail.ClientID %>').click(function(e) {
                e.preventDefault();
                var email = $('#<%= lblEmail.ClientID %>').text();
                if (email) {
                    window.location.href = 'mailto:' + email;
                }
            });
        });

        // 顯示載入指示器
        function showLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }
    </script>
</asp:Content>