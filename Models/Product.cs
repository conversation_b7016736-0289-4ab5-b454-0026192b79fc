using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 產品模型 - POS 系統產品管理
    /// </summary>
    [Table("Products")]
    public class Product
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "產品名稱")]
        public string ProductName { get; set; }

        [StringLength(500)]
        [Display(Name = "產品描述")]
        public string Description { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "單價")]
        [Range(0.01, 99999.99, ErrorMessage = "單價必須大於 0")]
        public decimal UnitPrice { get; set; }

        [Required]
        [Display(Name = "庫存數量")]
        [Range(0, int.MaxValue, ErrorMessage = "庫存數量不能為負數")]
        public int StockQuantity { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "產品類別")]
        public string Category { get; set; }

        [StringLength(20)]
        [Display(Name = "產品編號")]
        public string ProductCode { get; set; }

        [StringLength(500)]
        [Display(Name = "產品圖片路徑")]
        public string ImagePath { get; set; }

        [Display(Name = "最低庫存警告")]
        public int? MinStockLevel { get; set; }

        [Display(Name = "是否啟用")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "是否允許銷售")]
        public bool IsAvailable { get; set; } = true;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string ModifiedBy { get; set; }

        [StringLength(1000)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 導航屬性
        public virtual ICollection<SalesItem> SalesItems { get; set; }

        public Product()
        {
            SalesItems = new HashSet<SalesItem>();
        }

        // 業務邏輯方法

        /// <summary>
        /// 檢查庫存是否足夠
        /// </summary>
        /// <param name="quantity">需要的數量</param>
        /// <returns>是否有足夠庫存</returns>
        public bool HasSufficientStock(int quantity)
        {
            return IsActive && IsAvailable && StockQuantity >= quantity;
        }

        /// <summary>
        /// 減少庫存
        /// </summary>
        /// <param name="quantity">減少的數量</param>
        /// <param name="modifiedBy">修改者</param>
        public void ReduceStock(int quantity, string modifiedBy)
        {
            if (quantity < 0)
                throw new ArgumentException("減少數量不能為負數");

            if (StockQuantity < quantity)
                throw new InvalidOperationException($"庫存不足，當前庫存：{StockQuantity}，需要：{quantity}");

            StockQuantity -= quantity;
            ModifiedDate = DateTime.UtcNow;
            ModifiedBy = modifiedBy;
        }

        /// <summary>
        /// 增加庫存（退貨時使用）
        /// </summary>
        /// <param name="quantity">增加的數量</param>
        /// <param name="modifiedBy">修改者</param>
        public void IncreaseStock(int quantity, string modifiedBy)
        {
            if (quantity < 0)
                throw new ArgumentException("增加數量不能為負數");

            StockQuantity += quantity;
            ModifiedDate = DateTime.UtcNow;
            ModifiedBy = modifiedBy;
        }

        /// <summary>
        /// 是否需要庫存警告
        /// </summary>
        /// <returns>是否低於最低庫存</returns>
        public bool IsLowStock()
        {
            return MinStockLevel.HasValue && StockQuantity <= MinStockLevel.Value;
        }

        /// <summary>
        /// 計算銷售金額
        /// </summary>
        /// <param name="quantity">銷售數量</param>
        /// <returns>銷售金額</returns>
        public decimal CalculateSalesAmount(int quantity)
        {
            return UnitPrice * quantity;
        }

        /// <summary>
        /// 驗證產品資料
        /// </summary>
        /// <returns>驗證錯誤列表</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(ProductName))
                errors.Add("產品名稱不能為空");

            if (UnitPrice <= 0)
                errors.Add("單價必須大於 0");

            if (StockQuantity < 0)
                errors.Add("庫存數量不能為負數");

            if (string.IsNullOrWhiteSpace(Category))
                errors.Add("產品類別不能為空");

            if (MinStockLevel.HasValue && MinStockLevel.Value < 0)
                errors.Add("最低庫存警告不能為負數");

            return errors;
        }

        /// <summary>
        /// 產品狀態顯示文字
        /// </summary>
        public string StatusDisplayText
        {
            get
            {
                if (!IsActive) return "已停用";
                if (!IsAvailable) return "不可銷售";
                if (StockQuantity <= 0) return "缺貨";
                if (IsLowStock()) return "庫存不足";
                return "正常";
            }
        }

        /// <summary>
        /// 產品狀態 CSS 類別
        /// </summary>
        public string StatusCssClass
        {
            get
            {
                if (!IsActive) return "badge bg-secondary";
                if (!IsAvailable) return "badge bg-warning";
                if (StockQuantity <= 0) return "badge bg-danger";
                if (IsLowStock()) return "badge bg-warning";
                return "badge bg-success";
            }
        }
    }

    /// <summary>
    /// 產品類別常數
    /// </summary>
    public static class ProductCategories
    {
        public const string Food = "食品";
        public const string Beverage = "飲料";
        public const string Stationery = "文具";
        public const string Clothing = "服裝";
        public const string Accessory = "配件";
        public const string Book = "書籍";
        public const string Gift = "禮品";
        public const string Other = "其他";

        /// <summary>
        /// 取得所有產品類別
        /// </summary>
        /// <returns>產品類別字典</returns>
        public static Dictionary<string, string> GetAll()
        {
            return new Dictionary<string, string>
            {
                { Food, "食品" },
                { Beverage, "飲料" },
                { Stationery, "文具" },
                { Clothing, "服裝" },
                { Accessory, "配件" },
                { Book, "書籍" },
                { Gift, "禮品" },
                { Other, "其他" }
            };
        }

        /// <summary>
        /// 取得類別列表（供下拉選單使用）
        /// </summary>
        /// <returns>類別列表</returns>
        public static List<string> GetCategoryList()
        {
            return GetAll().Keys.ToList();
        }
    }
}