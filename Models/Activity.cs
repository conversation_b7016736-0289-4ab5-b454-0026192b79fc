using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 活動資料模型 - 管理活動資訊、報名狀態與參與者
    /// </summary>
    [Table("Activities")]
    public class Activity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "活動名稱")]
        public string ActivityName { get; set; }

        [StringLength(1000)]
        [Display(Name = "活動說明")]
        public string Description { get; set; }

        [Required]
        [Column(TypeName = "DATE")]
        [Display(Name = "活動日期")]
        public DateTime ActivityDate { get; set; }

        [Required]
        [Column(TypeName = "TIME")]
        [Display(Name = "開始時間")]
        public TimeSpan StartTime { get; set; }

        [Column(TypeName = "TIME")]
        [Display(Name = "結束時間")]
        public TimeSpan? EndTime { get; set; }

        [Required]
        [Display(Name = "最大名額")]
        public int MaxParticipants { get; set; }

        [Display(Name = "已報名人數")]
        public int CurrentParticipants { get; set; } = 0;

        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "活動費用")]
        public decimal? ActivityFee { get; set; }

        [StringLength(50)]
        [Display(Name = "對象限制")]
        public string TargetAudience { get; set; } // All, Members, Students, Seniors, Family

        [Required]
        [StringLength(20)]
        [Display(Name = "活動狀態")]
        public string Status { get; set; } // Registration, Full, Closed, Cancelled

        [StringLength(200)]
        [Display(Name = "舉辦地點")]
        public string Venue { get; set; }

        [StringLength(100)]
        [Display(Name = "聯絡人")]
        public string ContactPerson { get; set; }

        [StringLength(50)]
        [Display(Name = "聯絡電話")]
        public string ContactPhone { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "報名截止日期")]
        public DateTime? RegistrationDeadline { get; set; }

        [StringLength(1000)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 系統欄位
        [Display(Name = "啟用狀態")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? UpdatedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string UpdatedBy { get; set; }

        // 導航屬性
        public virtual ICollection<ActivityRegistration> ActivityRegistrations { get; set; }

        public Activity()
        {
            ActivityRegistrations = new HashSet<ActivityRegistration>();
        }

        #region 非映射屬性

        /// <summary>
        /// 活動日期時間
        /// </summary>
        [NotMapped]
        public DateTime ActivityDateTime
        {
            get
            {
                return ActivityDate.Date.Add(StartTime);
            }
        }

        /// <summary>
        /// 剩餘名額
        /// </summary>
        [NotMapped]
        public int AvailableSpots
        {
            get
            {
                return Math.Max(0, MaxParticipants - CurrentParticipants);
            }
        }

        /// <summary>
        /// 是否已滿額
        /// </summary>
        [NotMapped]
        public bool IsFull
        {
            get
            {
                return CurrentParticipants >= MaxParticipants;
            }
        }

        /// <summary>
        /// 是否已結束
        /// </summary>
        [NotMapped]
        public bool IsCompleted
        {
            get
            {
                return ActivityDateTime < DateTime.Now;
            }
        }

        /// <summary>
        /// 是否已過報名截止日期
        /// </summary>
        [NotMapped]
        public bool IsRegistrationClosed
        {
            get
            {
                return RegistrationDeadline.HasValue && RegistrationDeadline.Value.Date < DateTime.Today;
            }
        }

        /// <summary>
        /// 活動狀態顯示文字
        /// </summary>
        [NotMapped]
        public string StatusDisplayText
        {
            get
            {
                switch (Status)
                {
                    case ActivityConstants.Status.Registration:
                        return "報名中";
                    case ActivityConstants.Status.Full:
                        return "已滿額";
                    case ActivityConstants.Status.Closed:
                        return "已結束";
                    case ActivityConstants.Status.Cancelled:
                        return "已取消";
                    default:
                        return Status;
                }
            }
        }

        /// <summary>
        /// 對象限制顯示文字
        /// </summary>
        [NotMapped]
        public string TargetAudienceDisplayText
        {
            get
            {
                switch (TargetAudience)
                {
                    case ActivityConstants.TargetAudience.All:
                        return "不限";
                    case ActivityConstants.TargetAudience.Members:
                        return "會員限定";
                    case ActivityConstants.TargetAudience.Students:
                        return "學生限定";
                    case ActivityConstants.TargetAudience.Seniors:
                        return "長者限定";
                    case ActivityConstants.TargetAudience.Family:
                        return "家庭限定";
                    default:
                        return TargetAudience ?? "不限";
                }
            }
        }

        /// <summary>
        /// 格式化顯示名稱
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{ActivityName} ({ActivityDate:MM/dd})";

        /// <summary>
        /// 格式化時間顯示
        /// </summary>
        [NotMapped]
        public string TimeDisplay
        {
            get
            {
                if (EndTime.HasValue)
                {
                    return $"{StartTime:hh\\:mm} - {EndTime.Value:hh\\:mm}";
                }
                else
                {
                    return $"{StartTime:hh\\:mm} 開始";
                }
            }
        }

        #endregion

        #region 業務方法

        /// <summary>
        /// 更新活動狀態
        /// </summary>
        public void UpdateStatus()
        {
            if (!IsActive)
            {
                Status = ActivityConstants.Status.Cancelled;
            }
            else if (IsCompleted)
            {
                Status = ActivityConstants.Status.Closed;
            }
            else if (IsFull)
            {
                Status = ActivityConstants.Status.Full;
            }
            else if (IsRegistrationClosed)
            {
                Status = ActivityConstants.Status.Closed;
            }
            else
            {
                Status = ActivityConstants.Status.Registration;
            }

            UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// 檢查是否可以報名
        /// </summary>
        /// <returns>是否可以報名</returns>
        public bool CanRegister()
        {
            return IsActive && 
                   !IsFull && 
                   !IsCompleted && 
                   !IsRegistrationClosed &&
                   Status == ActivityConstants.Status.Registration;
        }

        /// <summary>
        /// 增加參與者
        /// </summary>
        /// <param name="count">增加數量</param>
        public void AddParticipants(int count = 1)
        {
            CurrentParticipants = Math.Min(MaxParticipants, CurrentParticipants + count);
            UpdateStatus();
        }

        /// <summary>
        /// 減少參與者
        /// </summary>
        /// <param name="count">減少數量</param>
        public void RemoveParticipants(int count = 1)
        {
            CurrentParticipants = Math.Max(0, CurrentParticipants - count);
            UpdateStatus();
        }

        /// <summary>
        /// 驗證活動資料
        /// </summary>
        /// <returns>驗證結果</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(ActivityName))
                errors.Add("活動名稱不能為空");

            if (ActivityDate < DateTime.Today)
                errors.Add("活動日期不能早於今天");

            if (MaxParticipants <= 0)
                errors.Add("最大名額必須大於0");

            if (CurrentParticipants > MaxParticipants)
                errors.Add("已報名人數不能超過最大名額");

            if (EndTime.HasValue && EndTime.Value <= StartTime)
                errors.Add("結束時間必須晚於開始時間");

            if (RegistrationDeadline.HasValue && RegistrationDeadline.Value.Date > ActivityDate.Date)
                errors.Add("報名截止日期不能晚於活動日期");

            if (ActivityFee.HasValue && ActivityFee.Value < 0)
                errors.Add("活動費用不能為負數");

            return errors;
        }

        #endregion
    }

    /// <summary>
    /// 活動報名記錄
    /// </summary>
    [Table("ActivityRegistrations")]
    public class ActivityRegistration
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ActivityId { get; set; }

        [Required]
        public int MemberId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "報名狀態")]
        public string RegistrationStatus { get; set; } // Registered, Confirmed, Cancelled, Attended, NoShow

        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "實際費用")]
        public decimal? ActualFee { get; set; }

        [StringLength(20)]
        [Display(Name = "付款方式")]
        public string PaymentMethod { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "付款日期")]
        public DateTime? PaymentDate { get; set; }

        [StringLength(500)]
        [Display(Name = "特殊需求")]
        public string SpecialRequirements { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        [Display(Name = "報名時間")]
        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        [StringLength(128)]
        [Display(Name = "報名處理者")]
        public string ProcessedBy { get; set; }

        [Display(Name = "確認時間")]
        public DateTime? ConfirmationDate { get; set; }

        [StringLength(128)]
        [Display(Name = "確認者")]
        public string ConfirmedBy { get; set; }

        // 導航屬性
        [ForeignKey("ActivityId")]
        public virtual Activity Activity { get; set; }

        [ForeignKey("MemberId")]
        public virtual Member Member { get; set; }

        #region 非映射屬性

        /// <summary>
        /// 報名狀態顯示文字
        /// </summary>
        [NotMapped]
        public string RegistrationStatusDisplayText
        {
            get
            {
                switch (RegistrationStatus)
                {
                    case ActivityConstants.RegistrationStatus.Registered:
                        return "已報名";
                    case ActivityConstants.RegistrationStatus.Confirmed:
                        return "已確認";
                    case ActivityConstants.RegistrationStatus.Cancelled:
                        return "已取消";
                    case ActivityConstants.RegistrationStatus.Attended:
                        return "已出席";
                    case ActivityConstants.RegistrationStatus.NoShow:
                        return "缺席";
                    default:
                        return RegistrationStatus;
                }
            }
        }

        /// <summary>
        /// 是否已付款
        /// </summary>
        [NotMapped]
        public bool IsPaid
        {
            get
            {
                return PaymentDate.HasValue && !string.IsNullOrEmpty(PaymentMethod);
            }
        }

        #endregion
    }

    /// <summary>
    /// 活動搜尋條件
    /// </summary>
    public class ActivitySearchCriteria
    {
        public string Keyword { get; set; }
        public string Status { get; set; }
        public string TargetAudience { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public decimal? MinFee { get; set; }
        public decimal? MaxFee { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "ActivityDate";
        public string SortDirection { get; set; } = "ASC";
    }

    /// <summary>
    /// 活動統計資訊
    /// </summary>
    public class ActivityStatistics
    {
        public int TotalActivities { get; set; }
        public int UpcomingActivities { get; set; }
        public int OngoingRegistrations { get; set; }
        public int CompletedActivities { get; set; }
        public int TotalParticipants { get; set; }
        public decimal TotalRevenue { get; set; }
        public int FullActivities { get; set; }
        public double AverageParticipationRate { get; set; }
    }

    /// <summary>
    /// 活動操作結果
    /// </summary>
    public class ActivityOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public int? ActivityId { get; set; }
        public object Data { get; set; }
    }

    /// <summary>
    /// 活動常數定義
    /// </summary>
    public static class ActivityConstants
    {
        public static class Status
        {
            public const string Registration = "Registration";
            public const string Full = "Full";
            public const string Closed = "Closed";
            public const string Cancelled = "Cancelled";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Registration, "報名中" },
                    { Full, "已滿額" },
                    { Closed, "已結束" },
                    { Cancelled, "已取消" }
                };
            }
        }

        public static class RegistrationStatus
        {
            public const string Registered = "Registered";
            public const string Confirmed = "Confirmed";
            public const string Cancelled = "Cancelled";
            public const string Attended = "Attended";
            public const string NoShow = "NoShow";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Registered, "已報名" },
                    { Confirmed, "已確認" },
                    { Cancelled, "已取消" },
                    { Attended, "已出席" },
                    { NoShow, "缺席" }
                };
            }
        }

        public static class TargetAudience
        {
            public const string All = "All";
            public const string Members = "Members";
            public const string Students = "Students";
            public const string Seniors = "Seniors";
            public const string Family = "Family";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { All, "不限" },
                    { Members, "會員限定" },
                    { Students, "學生限定" },
                    { Seniors, "長者限定" },
                    { Family, "家庭限定" }
                };
            }
        }

        public static class PaymentMethods
        {
            public const string Cash = "Cash";
            public const string Octopus = "Octopus";
            public const string CreditCard = "CreditCard";
            public const string BankTransfer = "BankTransfer";
            public const string Free = "Free";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Cash, "現金" },
                    { Octopus, "八達通" },
                    { CreditCard, "信用卡" },
                    { BankTransfer, "轉賬" },
                    { Free, "免費" }
                };
            }
        }
    }
}