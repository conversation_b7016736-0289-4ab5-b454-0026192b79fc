using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 系統模組模型 - 定義系統功能模組
    /// </summary>
    [Table("SystemModules")]
    public class SystemModule
    {
        [Key]
        [StringLength(100)]
        public string ModuleName { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "顯示名稱")]
        public string DisplayName { get; set; }

        [StringLength(500)]
        [Display(Name = "模組描述")]
        public string Description { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "模組分類")]
        public string Category { get; set; }

        [StringLength(50)]
        [Display(Name = "圖示")]
        public string Icon { get; set; }

        [StringLength(200)]
        [Display(Name = "模組 URL")]
        public string ModuleUrl { get; set; }

        [Display(Name = "排序順序")]
        public int SortOrder { get; set; } = 0;

        [Display(Name = "啟用狀態")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "需要驗證")]
        public bool RequireAuthentication { get; set; } = true;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string ModifiedBy { get; set; }

        [StringLength(1000)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 導航屬性
        public virtual ICollection<RolePermission> RolePermissions { get; set; }

        public SystemModule()
        {
            RolePermissions = new HashSet<RolePermission>();
        }

        public SystemModule(string moduleName, string displayName, string category) : this()
        {
            ModuleName = moduleName;
            DisplayName = displayName;
            Category = category;
        }
    }

    /// <summary>
    /// 系統模組分類定義
    /// </summary>
    public static class ModuleCategories
    {
        public const string Management = "管理功能";
        public const string Member = "會員管理";
        public const string Activity = "活動管理";
        public const string Meal = "膳食管理";
        public const string Report = "報告統計";
        public const string System = "系統管理";
        public const string Security = "安全管理";
        public const string Dashboard = "儀表板";

        /// <summary>
        /// 取得所有分類列表
        /// </summary>
        /// <returns>分類字典</returns>
        public static Dictionary<string, string> GetAll()
        {
            return new Dictionary<string, string>
            {
                { Dashboard, "儀表板" },
                { Member, "會員管理" },
                { Activity, "活動管理" },
                { Meal, "膳食管理" },
                { Report, "報告統計" },
                { Management, "管理功能" },
                { System, "系統管理" },
                { Security, "安全管理" }
            };
        }
    }

    /// <summary>
    /// 預設系統模組定義
    /// </summary>
    public static class DefaultModules
    {
        /// <summary>
        /// 取得預設系統模組列表
        /// </summary>
        /// <returns>模組列表</returns>
        public static List<SystemModule> GetDefaultModules()
        {
            return new List<SystemModule>
            {
                // 儀表板
                new SystemModule("Dashboard", "儀表板", ModuleCategories.Dashboard)
                {
                    Description = "系統主控台，顯示重要統計資訊",
                    Icon = "fas fa-tachometer-alt",
                    ModuleUrl = "~/Dashboard/",
                    SortOrder = 1
                },

                // 會員管理
                new SystemModule("Members", "會員管理", ModuleCategories.Member)
                {
                    Description = "管理會員資料、註冊、查詢等功能",
                    Icon = "fas fa-users",
                    ModuleUrl = "~/Members/",
                    SortOrder = 10
                },
                new SystemModule("MemberRegistration", "會員註冊", ModuleCategories.Member)
                {
                    Description = "新增會員註冊功能",
                    Icon = "fas fa-user-plus",
                    ModuleUrl = "~/Members/Register.aspx",
                    SortOrder = 11
                },
                new SystemModule("MemberProfiles", "會員檔案", ModuleCategories.Member)
                {
                    Description = "會員個人檔案管理",
                    Icon = "fas fa-address-card",
                    ModuleUrl = "~/Members/Profiles/",
                    SortOrder = 12
                },

                // 活動管理
                new SystemModule("Activities", "活動管理", ModuleCategories.Activity)
                {
                    Description = "管理學苑活動、課程安排等",
                    Icon = "fas fa-calendar-alt",
                    ModuleUrl = "~/Activities/",
                    SortOrder = 20
                },
                new SystemModule("ActivityScheduling", "活動排程", ModuleCategories.Activity)
                {
                    Description = "活動時間安排和場地管理",
                    Icon = "fas fa-calendar-check",
                    ModuleUrl = "~/Activities/Schedule/",
                    SortOrder = 21
                },
                new SystemModule("ActivityRegistration", "活動報名", ModuleCategories.Activity)
                {
                    Description = "會員活動報名管理",
                    Icon = "fas fa-clipboard-list",
                    ModuleUrl = "~/Activities/Registration/",
                    SortOrder = 22
                },

                // 膳食管理
                new SystemModule("MealOrdering", "膳食訂購", ModuleCategories.Meal)
                {
                    Description = "會員膳食訂購服務",
                    Icon = "fas fa-utensils",
                    ModuleUrl = "~/Meals/MealOrdering.aspx",
                    SortOrder = 25
                },
                new SystemModule("MealManagement", "膳食管理", ModuleCategories.Meal)
                {
                    Description = "每日膳食管理、訂單處理",
                    Icon = "fas fa-tasks",
                    ModuleUrl = "~/Meals/MealManagement.aspx",
                    SortOrder = 26
                },

                // 報告統計
                new SystemModule("Reports", "報告統計", ModuleCategories.Report)
                {
                    Description = "各類統計報告和數據分析",
                    Icon = "fas fa-chart-bar",
                    ModuleUrl = "~/Reports/",
                    SortOrder = 30
                },
                new SystemModule("MemberReports", "會員報告", ModuleCategories.Report)
                {
                    Description = "會員相關統計報告",
                    Icon = "fas fa-user-chart",
                    ModuleUrl = "~/Reports/Members.aspx",
                    SortOrder = 31
                },
                new SystemModule("ActivityReports", "活動報告", ModuleCategories.Report)
                {
                    Description = "活動相關統計報告",
                    Icon = "fas fa-chart-line",
                    ModuleUrl = "~/Reports/Activities.aspx",
                    SortOrder = 32
                },

                // 系統管理
                new SystemModule("UserManagement", "用戶管理", ModuleCategories.System)
                {
                    Description = "系統用戶帳號管理",
                    Icon = "fas fa-users-cog",
                    ModuleUrl = "~/Settings/UserRoles.aspx",
                    SortOrder = 40
                },
                new SystemModule("PermissionManagement", "權限管理", ModuleCategories.System)
                {
                    Description = "角色權限配置管理",
                    Icon = "fas fa-shield-alt",
                    ModuleUrl = "~/Settings/PermissionManagement.aspx",
                    SortOrder = 41
                },
                new SystemModule("SystemSettings", "系統設定", ModuleCategories.System)
                {
                    Description = "系統參數和配置管理",
                    Icon = "fas fa-cogs",
                    ModuleUrl = "~/Admin/Settings.aspx",
                    SortOrder = 42
                },

                // 安全管理
                new SystemModule("SecurityMonitoring", "安全監控", ModuleCategories.Security)
                {
                    Description = "系統安全監控和審計日誌",
                    Icon = "fas fa-eye",
                    ModuleUrl = "~/Admin/SecurityTest.aspx",
                    SortOrder = 50
                },
                new SystemModule("AuditLogs", "審計日誌", ModuleCategories.Security)
                {
                    Description = "系統操作審計記錄",
                    Icon = "fas fa-history",
                    ModuleUrl = "~/Admin/AuditLogs.aspx",
                    SortOrder = 51
                },

                // 測試功能
                new SystemModule("FirebaseTest", "Firebase 測試", ModuleCategories.System)
                {
                    Description = "Firebase 連接測試功能",
                    Icon = "fas fa-database",
                    ModuleUrl = "~/Admin/FirebaseTest.aspx",
                    SortOrder = 90,
                    IsActive = false
                }
            };
        }
    }
}