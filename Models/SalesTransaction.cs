using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 銷售交易模型 - POS 系統交易記錄
    /// </summary>
    [Table("SalesTransactions")]
    public class SalesTransaction
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [StringLength(30)]
        [Display(Name = "交易單號")]
        public string TransactionNumber { get; set; }

        [Display(Name = "會員ID")]
        public int? MemberId { get; set; }

        [StringLength(100)]
        [Display(Name = "購買人姓名")]
        public string CustomerName { get; set; }

        [StringLength(50)]
        [Display(Name = "購買人電話")]
        public string CustomerPhone { get; set; }

        [Required]
        [Display(Name = "交易類型")]
        public TransactionType TransactionType { get; set; } = TransactionType.Sale;

        [Required]
        [Display(Name = "付款方式")]
        public PaymentMethodType PaymentMethod { get; set; } = PaymentMethodType.Cash;

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "總金額")]
        public decimal TotalAmount { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "實收金額")]
        public decimal? ReceivedAmount { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "找零金額")]
        public decimal? ChangeAmount { get; set; }

        [Required]
        [Display(Name = "交易狀態")]
        public TransactionStatus Status { get; set; } = TransactionStatus.Completed;

        [Display(Name = "交易時間")]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [StringLength(128)]
        [Display(Name = "操作員")]
        public string OperatorId { get; set; }

        [StringLength(100)]
        [Display(Name = "操作員姓名")]
        public string OperatorName { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        [Display(Name = "原始交易ID")]
        public int? OriginalTransactionId { get; set; }

        [StringLength(100)]
        [Display(Name = "退貨原因")]
        public string RefundReason { get; set; }

        [Display(Name = "建立時間")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "修改時間")]
        public DateTime? UpdatedAt { get; set; }

        // 導航屬性
        public virtual Member Member { get; set; }
        public virtual SalesTransaction OriginalTransaction { get; set; }
        public virtual ICollection<SalesItem> SalesItems { get; set; }
        public virtual ICollection<SalesTransaction> RefundTransactions { get; set; }

        public SalesTransaction()
        {
            SalesItems = new HashSet<SalesItem>();
            RefundTransactions = new HashSet<SalesTransaction>();
        }

        // 業務邏輯方法

        /// <summary>
        /// 計算總金額
        /// </summary>
        public void CalculateTotalAmount()
        {
            if (SalesItems?.Any() == true)
            {
                TotalAmount = SalesItems.Sum(item => item.TotalPrice);
            }
        }

        /// <summary>
        /// 計算找零
        /// </summary>
        public void CalculateChange()
        {
            if (ReceivedAmount.HasValue && ReceivedAmount.Value >= TotalAmount)
            {
                ChangeAmount = ReceivedAmount.Value - TotalAmount;
            }
            else
            {
                ChangeAmount = 0;
            }
        }

        /// <summary>
        /// 檢查是否可以退貨
        /// </summary>
        /// <returns>是否可以退貨</returns>
        public bool CanRefund()
        {
            return TransactionType == TransactionType.Sale &&
                   Status == TransactionStatus.Completed &&
                   TransactionDate >= DateTime.Today.AddDays(-30); // 30天內可退貨
        }

        /// <summary>
        /// 執行退貨
        /// </summary>
        /// <param name="reason">退貨原因</param>
        /// <param name="operatorId">操作員ID</param>
        /// <param name="operatorName">操作員姓名</param>
        public void ProcessRefund(string reason, string operatorId, string operatorName)
        {
            if (!CanRefund())
                throw new InvalidOperationException("此交易無法退貨");

            RefundReason = reason;
            Status = TransactionStatus.Refunded;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 驗證交易資料
        /// </summary>
        /// <returns>驗證錯誤列表</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(TransactionNumber))
                errors.Add("交易單號不能為空");

            if (TotalAmount < 0)
                errors.Add("總金額不能為負數");

            if (PaymentMethod == PaymentMethodType.Cash && ReceivedAmount.HasValue && ReceivedAmount.Value < TotalAmount)
                errors.Add("現金付款時實收金額不能少於總金額");

            if (string.IsNullOrWhiteSpace(OperatorId))
                errors.Add("操作員ID不能為空");

            if (!SalesItems?.Any() == true)
                errors.Add("交易必須包含至少一個商品項目");

            return errors;
        }

        /// <summary>
        /// 交易類型顯示文字
        /// </summary>
        public string TransactionTypeDisplayText
        {
            get
            {
                return TransactionType switch
                {
                    TransactionType.Sale => "銷售",
                    TransactionType.Refund => "退貨",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 付款方式顯示文字
        /// </summary>
        public string PaymentMethodDisplayText
        {
            get
            {
                return PaymentMethod switch
                {
                    PaymentMethodType.Cash => "現金",
                    PaymentMethodType.Octopus => "八達通",
                    PaymentMethodType.CreditCard => "信用卡",
                    PaymentMethodType.BankTransfer => "銀行轉賬",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 交易狀態顯示文字
        /// </summary>
        public string StatusDisplayText
        {
            get
            {
                return Status switch
                {
                    TransactionStatus.Pending => "待處理",
                    TransactionStatus.Completed => "已完成",
                    TransactionStatus.Cancelled => "已取消",
                    TransactionStatus.Refunded => "已退貨",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 狀態 CSS 類別
        /// </summary>
        public string StatusCssClass
        {
            get
            {
                return Status switch
                {
                    TransactionStatus.Pending => "badge bg-warning",
                    TransactionStatus.Completed => "badge bg-success",
                    TransactionStatus.Cancelled => "badge bg-secondary",
                    TransactionStatus.Refunded => "badge bg-danger",
                    _ => "badge bg-secondary"
                };
            }
        }

        /// <summary>
        /// 購買人顯示名稱
        /// </summary>
        public string CustomerDisplayName
        {
            get
            {
                if (Member != null)
                    return $"{Member.FullName} ({Member.MemberNumber})";
                
                if (!string.IsNullOrWhiteSpace(CustomerName))
                    return CustomerName;
                
                return "訪客";
            }
        }
    }

    /// <summary>
    /// 銷售項目模型 - 交易明細
    /// </summary>
    [Table("SalesItems")]
    public class SalesItem
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [Display(Name = "交易ID")]
        public int SalesTransactionId { get; set; }

        [Required]
        [Display(Name = "產品ID")]
        public int ProductId { get; set; }

        [StringLength(100)]
        [Display(Name = "產品名稱")]
        public string ProductName { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "單價")]
        public decimal UnitPrice { get; set; }

        [Required]
        [Display(Name = "數量")]
        [Range(1, int.MaxValue, ErrorMessage = "數量必須大於 0")]
        public int Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "小計")]
        public decimal TotalPrice { get; set; }

        [Column(TypeName = "decimal(5,4)")]
        [Display(Name = "折扣率")]
        public decimal? DiscountRate { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        [Display(Name = "折扣金額")]
        public decimal? DiscountAmount { get; set; }

        [StringLength(200)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 導航屬性
        public virtual SalesTransaction SalesTransaction { get; set; }
        public virtual Product Product { get; set; }

        // 業務邏輯方法

        /// <summary>
        /// 計算小計
        /// </summary>
        public void CalculateTotalPrice()
        {
            var subtotal = UnitPrice * Quantity;
            
            if (DiscountRate.HasValue && DiscountRate.Value > 0)
            {
                DiscountAmount = subtotal * DiscountRate.Value;
                TotalPrice = subtotal - DiscountAmount.Value;
            }
            else if (DiscountAmount.HasValue && DiscountAmount.Value > 0)
            {
                TotalPrice = subtotal - DiscountAmount.Value;
                DiscountRate = DiscountAmount.Value / subtotal;
            }
            else
            {
                TotalPrice = subtotal;
                DiscountAmount = 0;
                DiscountRate = 0;
            }

            // 確保小計不為負數
            if (TotalPrice < 0)
                TotalPrice = 0;
        }

        /// <summary>
        /// 驗證銷售項目資料
        /// </summary>
        /// <returns>驗證錯誤列表</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (ProductId <= 0)
                errors.Add("產品ID無效");

            if (UnitPrice < 0)
                errors.Add("單價不能為負數");

            if (Quantity <= 0)
                errors.Add("數量必須大於 0");

            if (DiscountRate.HasValue && (DiscountRate.Value < 0 || DiscountRate.Value > 1))
                errors.Add("折扣率必須在 0-1 之間");

            if (DiscountAmount.HasValue && DiscountAmount.Value < 0)
                errors.Add("折扣金額不能為負數");

            return errors;
        }
    }

    /// <summary>
    /// 交易類型枚舉
    /// </summary>
    public enum TransactionType
    {
        [Display(Name = "銷售")]
        Sale = 1,
        
        [Display(Name = "退貨")]
        Refund = 2
    }

    /// <summary>
    /// 付款方式枚舉
    /// </summary>
    public enum PaymentMethodType
    {
        [Display(Name = "現金")]
        Cash = 1,
        
        [Display(Name = "八達通")]
        Octopus = 2,
        
        [Display(Name = "信用卡")]
        CreditCard = 3,
        
        [Display(Name = "銀行轉賬")]
        BankTransfer = 4
    }

    /// <summary>
    /// 交易狀態枚舉
    /// </summary>
    public enum TransactionStatus
    {
        [Display(Name = "待處理")]
        Pending = 1,
        
        [Display(Name = "已完成")]
        Completed = 2,
        
        [Display(Name = "已取消")]
        Cancelled = 3,
        
        [Display(Name = "已退貨")]
        Refunded = 4
    }

    /// <summary>
    /// 銷售統計模型
    /// </summary>
    public class SalesStatistics
    {
        public DateTime Date { get; set; }
        public int TotalTransactions { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalRefunds { get; set; }
        public decimal NetSales { get; set; }
        public int TotalItemsSold { get; set; }
        public decimal AverageTransactionAmount { get; set; }
        public Dictionary<string, int> PaymentMethodBreakdown { get; set; }
        public Dictionary<string, decimal> CategorySalesBreakdown { get; set; }

        public SalesStatistics()
        {
            PaymentMethodBreakdown = new Dictionary<string, int>();
            CategorySalesBreakdown = new Dictionary<string, decimal>();
        }
    }

    /// <summary>
    /// 產品銷售統計
    /// </summary>
    public class ProductSalesStatistic
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public int QuantitySold { get; set; }
        public decimal Revenue { get; set; }
        public decimal UnitPrice { get; set; }
        public int CurrentStock { get; set; }
    }

    /// <summary>
    /// 銷售操作結果
    /// </summary>
    public class SalesOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int? TransactionId { get; set; }
        public List<string> Errors { get; set; }
        public object Data { get; set; }

        public SalesOperationResult()
        {
            Errors = new List<string>();
        }
    }
}