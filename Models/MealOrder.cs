using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 膳食訂單資料模型 - 管理會員膳食訂購記錄
    /// </summary>
    [Table("MealOrders")]
    public class MealOrder
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "訂單號碼")]
        public string OrderNumber { get; set; }

        [Required]
        public int MemberId { get; set; }

        [Required]
        public int MealItemId { get; set; }

        [Required]
        [Column(TypeName = "DATE")]
        [Display(Name = "用餐日期")]
        public DateTime MealDate { get; set; }

        [Required]
        [Display(Name = "數量")]
        public int Quantity { get; set; } = 1;

        [Required]
        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "單價")]
        public decimal UnitPrice { get; set; }

        [Required]
        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "總金額")]
        public decimal TotalAmount { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "付款狀態")]
        public string PaymentStatus { get; set; } // Pending, Paid, Cancelled

        [StringLength(20)]
        [Display(Name = "付款方式")]
        public string PaymentMethod { get; set; } // Cash, Octopus, CreditCard, BankTransfer

        [Column(TypeName = "DATE")]
        [Display(Name = "付款日期")]
        public DateTime? PaymentDate { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "訂單狀態")]
        public string OrderStatus { get; set; } // Ordered, Confirmed, Cancelled, Completed

        [StringLength(500)]
        [Display(Name = "特殊要求")]
        public string SpecialRequests { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 系統欄位
        [Display(Name = "訂單時間")]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(128)]
        [Display(Name = "訂購者")]
        public string OrderedBy { get; set; }

        [Display(Name = "確認時間")]
        public DateTime? ConfirmedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "確認者")]
        public string ConfirmedBy { get; set; }

        [Display(Name = "取消時間")]
        public DateTime? CancelledDate { get; set; }

        [StringLength(128)]
        [Display(Name = "取消者")]
        public string CancelledBy { get; set; }

        [StringLength(200)]
        [Display(Name = "取消原因")]
        public string CancellationReason { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? UpdatedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string UpdatedBy { get; set; }

        // 導航屬性
        [ForeignKey("MemberId")]
        public virtual Member Member { get; set; }

        [ForeignKey("MealItemId")]
        public virtual MealItem MealItem { get; set; }

        #region 非映射屬性

        /// <summary>
        /// 是否可取消
        /// </summary>
        [NotMapped]
        public bool CanCancel
        {
            get
            {
                return OrderStatus == MealConstants.OrderStatus.Ordered &&
                       MealDate >= DateTime.Today;
            }
        }

        /// <summary>
        /// 是否已過期
        /// </summary>
        [NotMapped]
        public bool IsExpired
        {
            get
            {
                return MealDate < DateTime.Today && 
                       OrderStatus != MealConstants.OrderStatus.Completed &&
                       OrderStatus != MealConstants.OrderStatus.Cancelled;
            }
        }

        /// <summary>
        /// 訂單狀態顯示文字
        /// </summary>
        [NotMapped]
        public string OrderStatusDisplayText
        {
            get
            {
                return OrderStatus switch
                {
                    MealConstants.OrderStatus.Ordered => "已訂購",
                    MealConstants.OrderStatus.Confirmed => "已確認",
                    MealConstants.OrderStatus.Cancelled => "已取消",
                    MealConstants.OrderStatus.Completed => "已完成",
                    _ => OrderStatus
                };
            }
        }

        /// <summary>
        /// 付款狀態顯示文字
        /// </summary>
        [NotMapped]
        public string PaymentStatusDisplayText
        {
            get
            {
                return PaymentStatus switch
                {
                    MealConstants.PaymentStatus.Pending => "待付款",
                    MealConstants.PaymentStatus.Paid => "已付款",
                    MealConstants.PaymentStatus.Cancelled => "已取消",
                    _ => PaymentStatus
                };
            }
        }

        /// <summary>
        /// 格式化顯示訂單資訊
        /// </summary>
        [NotMapped]
        public string DisplaySummary => $"{OrderNumber} - {Member?.FullName} - {MealItem?.ItemName} ({MealDate:MM/dd})";

        #endregion

        #region 業務方法

        /// <summary>
        /// 計算總金額
        /// </summary>
        public void CalculateTotalAmount()
        {
            TotalAmount = UnitPrice * Quantity;
        }

        /// <summary>
        /// 確認訂單
        /// </summary>
        /// <param name="confirmedBy">確認者</param>
        public void ConfirmOrder(string confirmedBy)
        {
            if (OrderStatus == MealConstants.OrderStatus.Ordered)
            {
                OrderStatus = MealConstants.OrderStatus.Confirmed;
                ConfirmedBy = confirmedBy;
                ConfirmedDate = DateTime.Now;
                UpdatedBy = confirmedBy;
                UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// 取消訂單
        /// </summary>
        /// <param name="cancelledBy">取消者</param>
        /// <param name="reason">取消原因</param>
        public void CancelOrder(string cancelledBy, string reason)
        {
            if (CanCancel)
            {
                OrderStatus = MealConstants.OrderStatus.Cancelled;
                PaymentStatus = MealConstants.PaymentStatus.Cancelled;
                CancelledBy = cancelledBy;
                CancelledDate = DateTime.Now;
                CancellationReason = reason;
                UpdatedBy = cancelledBy;
                UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// 標記為已完成
        /// </summary>
        /// <param name="completedBy">完成者</param>
        public void CompleteOrder(string completedBy)
        {
            if (OrderStatus == MealConstants.OrderStatus.Confirmed)
            {
                OrderStatus = MealConstants.OrderStatus.Completed;
                UpdatedBy = completedBy;
                UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// 更新付款狀態
        /// </summary>
        /// <param name="paymentMethod">付款方式</param>
        /// <param name="updatedBy">更新者</param>
        public void UpdatePaymentStatus(string paymentMethod, string updatedBy)
        {
            PaymentStatus = MealConstants.PaymentStatus.Paid;
            PaymentMethod = paymentMethod;
            PaymentDate = DateTime.Now;
            UpdatedBy = updatedBy;
            UpdatedDate = DateTime.Now;
        }

        /// <summary>
        /// 驗證訂單資料
        /// </summary>
        /// <returns>驗證錯誤列表</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(OrderNumber))
                errors.Add("訂單號碼不能為空");

            if (MemberId <= 0)
                errors.Add("必須選擇有效的會員");

            if (MealItemId <= 0)
                errors.Add("必須選擇餐點");

            if (MealDate < DateTime.Today)
                errors.Add("用餐日期不能早於今天");

            if (Quantity <= 0)
                errors.Add("數量必須大於0");

            if (UnitPrice < 0)
                errors.Add("單價不能為負數");

            return errors;
        }

        /// <summary>
        /// 生成訂單號碼
        /// </summary>
        /// <param name="sequence">序號</param>
        /// <returns>訂單號碼</returns>
        public static string GenerateOrderNumber(int sequence)
        {
            return $"MO{DateTime.Now:yyyyMMdd}{sequence:D4}";
        }

        #endregion
    }

    /// <summary>
    /// 膳食項目資料模型 - 管理可訂購的餐點
    /// </summary>
    [Table("MealItems")]
    public class MealItem
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "餐點名稱")]
        public string ItemName { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "餐點類別")]
        public string Category { get; set; } // MainMeal, SideMeal, SpecialMeal

        [StringLength(500)]
        [Display(Name = "餐點描述")]
        public string Description { get; set; }

        [Required]
        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "價格")]
        public decimal Price { get; set; }

        [Display(Name = "每日限量")]
        public int? DailyLimit { get; set; }

        [Display(Name = "是否提供")]
        public bool IsAvailable { get; set; } = true;

        [Display(Name = "排序順序")]
        public int SortOrder { get; set; } = 0;

        [StringLength(200)]
        [Display(Name = "圖片路徑")]
        public string ImagePath { get; set; }

        [StringLength(200)]
        [Display(Name = "過敏原資訊")]
        public string AllergenInfo { get; set; }

        [StringLength(100)]
        [Display(Name = "營養資訊")]
        public string NutritionalInfo { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 系統欄位
        [Display(Name = "啟用狀態")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? UpdatedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string UpdatedBy { get; set; }

        // 導航屬性
        public virtual ICollection<MealOrder> MealOrders { get; set; }
        public virtual ICollection<MealDailyStock> MealDailyStocks { get; set; }

        public MealItem()
        {
            MealOrders = new HashSet<MealOrder>();
            MealDailyStocks = new HashSet<MealDailyStock>();
        }

        #region 非映射屬性

        /// <summary>
        /// 餐點類別顯示文字
        /// </summary>
        [NotMapped]
        public string CategoryDisplayText
        {
            get
            {
                return Category switch
                {
                    MealConstants.Category.MainMeal => "主餐",
                    MealConstants.Category.SideMeal => "副餐",
                    MealConstants.Category.SpecialMeal => "特餐",
                    _ => Category
                };
            }
        }

        /// <summary>
        /// 格式化價格顯示
        /// </summary>
        [NotMapped]
        public string PriceDisplay => $"HK${Price:F2}";

        /// <summary>
        /// 格式化顯示名稱
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{ItemName} ({CategoryDisplayText}) - {PriceDisplay}";

        #endregion

        #region 業務方法

        /// <summary>
        /// 檢查指定日期是否有庫存
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <param name="quantity">需要數量</param>
        /// <returns>是否有足夠庫存</returns>
        public bool HasStock(DateTime date, int quantity = 1)
        {
            if (!DailyLimit.HasValue) return true;

            var dailyStock = MealDailyStocks.FirstOrDefault(s => s.StockDate.Date == date.Date);
            if (dailyStock == null) return quantity <= DailyLimit.Value;

            return dailyStock.AvailableQuantity >= quantity;
        }

        /// <summary>
        /// 驗證餐點資料
        /// </summary>
        /// <returns>驗證錯誤列表</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(ItemName))
                errors.Add("餐點名稱不能為空");

            if (string.IsNullOrWhiteSpace(Category))
                errors.Add("餐點類別不能為空");

            if (Price < 0)
                errors.Add("價格不能為負數");

            if (DailyLimit.HasValue && DailyLimit.Value < 0)
                errors.Add("每日限量不能為負數");

            return errors;
        }

        #endregion
    }

    /// <summary>
    /// 每日膳食庫存記錄
    /// </summary>
    [Table("MealDailyStocks")]
    public class MealDailyStock
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int MealItemId { get; set; }

        [Required]
        [Column(TypeName = "DATE")]
        [Display(Name = "庫存日期")]
        public DateTime StockDate { get; set; }

        [Required]
        [Display(Name = "原始數量")]
        public int OriginalQuantity { get; set; }

        [Required]
        [Display(Name = "已訂數量")]
        public int OrderedQuantity { get; set; } = 0;

        [Required]
        [Display(Name = "可用數量")]
        public int AvailableQuantity { get; set; }

        [Display(Name = "修改時間")]
        public DateTime UpdatedDate { get; set; } = DateTime.Now;

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string UpdatedBy { get; set; }

        // 導航屬性
        [ForeignKey("MealItemId")]
        public virtual MealItem MealItem { get; set; }

        #region 業務方法

        /// <summary>
        /// 預訂庫存
        /// </summary>
        /// <param name="quantity">預訂數量</param>
        /// <param name="updatedBy">更新者</param>
        /// <returns>是否成功</returns>
        public bool ReserveStock(int quantity, string updatedBy)
        {
            if (AvailableQuantity >= quantity)
            {
                OrderedQuantity += quantity;
                AvailableQuantity -= quantity;
                UpdatedBy = updatedBy;
                UpdatedDate = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 釋放庫存
        /// </summary>
        /// <param name="quantity">釋放數量</param>
        /// <param name="updatedBy">更新者</param>
        public void ReleaseStock(int quantity, string updatedBy)
        {
            OrderedQuantity = Math.Max(0, OrderedQuantity - quantity);
            AvailableQuantity = Math.Min(OriginalQuantity, AvailableQuantity + quantity);
            UpdatedBy = updatedBy;
            UpdatedDate = DateTime.Now;
        }

        #endregion
    }

    /// <summary>
    /// 膳食搜尋條件
    /// </summary>
    public class MealOrderSearchCriteria
    {
        public string Keyword { get; set; }
        public int? MemberId { get; set; }
        public string MemberNumber { get; set; }
        public string OrderStatus { get; set; }
        public string PaymentStatus { get; set; }
        public string Category { get; set; }
        public DateTime? OrderDateFrom { get; set; }
        public DateTime? OrderDateTo { get; set; }
        public DateTime? MealDateFrom { get; set; }
        public DateTime? MealDateTo { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "OrderDate";
        public string SortDirection { get; set; } = "DESC";
    }

    /// <summary>
    /// 每日膳食統計
    /// </summary>
    public class DailyMealStatistics
    {
        public DateTime Date { get; set; }
        public int TotalOrders { get; set; }
        public int MainMealOrders { get; set; }
        public int SideMealOrders { get; set; }
        public int SpecialMealOrders { get; set; }
        public decimal TotalRevenue { get; set; }
        public List<MealItemStatistic> ItemStatistics { get; set; }

        public DailyMealStatistics()
        {
            ItemStatistics = new List<MealItemStatistic>();
        }
    }

    /// <summary>
    /// 單一餐點統計
    /// </summary>
    public class MealItemStatistic
    {
        public int MealItemId { get; set; }
        public string ItemName { get; set; }
        public string Category { get; set; }
        public int OrderedQuantity { get; set; }
        public int AvailableQuantity { get; set; }
        public decimal Revenue { get; set; }
    }

    /// <summary>
    /// 膳食操作結果
    /// </summary>
    public class MealOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public int? OrderId { get; set; }
        public List<int> OrderIds { get; set; } = new List<int>();
        public object Data { get; set; }
    }

    /// <summary>
    /// 膳食常數定義
    /// </summary>
    public static class MealConstants
    {
        public static class Category
        {
            public const string MainMeal = "MainMeal";
            public const string SideMeal = "SideMeal";
            public const string SpecialMeal = "SpecialMeal";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { MainMeal, "主餐" },
                    { SideMeal, "副餐" },
                    { SpecialMeal, "特餐" }
                };
            }
        }

        public static class OrderStatus
        {
            public const string Ordered = "Ordered";
            public const string Confirmed = "Confirmed";
            public const string Cancelled = "Cancelled";
            public const string Completed = "Completed";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Ordered, "已訂購" },
                    { Confirmed, "已確認" },
                    { Cancelled, "已取消" },
                    { Completed, "已完成" }
                };
            }
        }

        public static class PaymentStatus
        {
            public const string Pending = "Pending";
            public const string Paid = "Paid";
            public const string Cancelled = "Cancelled";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Pending, "待付款" },
                    { Paid, "已付款" },
                    { Cancelled, "已取消" }
                };
            }
        }

        public static class PaymentMethods
        {
            public const string Cash = "Cash";
            public const string Octopus = "Octopus";
            public const string CreditCard = "CreditCard";
            public const string BankTransfer = "BankTransfer";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Cash, "現金" },
                    { Octopus, "八達通" },
                    { CreditCard, "信用卡" },
                    { BankTransfer, "轉賬" }
                };
            }
        }
    }
}