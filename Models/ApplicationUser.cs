using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNet.Identity;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 應用程式用戶模型 - ASP.NET Identity 整合
    /// </summary>
    [Table("AspNetUsers")]
    public class ApplicationUser : IUser<string>
    {
        public ApplicationUser()
        {
            Id = Guid.NewGuid().ToString();
            SecurityStamp = Guid.NewGuid().ToString();
            CreatedDate = DateTime.UtcNow;
            LastActivityDate = DateTime.UtcNow;
            IsEnabled = true;
            AccessFailedCount = 0;
            LockoutEnabled = true;
        }

        public ApplicationUser(string userName) : this()
        {
            UserName = userName;
        }

        #region IUser<string> 實作

        [Key]
        [StringLength(128)]
        public string Id { get; set; }

        [Required]
        [StringLength(256)]
        [Index("UserNameIndex", IsUnique = true)]
        public string UserName { get; set; }

        #endregion

        #region 基本驗證欄位

        [Required]
        [StringLength(256)]
        [EmailAddress]
        [Index("EmailIndex")]
        public string Email { get; set; }

        public bool EmailConfirmed { get; set; }

        [StringLength(512)]
        public string PasswordHash { get; set; }

        [StringLength(512)]
        public string SecurityStamp { get; set; }

        [StringLength(50)]
        public string PhoneNumber { get; set; }

        public bool PhoneNumberConfirmed { get; set; }

        public bool TwoFactorEnabled { get; set; }

        #endregion

        #region 鎖定機制

        public DateTime? LockoutEndDateUtc { get; set; }

        public bool LockoutEnabled { get; set; }

        public int AccessFailedCount { get; set; }

        [NotMapped]
        public bool IsLockedOut
        {
            get
            {
                return LockoutEndDateUtc.HasValue && LockoutEndDateUtc > DateTime.UtcNow;
            }
        }

        #endregion

        #region 個人資料

        [StringLength(100)]
        [Display(Name = "姓名")]
        public string DisplayName { get; set; }

        [StringLength(20)]
        [Display(Name = "身份證號")]
        public string HKID { get; set; }

        [Display(Name = "生日")]
        [DataType(DataType.Date)]
        public DateTime? DateOfBirth { get; set; }

        [StringLength(10)]
        [Display(Name = "性別")]
        public string Gender { get; set; }

        [StringLength(500)]
        [Display(Name = "地址")]
        public string Address { get; set; }

        [StringLength(200)]
        [Display(Name = "緊急聯絡人")]
        public string EmergencyContact { get; set; }

        [StringLength(50)]
        [Display(Name = "緊急聯絡電話")]
        public string EmergencyPhone { get; set; }

        #endregion

        #region 角色和權限

        [StringLength(50)]
        [Display(Name = "用戶角色")]
        public string Role { get; set; }

        [StringLength(100)]
        [Display(Name = "部門")]
        public string Department { get; set; }

        [StringLength(100)]
        [Display(Name = "職位")]
        public string Position { get; set; }

        #endregion

        #region 系統欄位

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string ModifiedBy { get; set; }

        [Display(Name = "最後活動時間")]
        public DateTime LastActivityDate { get; set; }

        [StringLength(45)]
        [Display(Name = "最後登入 IP")]
        public string LastLoginIP { get; set; }

        [Display(Name = "啟用狀態")]
        public bool IsEnabled { get; set; }

        [StringLength(1000)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        #endregion

        #region 密碼重設

        [StringLength(512)]
        public string PasswordResetToken { get; set; }

        public DateTime? PasswordResetTokenExpiry { get; set; }

        [Display(Name = "強制修改密碼")]
        public bool MustChangePassword { get; set; }

        [Display(Name = "密碼到期日")]
        public DateTime? PasswordExpiry { get; set; }

        [NotMapped]
        public bool IsPasswordExpired
        {
            get
            {
                return PasswordExpiry.HasValue && PasswordExpiry < DateTime.UtcNow;
            }
        }

        #endregion

        #region 審計欄位

        [Display(Name = "登入次數")]
        public int LoginCount { get; set; }

        [Display(Name = "失敗登入次數")]
        public int FailedLoginCount { get; set; }

        [Display(Name = "最後失敗時間")]
        public DateTime? LastFailedLoginDate { get; set; }

        [StringLength(45)]
        [Display(Name = "最後失敗 IP")]
        public string LastFailedLoginIP { get; set; }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 重設存取失敗計數
        /// </summary>
        public void ResetAccessFailedCount()
        {
            AccessFailedCount = 0;
            LockoutEndDateUtc = null;
        }

        /// <summary>
        /// 增加存取失敗計數
        /// </summary>
        /// <param name="lockoutMinutes">鎖定分鐘數</param>
        public void IncrementAccessFailedCount(int lockoutMinutes = 30)
        {
            AccessFailedCount++;
            
            // 5次失敗後鎖定帳戶
            if (AccessFailedCount >= 5)
            {
                LockoutEndDateUtc = DateTime.UtcNow.AddMinutes(lockoutMinutes);
            }
        }

        /// <summary>
        /// 更新最後活動時間
        /// </summary>
        /// <param name="ipAddress">IP 地址</param>
        public void UpdateLastActivity(string ipAddress = null)
        {
            LastActivityDate = DateTime.UtcNow;
            
            if (!string.IsNullOrEmpty(ipAddress))
            {
                LastLoginIP = ipAddress;
            }
        }

        /// <summary>
        /// 記錄成功登入
        /// </summary>
        /// <param name="ipAddress">IP 地址</param>
        public void RecordSuccessfulLogin(string ipAddress)
        {
            LoginCount++;
            ResetAccessFailedCount();
            UpdateLastActivity(ipAddress);
        }

        /// <summary>
        /// 記錄失敗登入
        /// </summary>
        /// <param name="ipAddress">IP 地址</param>
        public void RecordFailedLogin(string ipAddress)
        {
            FailedLoginCount++;
            LastFailedLoginDate = DateTime.UtcNow;
            LastFailedLoginIP = ipAddress;
            IncrementAccessFailedCount();
        }

        /// <summary>
        /// 生成密碼重設 Token
        /// </summary>
        /// <param name="expiryHours">過期小時數</param>
        public void GeneratePasswordResetToken(int expiryHours = 24)
        {
            PasswordResetToken = Guid.NewGuid().ToString("N");
            PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(expiryHours);
        }

        /// <summary>
        /// 清除密碼重設 Token
        /// </summary>
        public void ClearPasswordResetToken()
        {
            PasswordResetToken = null;
            PasswordResetTokenExpiry = null;
        }

        /// <summary>
        /// 檢查密碼重設 Token 是否有效
        /// </summary>
        /// <param name="token">Token</param>
        /// <returns>是否有效</returns>
        public bool IsPasswordResetTokenValid(string token)
        {
            return !string.IsNullOrEmpty(PasswordResetToken) &&
                   PasswordResetToken == token &&
                   PasswordResetTokenExpiry.HasValue &&
                   PasswordResetTokenExpiry > DateTime.UtcNow;
        }

        #endregion
    }

    /// <summary>
    /// 用戶角色模型
    /// </summary>
    [Table("AspNetRoles")]
    public class ApplicationRole : IRole<string>
    {
        public ApplicationRole()
        {
            Id = Guid.NewGuid().ToString();
        }

        public ApplicationRole(string name) : this()
        {
            Name = name;
        }

        [Key]
        [StringLength(128)]
        public string Id { get; set; }

        [Required]
        [StringLength(256)]
        [Index("RoleNameIndex", IsUnique = true)]
        public string Name { get; set; }

        [StringLength(1000)]
        public string Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 用戶角色關聯模型
    /// </summary>
    [Table("AspNetUserRoles")]
    public class ApplicationUserRole
    {
        [Key, Column(Order = 0)]
        [StringLength(128)]
        public string UserId { get; set; }

        [Key, Column(Order = 1)]
        [StringLength(128)]
        public string RoleId { get; set; }

        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        public string AssignedBy { get; set; }

        // 導航屬性
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; }

        [ForeignKey("RoleId")]
        public virtual ApplicationRole Role { get; set; }
    }

    /// <summary>
    /// 審計日誌模型
    /// </summary>
    [Table("AuditLogs")]
    public class AuditLog
    {
        [Key]
        public int Id { get; set; }

        [StringLength(128)]
        [Index]
        public string UserId { get; set; }

        [StringLength(256)]
        public string UserName { get; set; }

        [Required]
        [StringLength(50)]
        [Index]
        public string Action { get; set; }

        [StringLength(100)]
        public string Entity { get; set; }

        [StringLength(128)]
        public string EntityId { get; set; }

        [StringLength(45)]
        [Index]
        public string IPAddress { get; set; }

        [StringLength(500)]
        public string UserAgent { get; set; }

        [Column(TypeName = "text")]
        public string Details { get; set; }

        [StringLength(20)]
        public string Result { get; set; }

        [Index]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string SessionId { get; set; }

        // 導航屬性
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; }
    }
}