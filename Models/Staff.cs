using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using CWDECC_3S.Data;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 職員資料模型 - 管理職員與導師的基本資料、合約與服務記錄
    /// </summary>
    [Table("Staff")]
    public class Staff
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "員工編號")]
        public string EmployeeCode { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "姓名")]
        public string FullName { get; set; }

        [Required]
        [StringLength(10)]
        [Display(Name = "性別")]
        public string Gender { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "出生日期")]
        public DateTime? DateOfBirth { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "身份證號碼")]
        public string HKID { get; set; }

        // 加密欄位 - 電話號碼
        [StringLength(1000)] // 加密後長度
        [Display(Name = "電話號碼（加密）")]
        public string PhoneEncrypted { get; set; }

        // 加密欄位 - 電子郵件
        [StringLength(1000)] // 加密後長度
        [Display(Name = "電子郵件（加密）")]
        public string EmailEncrypted { get; set; }

        // 加密欄位 - 住址
        [StringLength(2000)] // 加密後長度
        [Display(Name = "住址（加密）")]
        public string AddressEncrypted { get; set; }

        [StringLength(50)]
        [Display(Name = "部門")]
        public string Department { get; set; }

        [StringLength(100)]
        [Display(Name = "職位")]
        public string Position { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "職員類型")]
        public string StaffType { get; set; } // Teacher, Staff, Administrator

        [Required]
        [StringLength(20)]
        [Display(Name = "聘用類型")]
        public string EmploymentType { get; set; } // FullTime, PartTime, Contract, Volunteer

        [Column(TypeName = "DATE")]
        [Display(Name = "入職日期")]
        public DateTime HireDate { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "合約開始日期")]
        public DateTime? ContractStartDate { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "合約結束日期")]
        public DateTime? ContractEndDate { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "在職狀態")]
        public string EmploymentStatus { get; set; } // Active, Inactive, Expired, Terminated

        [Column(TypeName = "DECIMAL(10,2)")]
        [Display(Name = "基本薪資")]
        public decimal? BasicSalary { get; set; }

        [Column(TypeName = "DECIMAL(5,2)")]
        [Display(Name = "時薪")]
        public decimal? HourlyRate { get; set; }

        [StringLength(100)]
        [Display(Name = "銀行帳戶")]
        public string BankAccount { get; set; }

        [StringLength(100)]
        [Display(Name = "緊急聯絡人")]
        public string EmergencyContact { get; set; }

        [StringLength(50)]
        [Display(Name = "緊急聯絡電話")]
        public string EmergencyPhone { get; set; }

        [StringLength(200)]
        [Display(Name = "專長領域")]
        public string Specialization { get; set; }

        [StringLength(1000)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 系統欄位
        [Display(Name = "啟用狀態")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string ModifiedBy { get; set; }

        // 導航屬性
        public virtual ICollection<StaffActivity> StaffActivities { get; set; }
        public virtual ICollection<StaffTimesheet> StaffTimesheets { get; set; }

        public Staff()
        {
            StaffActivities = new HashSet<StaffActivity>();
            StaffTimesheets = new HashSet<StaffTimesheet>();
        }

        #region 非映射屬性

        /// <summary>
        /// 電話號碼（解密後）
        /// </summary>
        [NotMapped]
        [Display(Name = "電話號碼")]
        public string Phone
        {
            get
            {
                if (string.IsNullOrEmpty(PhoneEncrypted))
                    return string.Empty;

                try
                {
                    return CryptoHelper.Decrypt(PhoneEncrypted);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    PhoneEncrypted = null;
                }
                else
                {
                    PhoneEncrypted = CryptoHelper.Encrypt(value);
                }
            }
        }

        /// <summary>
        /// 電子郵件（解密後）
        /// </summary>
        [NotMapped]
        [Display(Name = "電子郵件")]
        public string Email
        {
            get
            {
                if (string.IsNullOrEmpty(EmailEncrypted))
                    return string.Empty;

                try
                {
                    return CryptoHelper.Decrypt(EmailEncrypted);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    EmailEncrypted = null;
                }
                else
                {
                    EmailEncrypted = CryptoHelper.Encrypt(value);
                }
            }
        }

        /// <summary>
        /// 住址（解密後）
        /// </summary>
        [NotMapped]
        [Display(Name = "住址")]
        public string Address
        {
            get
            {
                if (string.IsNullOrEmpty(AddressEncrypted))
                    return string.Empty;

                try
                {
                    return CryptoHelper.Decrypt(AddressEncrypted);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    AddressEncrypted = null;
                }
                else
                {
                    AddressEncrypted = CryptoHelper.Encrypt(value);
                }
            }
        }

        /// <summary>
        /// 年齡
        /// </summary>
        [NotMapped]
        [Display(Name = "年齡")]
        public int? Age
        {
            get
            {
                if (!DateOfBirth.HasValue)
                    return null;

                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Value.Year;
                if (DateOfBirth.Value.Date > today.AddYears(-age))
                    age--;

                return age;
            }
        }

        /// <summary>
        /// 服務年資
        /// </summary>
        [NotMapped]
        [Display(Name = "服務年資")]
        public double ServiceYears
        {
            get
            {
                var endDate = EmploymentStatus == "Active" ? DateTime.Today : (ContractEndDate ?? DateTime.Today);
                return (endDate - HireDate).TotalDays / 365.25;
            }
        }

        /// <summary>
        /// 合約是否即將到期（30天內）
        /// </summary>
        [NotMapped]
        [Display(Name = "合約即將到期")]
        public bool IsContractExpiringSoon
        {
            get
            {
                if (!ContractEndDate.HasValue || EmploymentStatus != "Active")
                    return false;

                return ContractEndDate.Value.Subtract(DateTime.Today).TotalDays <= 30;
            }
        }

        /// <summary>
        /// 合約是否已到期
        /// </summary>
        [NotMapped]
        [Display(Name = "合約已到期")]
        public bool IsContractExpired
        {
            get
            {
                if (!ContractEndDate.HasValue)
                    return false;

                return ContractEndDate.Value < DateTime.Today;
            }
        }

        /// <summary>
        /// 格式化顯示名稱
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{FullName} ({EmployeeCode})";

        /// <summary>
        /// 狀態顯示文字
        /// </summary>
        [NotMapped]
        public string StatusDisplayText
        {
            get
            {
                switch (EmploymentStatus)
                {
                    case "Active": return "在職";
                    case "Inactive": return "停職";
                    case "Expired": return "到期";
                    case "Terminated": return "離職";
                    default: return EmploymentStatus;
                }
            }
        }

        /// <summary>
        /// 職員類型顯示文字
        /// </summary>
        [NotMapped]
        public string StaffTypeDisplayText
        {
            get
            {
                switch (StaffType)
                {
                    case "Teacher": return "導師";
                    case "Staff": return "職員";
                    case "Administrator": return "管理員";
                    default: return StaffType;
                }
            }
        }

        /// <summary>
        /// 聘用類型顯示文字
        /// </summary>
        [NotMapped]
        public string EmploymentTypeDisplayText
        {
            get
            {
                switch (EmploymentType)
                {
                    case "FullTime": return "全職";
                    case "PartTime": return "兼職";
                    case "Contract": return "合約";
                    case "Volunteer": return "義工";
                    default: return EmploymentType;
                }
            }
        }

        #endregion

        #region 業務方法

        /// <summary>
        /// 更新在職狀態（根據合約日期）
        /// </summary>
        public void UpdateEmploymentStatus()
        {
            if (ContractEndDate.HasValue && ContractEndDate.Value < DateTime.Today)
            {
                if (EmploymentStatus == "Active")
                {
                    EmploymentStatus = "Expired";
                    ModifiedDate = DateTime.UtcNow;
                }
            }
        }

        /// <summary>
        /// 驗證資料完整性
        /// </summary>
        /// <returns>驗證結果</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(EmployeeCode))
                errors.Add("員工編號不能為空");

            if (string.IsNullOrWhiteSpace(FullName))
                errors.Add("姓名不能為空");

            if (string.IsNullOrWhiteSpace(Gender))
                errors.Add("性別不能為空");

            if (string.IsNullOrWhiteSpace(HKID))
                errors.Add("身份證號碼不能為空");

            if (ContractStartDate.HasValue && ContractEndDate.HasValue)
            {
                if (ContractEndDate.Value <= ContractStartDate.Value)
                    errors.Add("合約結束日期必須晚於開始日期");
            }

            if (HireDate > DateTime.Today)
                errors.Add("入職日期不能晚於今天");

            if (DateOfBirth.HasValue && DateOfBirth.Value > DateTime.Today.AddYears(-16))
                errors.Add("年齡必須至少16歲");

            return errors;
        }

        /// <summary>
        /// 生成員工編號
        /// </summary>
        /// <param name="staffType">職員類型</param>
        /// <param name="sequence">序號</param>
        /// <returns>員工編號</returns>
        public static string GenerateEmployeeCode(string staffType, int sequence)
        {
            string prefix;
            switch (staffType)
            {
                case "Teacher":
                    prefix = "T";
                    break;
                case "Staff":
                    prefix = "S";
                    break;
                case "Administrator":
                    prefix = "A";
                    break;
                default:
                    prefix = "E";
                    break;
            }

            return $"{prefix}{DateTime.Now.Year}{sequence:D4}";
        }

        #endregion
    }

    /// <summary>
    /// 職員活動參與記錄
    /// </summary>
    [Table("StaffActivities")]
    public class StaffActivity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int StaffId { get; set; }

        [Required]
        public int ActivityId { get; set; }

        [StringLength(50)]
        [Display(Name = "參與角色")]
        public string Role { get; set; } // Teacher, Assistant, Coordinator

        [Column(TypeName = "DECIMAL(5,2)")]
        [Display(Name = "授課時數")]
        public decimal TeachingHours { get; set; }

        [Column(TypeName = "DECIMAL(5,2)")]
        [Display(Name = "準備時數")]
        public decimal PreparationHours { get; set; }

        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "費用")]
        public decimal? Fee { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        [Display(Name = "記錄時間")]
        public DateTime RecordDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "記錄者")]
        public string RecordedBy { get; set; }

        // 導航屬性
        [ForeignKey("StaffId")]
        public virtual Staff Staff { get; set; }

        [ForeignKey("ActivityId")]
        public virtual Activity Activity { get; set; }
    }

    /// <summary>
    /// 職員工時記錄
    /// </summary>
    [Table("StaffTimesheets")]
    public class StaffTimesheet
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int StaffId { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "工作日期")]
        public DateTime WorkDate { get; set; }

        [Column(TypeName = "TIME")]
        [Display(Name = "開始時間")]
        public TimeSpan? StartTime { get; set; }

        [Column(TypeName = "TIME")]
        [Display(Name = "結束時間")]
        public TimeSpan? EndTime { get; set; }

        [Column(TypeName = "DECIMAL(5,2)")]
        [Display(Name = "工作時數")]
        public decimal WorkHours { get; set; }

        [Column(TypeName = "DECIMAL(5,2)")]
        [Display(Name = "加班時數")]
        public decimal OvertimeHours { get; set; }

        [StringLength(100)]
        [Display(Name = "工作內容")]
        public string WorkDescription { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        [StringLength(20)]
        [Display(Name = "狀態")]
        public string Status { get; set; } = "Pending"; // Pending, Approved, Rejected

        [Display(Name = "提交時間")]
        public DateTime SubmittedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "提交者")]
        public string SubmittedBy { get; set; }

        [Display(Name = "審核時間")]
        public DateTime? ApprovedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "審核者")]
        public string ApprovedBy { get; set; }

        // 導航屬性
        [ForeignKey("StaffId")]
        public virtual Staff Staff { get; set; }
    }

    /// <summary>
    /// 職員統計資訊
    /// </summary>
    public class StaffStatistics
    {
        public int TotalStaff { get; set; }
        public int ActiveStaff { get; set; }
        public int InactiveStaff { get; set; }
        public int Teachers { get; set; }
        public int GeneralStaff { get; set; }
        public int Administrators { get; set; }
        public int ContractsExpiringSoon { get; set; }
        public int ExpiredContracts { get; set; }
        public decimal TotalMonthlyHours { get; set; }
        public decimal AverageServiceYears { get; set; }
    }

    /// <summary>
    /// 職員搜尋條件
    /// </summary>
    public class StaffSearchCriteria
    {
        public string Keyword { get; set; }
        public string StaffType { get; set; }
        public string EmploymentType { get; set; }
        public string EmploymentStatus { get; set; }
        public string Department { get; set; }
        public DateTime? HireDateFrom { get; set; }
        public DateTime? HireDateTo { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "FullName";
        public string SortDirection { get; set; } = "ASC";
    }

    /// <summary>
    /// 常用常數定義
    /// </summary>
    public static class StaffConstants
    {
        public static class StaffTypes
        {
            public const string Teacher = "Teacher";
            public const string Staff = "Staff";
            public const string Administrator = "Administrator";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Teacher, "導師" },
                    { Staff, "職員" },
                    { Administrator, "管理員" }
                };
            }
        }

        public static class EmploymentTypes
        {
            public const string FullTime = "FullTime";
            public const string PartTime = "PartTime";
            public const string Contract = "Contract";
            public const string Volunteer = "Volunteer";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { FullTime, "全職" },
                    { PartTime, "兼職" },
                    { Contract, "合約" },
                    { Volunteer, "義工" }
                };
            }
        }

        public static class EmploymentStatuses
        {
            public const string Active = "Active";
            public const string Inactive = "Inactive";
            public const string Expired = "Expired";
            public const string Terminated = "Terminated";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Active, "在職" },
                    { Inactive, "停職" },
                    { Expired, "到期" },
                    { Terminated, "離職" }
                };
            }
        }

        public static class Genders
        {
            public const string Male = "M";
            public const string Female = "F";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Male, "男" },
                    { Female, "女" }
                };
            }
        }

        public static class ActivityRoles
        {
            public const string Teacher = "Teacher";
            public const string Assistant = "Assistant";
            public const string Coordinator = "Coordinator";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Teacher, "導師" },
                    { Assistant, "助教" },
                    { Coordinator, "統籌" }
                };
            }
        }
    }
}