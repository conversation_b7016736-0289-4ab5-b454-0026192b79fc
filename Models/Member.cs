using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using CWDECC_3S.Data;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 會員資料模型 - 管理會員基本資料與會籍狀態
    /// </summary>
    [Table("Members")]
    public class Member
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "會員號碼")]
        public string MemberNumber { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "姓名")]
        public string FullName { get; set; }

        [Required]
        [StringLength(10)]
        [Display(Name = "性別")]
        public string Gender { get; set; } // M, F

        [Column(TypeName = "DATE")]
        [Display(Name = "出生日期")]
        public DateTime? DateOfBirth { get; set; }

        [StringLength(20)]
        [Display(Name = "身份證號碼")]
        public string HKID { get; set; }

        // 加密欄位 - 電話號碼
        [StringLength(1000)] // 加密後長度
        [Display(Name = "電話號碼（加密）")]
        public string PhoneEncrypted { get; set; }

        // 加密欄位 - 電子郵件
        [StringLength(1000)] // 加密後長度
        [Display(Name = "電子郵件（加密）")]
        public string EmailEncrypted { get; set; }

        // 加密欄位 - 住址
        [StringLength(2000)] // 加密後長度
        [Display(Name = "住址（加密）")]
        public string AddressEncrypted { get; set; }

        [StringLength(20)]
        [Display(Name = "會員類型")]
        public string MemberType { get; set; } // Regular, Student, Senior, Family

        [Column(TypeName = "DATE")]
        [Display(Name = "入會日期")]
        public DateTime JoinDate { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "會籍開始日期")]
        public DateTime MembershipStartDate { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "會籍到期日期")]
        public DateTime MembershipEndDate { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "會員狀態")]
        public string MembershipStatus { get; set; } // Active, Expired, Suspended, Cancelled

        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "會費")]
        public decimal? MembershipFee { get; set; }

        [StringLength(20)]
        [Display(Name = "付款方式")]
        public string PaymentMethod { get; set; } // Cash, Octopus, CreditCard, BankTransfer, Cheque

        [StringLength(100)]
        [Display(Name = "緊急聯絡人")]
        public string EmergencyContactName { get; set; }

        [StringLength(50)]
        [Display(Name = "緊急聯絡電話")]
        public string EmergencyContactPhone { get; set; }

        [StringLength(500)]
        [Display(Name = "照片路徑")]
        public string PhotoPath { get; set; }

        [StringLength(500)]
        [Display(Name = "條碼路徑")]
        public string BarcodePath { get; set; }

        [StringLength(1000)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 系統欄位
        [Display(Name = "啟用狀態")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string ModifiedBy { get; set; }

        // 導航屬性
        public virtual ICollection<MemberActivity> MemberActivities { get; set; }

        public Member()
        {
            MemberActivities = new HashSet<MemberActivity>();
        }

        #region 非映射屬性

        /// <summary>
        /// 電話號碼（解密後）
        /// </summary>
        [NotMapped]
        [Display(Name = "電話號碼")]
        public string Phone
        {
            get
            {
                if (string.IsNullOrEmpty(PhoneEncrypted))
                    return string.Empty;

                try
                {
                    return CryptoHelper.Decrypt(PhoneEncrypted);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    PhoneEncrypted = null;
                }
                else
                {
                    PhoneEncrypted = CryptoHelper.Encrypt(value);
                }
            }
        }

        /// <summary>
        /// 電子郵件（解密後）
        /// </summary>
        [NotMapped]
        [Display(Name = "電子郵件")]
        public string Email
        {
            get
            {
                if (string.IsNullOrEmpty(EmailEncrypted))
                    return string.Empty;

                try
                {
                    return CryptoHelper.Decrypt(EmailEncrypted);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    EmailEncrypted = null;
                }
                else
                {
                    EmailEncrypted = CryptoHelper.Encrypt(value);
                }
            }
        }

        /// <summary>
        /// 住址（解密後）
        /// </summary>
        [NotMapped]
        [Display(Name = "住址")]
        public string Address
        {
            get
            {
                if (string.IsNullOrEmpty(AddressEncrypted))
                    return string.Empty;

                try
                {
                    return CryptoHelper.Decrypt(AddressEncrypted);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    AddressEncrypted = null;
                }
                else
                {
                    AddressEncrypted = CryptoHelper.Encrypt(value);
                }
            }
        }

        /// <summary>
        /// 年齡
        /// </summary>
        [NotMapped]
        [Display(Name = "年齡")]
        public int? Age
        {
            get
            {
                if (!DateOfBirth.HasValue)
                    return null;

                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Value.Year;
                if (DateOfBirth.Value.Date > today.AddYears(-age))
                    age--;

                return age;
            }
        }

        /// <summary>
        /// 會籍是否已過期
        /// </summary>
        [NotMapped]
        [Display(Name = "會籍已過期")]
        public bool IsMembershipExpired
        {
            get
            {
                return MembershipEndDate < DateTime.Today;
            }
        }

        /// <summary>
        /// 會籍是否即將到期（30天內）
        /// </summary>
        [NotMapped]
        [Display(Name = "會籍即將到期")]
        public bool IsMembershipExpiringSoon
        {
            get
            {
                if (MembershipStatus != "Active")
                    return false;

                return MembershipEndDate.Subtract(DateTime.Today).TotalDays <= 30;
            }
        }

        /// <summary>
        /// 會員到期剩餘天數
        /// </summary>
        [NotMapped]
        public int DaysUntilExpiry
        {
            get
            {
                return (MembershipEndDate - DateTime.Today).Days;
            }
        }

        /// <summary>
        /// 格式化顯示名稱
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{FullName} ({MemberNumber})";

        /// <summary>
        /// 會員狀態顯示文字
        /// </summary>
        [NotMapped]
        public string MembershipStatusDisplayText
        {
            get
            {
                if (IsMembershipExpired && MembershipStatus == "Active")
                    return "過期";
                    
                switch (MembershipStatus)
                {
                    case "Active": return "有效";
                    case "Expired": return "過期";
                    case "Suspended": return "暫停";
                    case "Cancelled": return "取消";
                    default: return MembershipStatus;
                }
            }
        }

        /// <summary>
        /// 會員類型顯示文字
        /// </summary>
        [NotMapped]
        public string MemberTypeDisplayText
        {
            get
            {
                switch (MemberType)
                {
                    case "Regular": return "一般";
                    case "Student": return "學生";
                    case "Senior": return "長者";
                    case "Family": return "家庭";
                    default: return MemberType;
                }
            }
        }

        /// <summary>
        /// 性別顯示文字
        /// </summary>
        [NotMapped]
        public string GenderDisplayText
        {
            get
            {
                return Gender == "M" ? "男" : Gender == "F" ? "女" : Gender;
            }
        }

        #endregion

        #region 業務方法

        /// <summary>
        /// 更新會員狀態（根據到期日期）
        /// </summary>
        public void UpdateMembershipStatus()
        {
            if (MembershipEndDate < DateTime.Today)
            {
                if (MembershipStatus == "Active")
                {
                    MembershipStatus = "Expired";
                    ModifiedDate = DateTime.UtcNow;
                }
            }
        }

        /// <summary>
        /// 續會
        /// </summary>
        /// <param name="months">續會月數</param>
        /// <param name="fee">續會費用</param>
        public void RenewMembership(int months, decimal fee)
        {
            // 如果已過期，從今天開始計算
            var startDate = IsMembershipExpired ? DateTime.Today : MembershipEndDate.AddDays(1);
            
            MembershipStartDate = IsMembershipExpired ? DateTime.Today : MembershipStartDate;
            MembershipEndDate = startDate.AddMonths(months);
            MembershipStatus = "Active";
            MembershipFee = fee;
            ModifiedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// 驗證資料完整性
        /// </summary>
        /// <returns>驗證結果</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(MemberNumber))
                errors.Add("會員號碼不能為空");

            if (string.IsNullOrWhiteSpace(FullName))
                errors.Add("姓名不能為空");

            if (string.IsNullOrWhiteSpace(Gender))
                errors.Add("性別不能為空");

            if (MembershipEndDate <= MembershipStartDate)
                errors.Add("會籍到期日期必須晚於開始日期");

            if (JoinDate > DateTime.Today)
                errors.Add("入會日期不能晚於今天");

            if (DateOfBirth.HasValue && DateOfBirth.Value > DateTime.Today.AddYears(-16))
                errors.Add("年齡必須至少16歲");

            return errors;
        }

        /// <summary>
        /// 生成會員號碼
        /// </summary>
        /// <param name="memberType">會員類型</param>
        /// <param name="sequence">序號</param>
        /// <returns>會員號碼</returns>
        public static string GenerateMemberNumber(string memberType, int sequence)
        {
            string prefix;
            switch (memberType)
            {
                case "Regular":
                    prefix = "R";
                    break;
                case "Student":
                    prefix = "S";
                    break;
                case "Senior":
                    prefix = "E";
                    break;
                case "Family":
                    prefix = "F";
                    break;
                default:
                    prefix = "M";
                    break;
            }

            return $"{prefix}{DateTime.Now.Year}{sequence:D4}";
        }

        #endregion
    }

    /// <summary>
    /// 會員活動參與記錄
    /// </summary>
    [Table("MemberActivities")]
    public class MemberActivity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int MemberId { get; set; }

        [Required]
        public int ActivityId { get; set; }

        [Column(TypeName = "DATE")]
        [Display(Name = "參與日期")]
        public DateTime ParticipationDate { get; set; }

        [StringLength(20)]
        [Display(Name = "參與狀態")]
        public string Status { get; set; } // Registered, Attended, NoShow, Cancelled

        [Column(TypeName = "DECIMAL(8,2)")]
        [Display(Name = "費用")]
        public decimal? Fee { get; set; }

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        [Display(Name = "記錄時間")]
        public DateTime RecordDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "記錄者")]
        public string RecordedBy { get; set; }

        // 導航屬性
        [ForeignKey("MemberId")]
        public virtual Member Member { get; set; }

        [ForeignKey("ActivityId")]
        public virtual Activity Activity { get; set; }
    }

    /// <summary>
    /// 會員搜尋條件
    /// </summary>
    public class MemberSearchCriteria
    {
        public string Keyword { get; set; }
        public string MemberType { get; set; }
        public string MembershipStatus { get; set; }
        public DateTime? JoinDateFrom { get; set; }
        public DateTime? JoinDateTo { get; set; }
        public DateTime? ExpiryDateFrom { get; set; }
        public DateTime? ExpiryDateTo { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "FullName";
        public string SortDirection { get; set; } = "ASC";
    }

    /// <summary>
    /// 會員統計資訊
    /// </summary>
    public class MemberStatistics
    {
        public int TotalMembers { get; set; }
        public int ActiveMembers { get; set; }
        public int ExpiredMembers { get; set; }
        public int ExpiringSoonMembers { get; set; }
        public int RegularMembers { get; set; }
        public int StudentMembers { get; set; }
        public int SeniorMembers { get; set; }
        public int FamilyMembers { get; set; }
        public decimal TotalMembershipRevenue { get; set; }
    }

    /// <summary>
    /// 常用常數定義
    /// </summary>
    public static class MemberConstants
    {
        public static class MemberTypes
        {
            public const string Regular = "Regular";
            public const string Student = "Student";
            public const string Senior = "Senior";
            public const string Family = "Family";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Regular, "一般" },
                    { Student, "學生" },
                    { Senior, "長者" },
                    { Family, "家庭" }
                };
            }
        }

        public static class MembershipStatuses
        {
            public const string Active = "Active";
            public const string Expired = "Expired";
            public const string Suspended = "Suspended";
            public const string Cancelled = "Cancelled";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Active, "有效" },
                    { Expired, "過期" },
                    { Suspended, "暫停" },
                    { Cancelled, "取消" }
                };
            }
        }

        public static class Genders
        {
            public const string Male = "M";
            public const string Female = "F";

            public static Dictionary<string, string> GetAll()
            {
                return new Dictionary<string, string>
                {
                    { Male, "男" },
                    { Female, "女" }
                };
            }
        }
    }
}