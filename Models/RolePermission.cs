using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 角色權限模型 - 存儲角色對模組的 CRUD 權限
    /// </summary>
    [Table("RolePermissions")]
    public class RolePermission
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(128)]
        [Index("IX_RolePermission_Role_Module", 1, IsUnique = true)]
        public string RoleId { get; set; }

        [Required]
        [StringLength(100)]
        [Index("IX_RolePermission_Role_Module", 2, IsUnique = true)]
        public string ModuleName { get; set; }

        [Display(Name = "可讀取")]
        public bool CanRead { get; set; } = false;

        [Display(Name = "可建立")]
        public bool CanCreate { get; set; } = false;

        [Display(Name = "可更新")]
        public bool CanUpdate { get; set; } = false;

        [Display(Name = "可刪除")]
        public bool CanDelete { get; set; } = false;

        [Display(Name = "建立時間")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(128)]
        [Display(Name = "建立者")]
        public string CreatedBy { get; set; }

        [Display(Name = "修改時間")]
        public DateTime? ModifiedDate { get; set; }

        [StringLength(128)]
        [Display(Name = "修改者")]
        public string ModifiedBy { get; set; }

        [Display(Name = "啟用狀態")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "備註")]
        public string Remarks { get; set; }

        // 導航屬性
        [ForeignKey("RoleId")]
        public virtual ApplicationRole Role { get; set; }

        [ForeignKey("ModuleName")]
        public virtual SystemModule Module { get; set; }

        /// <summary>
        /// 檢查是否有指定的操作權限
        /// </summary>
        /// <param name="operation">操作類型 (Create, Read, Update, Delete)</param>
        /// <returns>是否有權限</returns>
        public bool HasPermission(string operation)
        {
            if (!IsActive) return false;

            switch (operation?.ToUpper())
            {
                case "READ":
                case "VIEW":
                    return CanRead;
                case "CREATE":
                case "ADD":
                    return CanCreate;
                case "UPDATE":
                case "EDIT":
                    return CanUpdate;
                case "DELETE":
                case "REMOVE":
                    return CanDelete;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 設定操作權限
        /// </summary>
        /// <param name="operation">操作類型</param>
        /// <param name="granted">是否授予權限</param>
        public void SetPermission(string operation, bool granted)
        {
            switch (operation?.ToUpper())
            {
                case "READ":
                case "VIEW":
                    CanRead = granted;
                    break;
                case "CREATE":
                case "ADD":
                    CanCreate = granted;
                    break;
                case "UPDATE":
                case "EDIT":
                    CanUpdate = granted;
                    break;
                case "DELETE":
                case "REMOVE":
                    CanDelete = granted;
                    break;
            }
        }

        /// <summary>
        /// 取得權限摘要
        /// </summary>
        /// <returns>權限摘要字串</returns>
        public string GetPermissionSummary()
        {
            var permissions = new List<string>();
            
            if (CanRead) permissions.Add("讀取");
            if (CanCreate) permissions.Add("建立");
            if (CanUpdate) permissions.Add("更新");
            if (CanDelete) permissions.Add("刪除");

            return permissions.Any() ? string.Join(", ", permissions) : "無權限";
        }

        /// <summary>
        /// 複製權限設定
        /// </summary>
        /// <param name="source">來源權限</param>
        public void CopyPermissionsFrom(RolePermission source)
        {
            if (source == null) return;

            CanRead = source.CanRead;
            CanCreate = source.CanCreate;
            CanUpdate = source.CanUpdate;
            CanDelete = source.CanDelete;
        }
    }
}