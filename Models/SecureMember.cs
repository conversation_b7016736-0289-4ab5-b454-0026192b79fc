using System;
using System.ComponentModel.DataAnnotations;
using Google.Cloud.Firestore;
using CWDECC_3S.Data;

namespace CWDECC_3S.Models
{
    /// <summary>
    /// 安全會員模型 - 敏感資料自動加密
    /// </summary>
    [FirestoreData]
    public class SecureMember
    {
        [FirestoreProperty]
        public string Id { get; set; }

        // 敏感資料欄位 - 自動加密儲存
        private string _encryptedName;
        private string _encryptedHKID;
        private string _encryptedPhone;
        private string _encryptedAddress;

        [FirestoreProperty("encrypted_name")]
        public string EncryptedName
        {
            get => _encryptedName;
            set => _encryptedName = value;
        }

        [FirestoreProperty("encrypted_hkid")]
        public string EncryptedHKID
        {
            get => _encryptedHKID;
            set => _encryptedHKID = value;
        }

        [FirestoreProperty("encrypted_phone")]
        public string EncryptedPhone
        {
            get => _encryptedPhone;
            set => _encryptedPhone = value;
        }

        [FirestoreProperty("encrypted_address")]
        public string EncryptedAddress
        {
            get => _encryptedAddress;
            set => _encryptedAddress = value;
        }

        // 明文屬性 - 透過加密/解密存取敏感資料
        [Display(Name = "姓名")]
        public string Name
        {
            get
            {
                try
                {
                    return string.IsNullOrEmpty(_encryptedName) ? 
                        string.Empty : 
                        SecureDataProtector.Decrypt(_encryptedName);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                try
                {
                    _encryptedName = string.IsNullOrEmpty(value) ? 
                        string.Empty : 
                        SecureDataProtector.Encrypt(value);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"姓名加密失敗: {ex.Message}", ex);
                }
            }
        }

        [Display(Name = "香港身份證")]
        public string HKID
        {
            get
            {
                try
                {
                    return string.IsNullOrEmpty(_encryptedHKID) ? 
                        string.Empty : 
                        SecureDataProtector.Decrypt(_encryptedHKID);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                try
                {
                    _encryptedHKID = string.IsNullOrEmpty(value) ? 
                        string.Empty : 
                        SecureDataProtector.Encrypt(value);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"身份證加密失敗: {ex.Message}", ex);
                }
            }
        }

        [Display(Name = "電話號碼")]
        public string Phone
        {
            get
            {
                try
                {
                    return string.IsNullOrEmpty(_encryptedPhone) ? 
                        string.Empty : 
                        SecureDataProtector.Decrypt(_encryptedPhone);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                try
                {
                    _encryptedPhone = string.IsNullOrEmpty(value) ? 
                        string.Empty : 
                        SecureDataProtector.Encrypt(value);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"電話號碼加密失敗: {ex.Message}", ex);
                }
            }
        }

        [Display(Name = "地址")]
        public string Address
        {
            get
            {
                try
                {
                    return string.IsNullOrEmpty(_encryptedAddress) ? 
                        string.Empty : 
                        SecureDataProtector.Decrypt(_encryptedAddress);
                }
                catch
                {
                    return "[解密失敗]";
                }
            }
            set
            {
                try
                {
                    _encryptedAddress = string.IsNullOrEmpty(value) ? 
                        string.Empty : 
                        SecureDataProtector.Encrypt(value);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"地址加密失敗: {ex.Message}", ex);
                }
            }
        }

        // 非敏感資料欄位 - 明文儲存
        [FirestoreProperty]
        [Required(ErrorMessage = "電子郵件為必填項目")]
        [EmailAddress(ErrorMessage = "請輸入有效的電子郵件地址")]
        [Display(Name = "電子郵件")]
        public string Email { get; set; }

        [FirestoreProperty]
        [Display(Name = "出生日期")]
        public DateTime? DateOfBirth { get; set; }

        [FirestoreProperty]
        [Display(Name = "性別")]
        public string Gender { get; set; }

        [FirestoreProperty]
        [Display(Name = "會員狀態")]
        public string Status { get; set; } = "Active";

        [FirestoreProperty]
        [Display(Name = "註冊日期")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [FirestoreProperty]
        [Display(Name = "最後更新日期")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [FirestoreProperty]
        [Display(Name = "備註")]
        public string Notes { get; set; }

        // 加密中繼資料
        [FirestoreProperty]
        public byte EncryptionVersion { get; set; } = 1;

        [FirestoreProperty]
        public DateTime EncryptionDate { get; set; } = DateTime.Now;

        // Constructor
        public SecureMember()
        {
            Id = Guid.NewGuid().ToString();
            CreatedDate = DateTime.Now;
            LastUpdated = DateTime.Now;
            Status = "Active";
            EncryptionVersion = 1;
            EncryptionDate = DateTime.Now;
        }

        /// <summary>
        /// 驗證敏感資料是否可正常解密
        /// </summary>
        /// <returns>驗證結果</returns>
        public EncryptionValidationResult ValidateEncryption()
        {
            var result = new EncryptionValidationResult { IsValid = true };
            
            try
            {
                // 測試姓名解密
                if (!string.IsNullOrEmpty(_encryptedName))
                {
                    var testName = SecureDataProtector.Decrypt(_encryptedName);
                    result.DecryptedFields.Add("Name");
                }

                // 測試身份證解密
                if (!string.IsNullOrEmpty(_encryptedHKID))
                {
                    var testHKID = SecureDataProtector.Decrypt(_encryptedHKID);
                    result.DecryptedFields.Add("HKID");
                }

                // 測試電話解密
                if (!string.IsNullOrEmpty(_encryptedPhone))
                {
                    var testPhone = SecureDataProtector.Decrypt(_encryptedPhone);
                    result.DecryptedFields.Add("Phone");
                }

                // 測試地址解密
                if (!string.IsNullOrEmpty(_encryptedAddress))
                {
                    var testAddress = SecureDataProtector.Decrypt(_encryptedAddress);
                    result.DecryptedFields.Add("Address");
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 重新加密所有敏感資料（用於密鑰輪換）
        /// </summary>
        public void ReencryptSensitiveData()
        {
            try
            {
                // 先解密所有資料
                string tempName = Name;
                string tempHKID = HKID;
                string tempPhone = Phone;
                string tempAddress = Address;

                // 重新加密
                Name = tempName;
                HKID = tempHKID;
                Phone = tempPhone;
                Address = tempAddress;

                // 更新加密中繼資料
                EncryptionVersion = 1; // 當前版本
                EncryptionDate = DateTime.Now;
                LastUpdated = DateTime.Now;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"重新加密失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得遮罩後的敏感資料（用於顯示）
        /// </summary>
        /// <returns>遮罩後的會員資料</returns>
        public SecureMemberDisplayInfo GetMaskedDisplayInfo()
        {
            return new SecureMemberDisplayInfo
            {
                Id = Id,
                MaskedName = MaskString(Name, 1, 1),
                MaskedHKID = MaskHKID(HKID),
                MaskedPhone = MaskPhone(Phone),
                MaskedAddress = MaskString(Address, 3, 3),
                Email = Email,
                Gender = Gender,
                Status = Status,
                CreatedDate = CreatedDate
            };
        }

        // 私有遮罩方法
        private string MaskString(string input, int prefixKeep, int suffixKeep)
        {
            if (string.IsNullOrEmpty(input) || input.Length <= prefixKeep + suffixKeep)
                return input;

            string prefix = input.Substring(0, prefixKeep);
            string suffix = input.Substring(input.Length - suffixKeep);
            string mask = new string('*', Math.Max(1, input.Length - prefixKeep - suffixKeep));

            return prefix + mask + suffix;
        }

        private string MaskHKID(string hkid)
        {
            if (string.IsNullOrEmpty(hkid) || hkid.Length < 4)
                return hkid;

            // 只顯示前面字母和最後一位
            return hkid.Substring(0, 2) + "****" + hkid.Substring(hkid.Length - 1);
        }

        private string MaskPhone(string phone)
        {
            if (string.IsNullOrEmpty(phone) || phone.Length < 4)
                return phone;

            // 只顯示前 2 位和後 2 位
            return phone.Substring(0, 2) + "****" + phone.Substring(phone.Length - 2);
        }
    }

    /// <summary>
    /// 加密驗證結果
    /// </summary>
    public class EncryptionValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public System.Collections.Generic.List<string> DecryptedFields { get; set; } = new System.Collections.Generic.List<string>();
    }

    /// <summary>
    /// 遮罩後的會員顯示資訊
    /// </summary>
    public class SecureMemberDisplayInfo
    {
        public string Id { get; set; }
        public string MaskedName { get; set; }
        public string MaskedHKID { get; set; }
        public string MaskedPhone { get; set; }
        public string MaskedAddress { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}