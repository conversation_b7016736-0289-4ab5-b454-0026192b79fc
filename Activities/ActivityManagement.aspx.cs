using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Models;
using CWDECC_3S.Services;

namespace CWDECC_3S.Activities
{
    public partial class ActivityManagement : System.Web.UI.Page
    {
        private readonly ActivityService _activityService;
        private readonly AuditService _auditService;
        private readonly PermissionService _permissionService;
        private readonly string _currentUserId;

        public ActivityManagement()
        {
            _activityService = new ActivityService();
            _auditService = new AuditService();
            _permissionService = new PermissionService();
            _currentUserId = HttpContext.Current?.Session["UserId"]?.ToString() ?? "system";
        }

        protected async void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限 - 只有管理員、社工和前台職員可以管理活動
                if (!_permissionService.HasRolePermission(_currentUserId, "Administrator") &&
                    !_permissionService.HasRolePermission(_currentUserId, "SocialWorker") &&
                    !_permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    await InitializePageAsync();
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "ActivityManagement", _currentUserId,
                    $"頁面載入錯誤: {ex.Message}", null, "ERROR");
                ShowMessage($"系統錯誤：{ex.Message}", "error");
            }
        }

        private async Task InitializePageAsync()
        {
            try
            {
                // 初始化下拉式清單
                InitializeDropDownLists();

                // 載入統計資訊
                await LoadStatisticsAsync();

                // 載入活動列表
                await LoadActivitiesAsync();

                // 記錄頁面訪問
                await _auditService.LogAsync("PAGE_ACCESS", "ActivityManagement", _currentUserId,
                    "訪問活動管理頁面", null, "SUCCESS");
            }
            catch (Exception ex)
            {
                ShowMessage($"初始化錯誤：{ex.Message}", "error");
            }
        }

        private void InitializeDropDownLists()
        {
            try
            {
                // 初始化活動狀態下拉式清單
                ddlStatus.Items.Clear();
                ddlStatus.Items.Add(new ListItem("全部", ""));
                foreach (var status in ActivityConstants.Status.GetAll())
                {
                    ddlStatus.Items.Add(new ListItem(status.Value, status.Key));
                }

                // 初始化對象限制下拉式清單
                ddlTargetAudience.Items.Clear();
                ddlTargetAudience.Items.Add(new ListItem("全部", ""));
                foreach (var audience in ActivityConstants.TargetAudience.GetAll())
                {
                    ddlTargetAudience.Items.Add(new ListItem(audience.Value, audience.Key));
                }

                // 初始化模態視窗中的對象限制下拉式清單
                ddlTargetAudienceModal.Items.Clear();
                ddlTargetAudienceModal.Items.Add(new ListItem("請選擇", ""));
                foreach (var audience in ActivityConstants.TargetAudience.GetAll())
                {
                    ddlTargetAudienceModal.Items.Add(new ListItem(audience.Value, audience.Key));
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"初始化下拉式清單錯誤：{ex.Message}", "warning");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var statistics = await _activityService.GetActivityStatisticsAsync();

                lblTotalActivities.Text = statistics.TotalActivities.ToString();
                lblUpcomingActivities.Text = statistics.UpcomingActivities.ToString();
                lblOngoingRegistrations.Text = statistics.OngoingRegistrations.ToString();
                lblTotalParticipants.Text = statistics.TotalParticipants.ToString();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入統計資訊錯誤：{ex.Message}", "warning");
            }
        }

        private async Task LoadActivitiesAsync()
        {
            try
            {
                var criteria = GetSearchCriteria();
                var (activities, totalCount) = await _activityService.SearchActivitiesAsync(criteria, _currentUserId);

                gvActivities.DataSource = activities;
                gvActivities.DataBind();

                lblRecordCount.Text = totalCount.ToString();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入活動列表錯誤：{ex.Message}", "error");
            }
        }

        private ActivitySearchCriteria GetSearchCriteria()
        {
            var criteria = new ActivitySearchCriteria
            {
                Keyword = txtKeyword.Text.Trim(),
                Status = ddlStatus.SelectedValue,
                TargetAudience = ddlTargetAudience.SelectedValue,
                PageNumber = gvActivities.PageIndex + 1,
                PageSize = gvActivities.PageSize
            };

            if (!string.IsNullOrEmpty(txtDateFrom.Text))
            {
                DateTime.TryParse(txtDateFrom.Text, out var dateFrom);
                criteria.DateFrom = dateFrom;
            }

            if (!string.IsNullOrEmpty(txtDateTo.Text))
            {
                DateTime.TryParse(txtDateTo.Text, out var dateTo);
                criteria.DateTo = dateTo;
            }

            return criteria;
        }

        protected async void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                gvActivities.PageIndex = 0; // 重置到第一頁
                await LoadActivitiesAsync();

                // 記錄搜尋操作
                await _auditService.LogAsync("ACTIVITY_SEARCH", "ActivityManagement", _currentUserId,
                    $"搜尋活動：關鍵字={txtKeyword.Text}, 狀態={ddlStatus.SelectedValue}", null, "SUCCESS");
            }
            catch (Exception ex)
            {
                ShowMessage($"搜尋錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnClear_Click(object sender, EventArgs e)
        {
            try
            {
                txtKeyword.Text = "";
                ddlStatus.SelectedIndex = 0;
                ddlTargetAudience.SelectedIndex = 0;
                txtDateFrom.Text = "";
                txtDateTo.Text = "";

                gvActivities.PageIndex = 0;
                await LoadActivitiesAsync();
            }
            catch (Exception ex)
            {
                ShowMessage($"清除搜尋條件錯誤：{ex.Message}", "warning");
            }
        }

        protected async void btnAddActivity_Click(object sender, EventArgs e)
        {
            try
            {
                // 重置模態視窗表單
                ClearModalForm();
                lblModalTitle.Text = "新增活動";
                hdnActivityId.Value = "";

                // 顯示模態視窗
                ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showActivityModal();", true);

                // 記錄操作
                await _auditService.LogAsync("ACTIVITY_ADD_START", "ActivityManagement", _currentUserId,
                    "開始新增活動", null, "SUCCESS");
            }
            catch (Exception ex)
            {
                ShowMessage($"開啟新增活動錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnUpdateStatuses_Click(object sender, EventArgs e)
        {
            try
            {
                var updatedCount = await _activityService.UpdateAllActivityStatusesAsync(_currentUserId);

                if (updatedCount > 0)
                {
                    ShowMessage($"成功更新 {updatedCount} 個活動的狀態", "success");
                    await LoadActivitiesAsync();
                    await LoadStatisticsAsync();
                }
                else
                {
                    ShowMessage("所有活動狀態已是最新", "info");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"更新活動狀態錯誤：{ex.Message}", "error");
            }
        }

        protected async void gvActivities_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (int.TryParse(e.CommandArgument.ToString(), out int activityId))
                {
                    switch (e.CommandName)
                    {
                        case "ViewActivity":
                            await ViewActivity(activityId);
                            break;
                        case "EditActivity":
                            await EditActivity(activityId);
                            break;
                        case "DeleteActivity":
                            await DeleteActivity(activityId);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"執行操作錯誤：{ex.Message}", "error");
            }
        }

        private async Task ViewActivity(int activityId)
        {
            try
            {
                var activity = await _activityService.GetActivityByIdAsync(activityId);
                if (activity != null)
                {
                    // 可以跳轉到活動詳情頁面或在模態視窗中顯示
                    Response.Redirect($"~/Activities/ActivityDetails.aspx?id={activityId}");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"查看活動錯誤：{ex.Message}", "error");
            }
        }

        private async Task EditActivity(int activityId)
        {
            try
            {
                var activity = await _activityService.GetActivityByIdAsync(activityId);
                if (activity != null)
                {
                    // 填充模態視窗表單
                    PopulateModalForm(activity);
                    lblModalTitle.Text = "編輯活動";
                    hdnActivityId.Value = activityId.ToString();

                    // 顯示模態視窗
                    ScriptManager.RegisterStartupScript(this, GetType(), "showModal", "showActivityModal();", true);

                    // 記錄操作
                    await _auditService.LogAsync("ACTIVITY_EDIT_START", "ActivityManagement", _currentUserId,
                        $"開始編輯活動：{activity.ActivityName}", activityId.ToString(), "SUCCESS");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入編輯活動錯誤：{ex.Message}", "error");
            }
        }

        private async Task DeleteActivity(int activityId)
        {
            try
            {
                var result = await _activityService.DeleteActivityAsync(activityId, _currentUserId);

                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    await LoadActivitiesAsync();
                    await LoadStatisticsAsync();
                }
                else
                {
                    ShowMessage(result.Message, "error");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"刪除活動錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnSaveActivity_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                {
                    ShowMessage("請檢查表單資料是否正確填寫", "warning");
                    return;
                }

                var isEdit = !string.IsNullOrEmpty(hdnActivityId.Value);
                var activity = CreateActivityFromForm();

                ActivityOperationResult result;
                
                if (isEdit)
                {
                    activity.Id = int.Parse(hdnActivityId.Value);
                    result = await _activityService.UpdateActivityAsync(activity, _currentUserId);
                }
                else
                {
                    result = await _activityService.CreateActivityAsync(activity, _currentUserId);
                }

                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    await LoadActivitiesAsync();
                    await LoadStatisticsAsync();
                    
                    // 關閉模態視窗
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideModal", 
                        "$('#activityModal').modal('hide');", true);
                }
                else
                {
                    var errorMessages = string.Join("<br/>", result.Errors);
                    ShowMessage($"{result.Message}<br/>{errorMessages}", "error");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"儲存活動錯誤：{ex.Message}", "error");
            }
        }

        private Activity CreateActivityFromForm()
        {
            var activity = new Activity
            {
                ActivityName = txtActivityName.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                ActivityDate = DateTime.Parse(txtActivityDate.Text),
                StartTime = TimeSpan.Parse(txtStartTime.Text),
                EndTime = string.IsNullOrEmpty(txtEndTime.Text) ? (TimeSpan?)null : TimeSpan.Parse(txtEndTime.Text),
                MaxParticipants = int.Parse(txtMaxParticipants.Text),
                TargetAudience = ddlTargetAudienceModal.SelectedValue,
                Venue = txtVenue.Text.Trim(),
                ContactPerson = txtContactPerson.Text.Trim(),
                ContactPhone = txtContactPhone.Text.Trim(),
                Remarks = txtRemarks.Text.Trim()
            };

            if (!string.IsNullOrEmpty(txtActivityFee.Text))
            {
                activity.ActivityFee = decimal.Parse(txtActivityFee.Text);
            }

            if (!string.IsNullOrEmpty(txtRegistrationDeadline.Text))
            {
                activity.RegistrationDeadline = DateTime.Parse(txtRegistrationDeadline.Text);
            }

            return activity;
        }

        private void PopulateModalForm(Activity activity)
        {
            txtActivityName.Text = activity.ActivityName;
            txtDescription.Text = activity.Description;
            txtActivityDate.Text = activity.ActivityDate.ToString("yyyy-MM-dd");
            txtStartTime.Text = activity.StartTime.ToString(@"hh\:mm");
            txtEndTime.Text = activity.EndTime?.ToString(@"hh\:mm") ?? "";
            txtMaxParticipants.Text = activity.MaxParticipants.ToString();
            ddlTargetAudienceModal.SelectedValue = activity.TargetAudience ?? "";
            txtActivityFee.Text = activity.ActivityFee?.ToString("F2") ?? "";
            txtVenue.Text = activity.Venue ?? "";
            txtContactPerson.Text = activity.ContactPerson ?? "";
            txtContactPhone.Text = activity.ContactPhone ?? "";
            txtRegistrationDeadline.Text = activity.RegistrationDeadline?.ToString("yyyy-MM-dd") ?? "";
            txtRemarks.Text = activity.Remarks ?? "";
        }

        private void ClearModalForm()
        {
            txtActivityName.Text = "";
            txtDescription.Text = "";
            txtActivityDate.Text = "";
            txtStartTime.Text = "";
            txtEndTime.Text = "";
            txtMaxParticipants.Text = "";
            ddlTargetAudienceModal.SelectedIndex = 0;
            txtActivityFee.Text = "";
            txtVenue.Text = "";
            txtContactPerson.Text = "";
            txtContactPhone.Text = "";
            txtRegistrationDeadline.Text = "";
            txtRemarks.Text = "";
        }

        protected void gvActivities_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                var activity = (Activity)e.Row.DataItem;
                
                // 設置按鈕權限
                var btnEdit = (LinkButton)e.Row.FindControl("btnEdit");
                var btnDelete = (LinkButton)e.Row.FindControl("btnDelete");

                if (btnEdit != null)
                {
                    btnEdit.Visible = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                                     _permissionService.HasRolePermission(_currentUserId, "SocialWorker");
                }

                if (btnDelete != null)
                {
                    btnDelete.Visible = _permissionService.HasRolePermission(_currentUserId, "Administrator");
                }
            }
        }

        protected async void gvActivities_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            try
            {
                gvActivities.PageIndex = e.NewPageIndex;
                await LoadActivitiesAsync();
            }
            catch (Exception ex)
            {
                ShowMessage($"分頁錯誤：{ex.Message}", "error");
            }
        }

        // 輔助方法：取得名額標籤顏色
        public string GetParticipantsBadgeColor(int current, int max)
        {
            var percentage = (double)current / max;
            if (percentage >= 1.0) return "danger";
            if (percentage >= 0.8) return "warning";
            return "success";
        }

        // 輔助方法：取得狀態標籤顏色
        public string GetStatusBadgeColor(string status)
        {
            return status switch
            {
                ActivityConstants.Status.Registration => "success",
                ActivityConstants.Status.Full => "warning",
                ActivityConstants.Status.Closed => "secondary",
                ActivityConstants.Status.Cancelled => "danger",
                _ => "secondary"
            };
        }

        // 輔助方法：格式化活動費用
        public string FormatActivityFee(object fee)
        {
            if (fee == null || fee == DBNull.Value)
                return "免費";

            var feeValue = Convert.ToDecimal(fee);
            return feeValue == 0 ? "免費" : $"HK${feeValue:F2}";
        }

        private void ShowMessage(string message, string type)
        {
            try
            {
                pnlMessage.Visible = true;
                ltlMessage.Text = message;

                // 設定訊息樣式
                pnlMessage.CssClass = type switch
                {
                    "success" => "alert alert-success alert-dismissible fade show",
                    "error" => "alert alert-danger alert-dismissible fade show",
                    "warning" => "alert alert-warning alert-dismissible fade show",
                    "info" => "alert alert-info alert-dismissible fade show",
                    _ => "alert alert-info alert-dismissible fade show"
                };

                // 自動隱藏成功訊息
                if (type == "success")
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideSuccess",
                        "setTimeout(function() { $('.alert-success').fadeOut(); }, 5000);", true);
                }
            }
            catch
            {
                // 如果顯示訊息失敗，至少記錄到瀏覽器控制台
                ScriptManager.RegisterStartupScript(this, GetType(), "consoleLog",
                    $"console.log('訊息: {message}');", true);
            }
        }

        protected override void OnUnload(EventArgs e)
        {
            _activityService?.Dispose();
            _auditService?.Dispose();
            base.OnUnload(e);
        }
    }
}