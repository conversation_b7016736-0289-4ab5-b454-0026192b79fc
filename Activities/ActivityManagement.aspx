<%@ Page Title="活動管理" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ActivityManagement.aspx.cs" Inherits="CWDECC_3S.Activities.ActivityManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-calendar-alt text-primary me-2"></i>活動管理
                </h2>
                <p class="text-muted">建立、編輯、刪除活動，設定名額、費用、對象限制</p>
            </div>
            <div>
                <asp:Button ID="btnAddActivity" runat="server" Text="新增活動" CssClass="btn btn-success"
                    OnClick="btnAddActivity_Click" />
                <asp:Button ID="btnUpdateStatuses" runat="server" Text="更新活動狀態" CssClass="btn btn-warning"
                    OnClick="btnUpdateStatuses_Click" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <!-- 統計資訊卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-primary text-white rounded-circle shadow">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">總活動數</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblTotalActivities" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-success text-white rounded-circle shadow">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">即將舉行</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblUpcomingActivities" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-warning text-white rounded-circle shadow">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">報名中</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblOngoingRegistrations" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-info text-white rounded-circle shadow">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">總參與人數</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblTotalParticipants" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜尋區域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>搜尋條件
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="<%= txtKeyword.ClientID %>">搜尋關鍵字</label>
                            <asp:TextBox ID="txtKeyword" runat="server" CssClass="form-control" 
                                placeholder="活動名稱、說明、地點、聯絡人"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="<%= ddlStatus.ClientID %>">活動狀態</label>
                            <asp:DropDownList ID="ddlStatus" runat="server" CssClass="form-select">
                                <asp:ListItem Text="全部" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="<%= ddlTargetAudience.ClientID %>">對象限制</label>
                            <asp:DropDownList ID="ddlTargetAudience" runat="server" CssClass="form-select">
                                <asp:ListItem Text="全部" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="<%= txtDateFrom.ClientID %>">日期起</label>
                            <asp:TextBox ID="txtDateFrom" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="<%= txtDateTo.ClientID %>">日期至</label>
                            <asp:TextBox ID="txtDateTo" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-grid">
                                <asp:Button ID="btnSearch" runat="server" Text="搜尋" CssClass="btn btn-primary"
                                    OnClick="btnSearch_Click" />
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-3">
                        <div class="form-group">
                            <asp:Button ID="btnClear" runat="server" Text="清除條件" CssClass="btn btn-outline-secondary"
                                OnClick="btnClear_Click" CausesValidation="false" />
                        </div>
                    </div>
                    <div class="col-md-9 text-end">
                        <small class="text-muted">
                            搜尋結果數量：<asp:Label ID="lblRecordCount" runat="server" Text="0"></asp:Label> 筆
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活動列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>活動列表
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <asp:GridView ID="gvActivities" runat="server" CssClass="table table-striped table-hover"
                        AutoGenerateColumns="False" EmptyDataText="沒有找到符合條件的活動資料"
                        OnRowCommand="gvActivities_RowCommand" OnRowDataBound="gvActivities_RowDataBound"
                        AllowPaging="True" PageSize="20" OnPageIndexChanging="gvActivities_PageIndexChanging">
                        <Columns>
                            <asp:BoundField DataField="ActivityName" HeaderText="活動名稱" SortExpression="ActivityName">
                                <HeaderStyle Width="20%" />
                            </asp:BoundField>
                            <asp:BoundField DataField="ActivityDate" HeaderText="活動日期" SortExpression="ActivityDate" 
                                DataFormatString="{0:yyyy-MM-dd}">
                                <HeaderStyle Width="10%" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="時間">
                                <ItemTemplate>
                                    <%# Eval("TimeDisplay") %>
                                </ItemTemplate>
                                <HeaderStyle Width="12%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="名額">
                                <ItemTemplate>
                                    <span class="badge bg-<%# GetParticipantsBadgeColor(Convert.ToInt32(Eval("CurrentParticipants")), Convert.ToInt32(Eval("MaxParticipants"))) %>">
                                        <%# Eval("CurrentParticipants") %>/<%# Eval("MaxParticipants") %>
                                    </span>
                                </ItemTemplate>
                                <HeaderStyle Width="8%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="費用">
                                <ItemTemplate>
                                    <%# FormatActivityFee(Eval("ActivityFee")) %>
                                </ItemTemplate>
                                <HeaderStyle Width="8%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="對象">
                                <ItemTemplate>
                                    <%# Eval("TargetAudienceDisplayText") %>
                                </ItemTemplate>
                                <HeaderStyle Width="10%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="狀態">
                                <ItemTemplate>
                                    <span class='badge bg-<%# GetStatusBadgeColor(Eval("Status").ToString()) %>'>
                                        <%# Eval("StatusDisplayText") %>
                                    </span>
                                </ItemTemplate>
                                <HeaderStyle Width="10%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="地點">
                                <ItemTemplate>
                                    <%# Eval("Venue") %>
                                </ItemTemplate>
                                <HeaderStyle Width="12%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="操作">
                                <ItemTemplate>
                                    <div class="btn-group" role="group">
                                        <asp:LinkButton ID="btnView" runat="server" 
                                            CommandName="ViewActivity" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-info btn-sm" 
                                            ToolTip="查看詳情">
                                            <i class="fas fa-eye"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnEdit" runat="server" 
                                            CommandName="EditActivity" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-warning btn-sm" 
                                            ToolTip="編輯">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnDelete" runat="server" 
                                            CommandName="DeleteActivity" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-danger btn-sm" 
                                            ToolTip="刪除"
                                            OnClientClick="return confirm('確定要刪除此活動嗎？這將同時刪除所有報名記錄，此操作無法復原。');">
                                            <i class="fas fa-trash"></i>
                                        </asp:LinkButton>
                                    </div>
                                </ItemTemplate>
                                <HeaderStyle Width="10%" />
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="table-dark" />
                        <EmptyDataRowStyle CssClass="text-center text-muted" />
                        <PagerStyle CssClass="pagination-wrapper" />
                    </asp:GridView>
                </div>
            </div>
        </div>

        <!-- 新增/編輯活動模態視窗 -->
        <div class="modal fade" id="activityModal" tabindex="-1" aria-labelledby="activityModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="activityModalLabel">
                            <i class="fas fa-calendar-plus me-2"></i>
                            <asp:Label ID="lblModalTitle" runat="server" Text="新增活動"></asp:Label>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <asp:HiddenField ID="hdnActivityId" runat="server" />
                        
                        <div class="row">
                            <!-- 基本資訊 -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>基本資訊
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtActivityName.ClientID %>" class="form-label">
                                        活動名稱 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtActivityName" runat="server" CssClass="form-control" 
                                        placeholder="請輸入活動名稱" MaxLength="100"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvActivityName" runat="server" 
                                        ControlToValidate="txtActivityName" ErrorMessage="請輸入活動名稱"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= ddlTargetAudienceModal.ClientID %>" class="form-label">
                                        對象限制 <span class="text-danger">*</span>
                                    </label>
                                    <asp:DropDownList ID="ddlTargetAudienceModal" runat="server" CssClass="form-select">
                                        <asp:ListItem Text="請選擇" Value=""></asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvTargetAudience" runat="server" 
                                        ControlToValidate="ddlTargetAudienceModal" InitialValue="" ErrorMessage="請選擇對象限制"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="<%= txtDescription.ClientID %>" class="form-label">活動說明</label>
                                    <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" 
                                        TextMode="MultiLine" Rows="3" placeholder="請輸入活動說明" MaxLength="1000"></asp:TextBox>
                                </div>
                            </div>

                            <!-- 時間與地點 -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3 mt-3">
                                    <i class="fas fa-clock me-2"></i>時間與地點
                                </h6>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="<%= txtActivityDate.ClientID %>" class="form-label">
                                        活動日期 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtActivityDate" runat="server" CssClass="form-control" 
                                        TextMode="Date"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvActivityDate" runat="server" 
                                        ControlToValidate="txtActivityDate" ErrorMessage="請選擇活動日期"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="<%= txtStartTime.ClientID %>" class="form-label">
                                        開始時間 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtStartTime" runat="server" CssClass="form-control" 
                                        TextMode="Time"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvStartTime" runat="server" 
                                        ControlToValidate="txtStartTime" ErrorMessage="請選擇開始時間"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="<%= txtEndTime.ClientID %>" class="form-label">結束時間</label>
                                    <asp:TextBox ID="txtEndTime" runat="server" CssClass="form-control" 
                                        TextMode="Time"></asp:TextBox>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtVenue.ClientID %>" class="form-label">舉辦地點</label>
                                    <asp:TextBox ID="txtVenue" runat="server" CssClass="form-control" 
                                        placeholder="請輸入舉辦地點" MaxLength="200"></asp:TextBox>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtRegistrationDeadline.ClientID %>" class="form-label">報名截止日期</label>
                                    <asp:TextBox ID="txtRegistrationDeadline" runat="server" CssClass="form-control" 
                                        TextMode="Date"></asp:TextBox>
                                </div>
                            </div>

                            <!-- 名額與費用 -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3 mt-3">
                                    <i class="fas fa-users me-2"></i>名額與費用
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtMaxParticipants.ClientID %>" class="form-label">
                                        最大名額 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtMaxParticipants" runat="server" CssClass="form-control" 
                                        TextMode="Number" min="1" placeholder="請輸入最大名額"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvMaxParticipants" runat="server" 
                                        ControlToValidate="txtMaxParticipants" ErrorMessage="請輸入最大名額"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RequiredFieldValidator>
                                    <asp:RangeValidator ID="rvMaxParticipants" runat="server" 
                                        ControlToValidate="txtMaxParticipants" Type="Integer" MinimumValue="1" MaximumValue="1000"
                                        ErrorMessage="名額必須在 1-1000 之間" CssClass="text-danger small" Display="Dynamic" ValidationGroup="ActivityModal"></asp:RangeValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtActivityFee.ClientID %>" class="form-label">活動費用</label>
                                    <div class="input-group">
                                        <span class="input-group-text">HK$</span>
                                        <asp:TextBox ID="txtActivityFee" runat="server" CssClass="form-control" 
                                            TextMode="Number" step="0.01" min="0" placeholder="0.00"></asp:TextBox>
                                    </div>
                                </div>
                            </div>

                            <!-- 聯絡資訊 -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3 mt-3">
                                    <i class="fas fa-phone me-2"></i>聯絡資訊
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtContactPerson.ClientID %>" class="form-label">聯絡人</label>
                                    <asp:TextBox ID="txtContactPerson" runat="server" CssClass="form-control" 
                                        placeholder="請輸入聯絡人姓名" MaxLength="100"></asp:TextBox>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtContactPhone.ClientID %>" class="form-label">聯絡電話</label>
                                    <asp:TextBox ID="txtContactPhone" runat="server" CssClass="form-control" 
                                        placeholder="請輸入聯絡電話" MaxLength="50"></asp:TextBox>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="<%= txtRemarks.ClientID %>" class="form-label">備註</label>
                                    <asp:TextBox ID="txtRemarks" runat="server" CssClass="form-control" 
                                        TextMode="MultiLine" Rows="3" placeholder="請輸入備註資訊" MaxLength="1000"></asp:TextBox>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <asp:Button ID="btnSaveActivity" runat="server" Text="儲存" CssClass="btn btn-success"
                            OnClick="btnSaveActivity_Click" ValidationGroup="ActivityModal" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-stats {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }
        
        .card-stats:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .btn-group .btn {
            margin-right: 2px;
        }
        
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        
        .modal-body h6 {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }
        
        @media print {
            .btn, .card-header, .pagination-wrapper {
                display: none !important;
            }
            
            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 處理模態視窗顯示
            window.showActivityModal = function() {
                $('#activityModal').modal('show');
            };

            // 表單驗證和提交
            function validateForm() {
                var isValid = true;
                
                // 驗證活動日期不能早於今天
                var activityDate = $('#<%= txtActivityDate.ClientID %>').val();
                if (activityDate) {
                    var selectedDate = new Date(activityDate);
                    var today = new Date();
                    today.setHours(0, 0, 0, 0);
                    
                    if (selectedDate < today) {
                        alert('活動日期不能早於今天');
                        isValid = false;
                    }
                }
                
                // 驗證結束時間必須晚於開始時間
                var startTime = $('#<%= txtStartTime.ClientID %>').val();
                var endTime = $('#<%= txtEndTime.ClientID %>').val();
                
                if (startTime && endTime && endTime <= startTime) {
                    alert('結束時間必須晚於開始時間');
                    isValid = false;
                }
                
                // 驗證報名截止日期不能晚於活動日期
                var registrationDeadline = $('#<%= txtRegistrationDeadline.ClientID %>').val();
                if (activityDate && registrationDeadline) {
                    var activityDateObj = new Date(activityDate);
                    var deadlineObj = new Date(registrationDeadline);
                    
                    if (deadlineObj > activityDateObj) {
                        alert('報名截止日期不能晚於活動日期');
                        isValid = false;
                    }
                }
                
                return isValid;
            }
            
            // 綁定表單提交事件
            $('#<%= btnSaveActivity.ClientID %>').click(function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return false;
                }
            });
        });

        // 顯示載入指示器
        function showLoading(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: message || '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }
    </script>
</asp:Content>