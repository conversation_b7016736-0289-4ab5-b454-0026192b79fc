<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="CWDECC_3S.SiteMaster" %>

<!DOCTYPE html>
<html lang="zh-HK">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><%: Page.Title %> - CWDECC 3S系統</title>

    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link href="~/Styles/site.css" rel="stylesheet" />
    
    <asp:PlaceHolder runat="server">
        <%: Scripts.Render("~/bundles/modernizr") %>
    </asp:PlaceHolder>
    
    <link href="~/favicon.ico" rel="shortcut icon" type="image/x-icon" />
</head>

<body>
    <form runat="server">
        <asp:ScriptManager runat="server">
            <Scripts>
                <asp:ScriptReference Name="MsAjaxBundle" />
                <asp:ScriptReference Name="jquery" />
                <asp:ScriptReference Name="WebForms.js" Assembly="System.Web" />
                <asp:ScriptReference Name="WebUIValidation.js" Assembly="System.Web" />
                <asp:ScriptReference Name="MenuStandards.js" Assembly="System.Web" />
                <asp:ScriptReference Name="GridView.js" Assembly="System.Web" />
                <asp:ScriptReference Name="DetailsView.js" Assembly="System.Web" />
                <asp:ScriptReference Name="TreeView.js" Assembly="System.Web" />
                <asp:ScriptReference Name="WebParts.js" Assembly="System.Web" />
                <asp:ScriptReference Name="Focus.js" Assembly="System.Web" />
                <asp:ScriptReference Name="WebFormsBundle" />
            </Scripts>
        </asp:ScriptManager>

        <!-- 頁首導航欄 - HTTrack 企業風格 -->
        <nav class="navbar navbar-expand-lg navbar-dark fixed-top shadow" style="background: linear-gradient(135deg, #0850B2 0%, #284E98 100%);">
            <div class="container-fluid">
                <!-- 品牌標誌 - 企業風格 -->
                <a class="navbar-brand d-flex align-items-center enterprise-header" runat="server" href="~/" style="color: white; font-weight: bold; font-family: Verdana, Arial, Tahoma, sans-serif;">
                    <i class="fas fa-home me-2"></i>
                    <strong>CWDECC 3S 社會服務系統</strong>
                </a>

                <!-- 移動端選單按鈕 -->
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" 
                        aria-controls="navbarNav" aria-expanded="false" aria-label="切換導航">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- 導航選單 -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <!-- 左側主導航 - 企業風格 -->
                    <ul class="navbar-nav mr-auto navigation-enterprise" id="mainNavigation" runat="server" style="background: none;">
                        <!-- 動態生成導航選單 -->
                    </ul>

                    <!-- 右側用戶資訊 -->
                    <ul class="navbar-nav ml-auto">
                        <!-- 用戶資訊顯示 - 企業風格 -->
                        <li class="nav-item dropdown" id="userInfoDropdown" runat="server" visible="false">
                            <a class="nav-link dropdown-toggle small-text" href="#" id="userDropdown" role="button" 
                               data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" 
                               style="color: rgba(255, 255, 255, 0.9); font-family: Verdana, Arial, Tahoma, sans-serif;">
                                <i class="fas fa-user-circle me-1"></i>
                                <span id="userDisplayName" runat="server"></span>
                                <small class="ms-1">(<span id="userRole" runat="server"></span>)</small>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
                                <h6 class="dropdown-header">
                                    <i class="fas fa-info-circle me-1"></i>用戶資訊
                                </h6>
                                <div class="dropdown-item-text">
                                    <small class="text-muted">登入帳號：<span id="username" runat="server"></span></small>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="~/Account/Profile.aspx">
                                    <i class="fas fa-user-edit me-2"></i>個人資料
                                </a>
                                <a class="dropdown-item" href="~/Account/ChangePassword.aspx">
                                    <i class="fas fa-key me-2"></i>修改密碼
                                </a>
                                <div class="dropdown-divider"></div>
                                <asp:LinkButton ID="btnLogout" runat="server" CssClass="dropdown-item text-danger"
                                    OnClick="btnLogout_Click" OnClientClick="return confirm('確定要登出嗎？');">
                                    <i class="fas fa-sign-out-alt me-2"></i>登出
                                </asp:LinkButton>
                            </div>
                        </li>

                        <!-- 登入按鈕（未登入時顯示） - 企業風格 -->
                        <li class="nav-item" id="loginButton" runat="server" visible="true">
                            <a class="nav-link btn-enterprise" href="~/Login.aspx" 
                               style="background-color: rgba(255, 255, 255, 0.2); color: white; border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 3px; padding: 4px 12px; font-family: Verdana, Arial, Tahoma, sans-serif;">
                                <i class="fas fa-sign-in-alt me-1"></i>登入系統
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- 主要內容區域 - HTTrack 企業風格 -->
        <main class="main-content" style="background-color: white; border-radius: 4px; margin-top: 1rem;">
            <div class="container-fluid mt-4 pt-4">
                <!-- 頁面標題和麵包屑 - 企業風格 -->
                <div class="row mb-4" id="pageHeader" runat="server" visible="false">
                    <div class="col-12">
                        <nav aria-label="breadcrumb" class="navigation-enterprise">
                            <ol class="breadcrumb" id="breadcrumb" runat="server">
                                <!-- 動態生成麵包屑 -->
                            </ol>
                        </nav>
                    </div>
                </div>

                <!-- 內容佔位符 -->
                <asp:ContentPlaceHolder ID="MainContent" runat="server">
                </asp:ContentPlaceHolder>
            </div>
        </main>

        <!-- 頁尾 - HTTrack 企業風格 -->
        <footer class="enterprise-footer mt-5 py-4">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h6 style="color: #ffffff; font-family: Verdana, Geneva, Tahoma, sans-serif;">
                            <i class="fas fa-building me-2"></i>CWDECC 3S 社會服務系統
                        </h6>
                        <p class="small mb-0" style="color: #ffffff; font-family: Verdana, Geneva, Tahoma, sans-serif;">
                            智慧化會員及活動管理系統<br/>
                            © <%: DateTime.Now.Year %> 香港基督教培道聯愛會版權所有
                        </p>
                    </div>
                    <div class="col-md-6 text-md-right">
                        <small style="color: #ffffff; font-family: Verdana, Geneva, Tahoma, sans-serif;">
                            <i class="fas fa-clock me-1"></i>
                            系統時間：<%: DateTime.Now.ToString("yyyy-MM-dd HH:mm") %>
                        </small>
                        <br/>
                        <small style="color: #ffffff; font-family: Verdana, Geneva, Tahoma, sans-serif;">
                            <i class="fas fa-code me-1"></i>
                            系統版本：v1.0.0 | ASP.NET WebForms + Firebase
                        </small>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Bootstrap 4 JavaScript -->
        <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
        
        <!-- 自定義 JavaScript -->
        <script type="text/javascript">
            $(document).ready(function() {
                // 初始化工具提示
                $('[data-toggle="tooltip"]').tooltip();
                
                // 初始化彈出框
                $('[data-toggle="popover"]').popover();
                
                // 活動導航選單項目
                $('.navbar-nav .nav-link').click(function() {
                    $('.navbar-nav .nav-link').removeClass('active');
                    $(this).addClass('active');
                });
                
                // 移動端選單自動關閉
                $('.navbar-nav .nav-link').on('click', function() {
                    if ($(window).width() < 768) {
                        $('.navbar-collapse').collapse('hide');
                    }
                });
                
                // 下拉選單 hover 效果（桌面版）
                if ($(window).width() > 768) {
                    $('.dropdown').hover(function() {
                        $(this).addClass('show');
                        $(this).find('.dropdown-menu').addClass('show');
                    }, function() {
                        $(this).removeClass('show');
                        $(this).find('.dropdown-menu').removeClass('show');
                    });
                }
            });
            
            // 響應式導航欄調整
            $(window).resize(function() {
                if ($(window).width() > 768) {
                    $('.navbar-collapse').removeClass('show');
                }
            });
        </script>
    </form>
</body>
</html>