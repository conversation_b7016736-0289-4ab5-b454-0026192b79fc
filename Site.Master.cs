using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Security.Principal;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Text;
using CWDECC_3S.Services;

namespace CWDECC_3S
{
    public partial class SiteMaster : MasterPage
    {
        private const string AntiXsrfTokenKey = "__AntiXsrfToken";
        private const string AntiXsrfUserNameKey = "__AntiXsrfUserName";
        private string _antiXsrfTokenValue;

        protected void Page_Init(object sender, EventArgs e)
        {
            // The code below helps to protect against XSRF attacks
            var requestCookie = Request.Cookies[AntiXsrfTokenKey];
            Guid requestCookieGuidValue;
            if (requestCookie != null && Guid.TryParse(requestCookie.Value, out requestCookieGuidValue))
            {
                // Use the Anti-XSRF token from the cookie
                _antiXsrfTokenValue = requestCookie.Value;
                Page.ViewStateUserKey = _antiXsrfTokenValue;
            }
            else
            {
                // Generate a new Anti-XSRF token and save to the cookie
                _antiXsrfTokenValue = Guid.NewGuid().ToString("N");
                Page.ViewStateUserKey = _antiXsrfTokenValue;

                var responseCookie = new HttpCookie(AntiXsrfTokenKey)
                {
                    HttpOnly = true,
                    Value = _antiXsrfTokenValue
                };
                if (FormsAuthentication.RequireSSL && Request.IsSecureConnection)
                {
                    responseCookie.Secure = true;
                }
                Response.Cookies.Set(responseCookie);
            }

            Page.PreLoad += master_Page_PreLoad;
        }

        protected void master_Page_PreLoad(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Set Anti-XSRF token
                ViewState[AntiXsrfTokenKey] = Page.ViewStateUserKey;
                ViewState[AntiXsrfUserNameKey] = Context.User.Identity.Name ?? String.Empty;
            }
            else
            {
                // Validate the Anti-XSRF token
                if ((string)ViewState[AntiXsrfTokenKey] != _antiXsrfTokenValue
                    || (string)ViewState[AntiXsrfUserNameKey] != (Context.User.Identity.Name ?? String.Empty))
                {
                    throw new InvalidOperationException("Validation of Anti-XSRF token failed.");
                }
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // 初始化導航選單和用戶資訊
                InitializeNavigation();
                InitializeUserInfo();
                InitializeBreadcrumb();
            }
        }

        #region 導航選單初始化

        /// <summary>
        /// 初始化導航選單
        /// </summary>
        private void InitializeNavigation()
        {
            try
            {
                // 取得用戶的導航選單
                var navigationItems = PermissionService.GetUserNavigation();
                
                // 清空現有選單
                mainNavigation.Controls.Clear();
                
                // 動態生成導航選單
                foreach (var item in navigationItems)
                {
                    var listItem = CreateNavigationItem(item);
                    mainNavigation.Controls.Add(listItem);
                }
                
                LogNavigationEvent($"導航選單已初始化，共 {navigationItems.Count} 個項目");
            }
            catch (Exception ex)
            {
                LogNavigationEvent($"導航選單初始化失敗: {ex.Message}");
                
                // 提供基本導航選單作為備用
                CreateFallbackNavigation();
            }
        }

        /// <summary>
        /// 建立導航選單項目
        /// </summary>
        /// <param name="item">導航項目</param>
        /// <returns>HTML 列表項目</returns>
        private HtmlGenericControl CreateNavigationItem(PermissionService.NavigationItem item)
        {
            var listItem = new HtmlGenericControl("li");
            listItem.Attributes["class"] = "nav-item";
            
            if (item.IsDropdown)
            {
                // 下拉選單
                listItem.Attributes["class"] += " dropdown";
                
                var mainLink = new HtmlGenericControl("a");
                mainLink.Attributes["class"] = "nav-link dropdown-toggle";
                mainLink.Attributes["href"] = "#";
                mainLink.Attributes["id"] = $"navbarDropdown_{item.Text}";
                mainLink.Attributes["role"] = "button";
                mainLink.Attributes["data-toggle"] = "dropdown";
                mainLink.Attributes["aria-haspopup"] = "true";
                mainLink.Attributes["aria-expanded"] = "false";
                
                var icon = new HtmlGenericControl("i");
                icon.Attributes["class"] = item.Icon + " me-2";
                mainLink.Controls.Add(icon);
                
                var textNode = new LiteralControl(item.Text);
                mainLink.Controls.Add(textNode);
                
                listItem.Controls.Add(mainLink);
                
                // 下拉選單內容
                var dropdownMenu = new HtmlGenericControl("div");
                dropdownMenu.Attributes["class"] = "dropdown-menu";
                dropdownMenu.Attributes["aria-labelledby"] = $"navbarDropdown_{item.Text}";
                
                foreach (var child in item.Children)
                {
                    var childLink = new HtmlGenericControl("a");
                    childLink.Attributes["class"] = "dropdown-item";
                    childLink.Attributes["href"] = ResolveUrl(child.Url);
                    
                    var childIcon = new HtmlGenericControl("i");
                    childIcon.Attributes["class"] = "fas fa-chevron-right me-2";
                    childLink.Controls.Add(childIcon);
                    
                    var childText = new LiteralControl(child.Text);
                    childLink.Controls.Add(childText);
                    
                    dropdownMenu.Controls.Add(childLink);
                }
                
                listItem.Controls.Add(dropdownMenu);
            }
            else
            {
                // 一般連結
                var link = new HtmlGenericControl("a");
                link.Attributes["class"] = "nav-link";
                link.Attributes["href"] = ResolveUrl(item.Url);
                
                var icon = new HtmlGenericControl("i");
                icon.Attributes["class"] = item.Icon + " me-2";
                link.Controls.Add(icon);
                
                var textNode = new LiteralControl(item.Text);
                link.Controls.Add(textNode);
                
                listItem.Controls.Add(link);
            }
            
            return listItem;
        }

        /// <summary>
        /// 建立備用導航選單
        /// </summary>
        private void CreateFallbackNavigation()
        {
            mainNavigation.Controls.Clear();
            
            // 基本選單項目
            var homeItem = new HtmlGenericControl("li");
            homeItem.Attributes["class"] = "nav-item";
            
            var homeLink = new HtmlGenericControl("a");
            homeLink.Attributes["class"] = "nav-link";
            homeLink.Attributes["href"] = ResolveUrl("~/");
            homeLink.InnerHtml = "<i class='fas fa-home me-2'></i>首頁";
            
            homeItem.Controls.Add(homeLink);
            mainNavigation.Controls.Add(homeItem);
        }

        #endregion

        #region 用戶資訊初始化

        /// <summary>
        /// 初始化用戶資訊顯示
        /// </summary>
        private void InitializeUserInfo()
        {
            try
            {
                var userInfo = PermissionService.GetUserDisplayInfo();
                
                if (userInfo.IsAuthenticated)
                {
                    // 顯示用戶資訊
                    userInfoDropdown.Visible = true;
                    loginButton.Visible = false;
                    
                    // 設定用戶顯示資訊
                    userDisplayName.InnerText = userInfo.DisplayName;
                    userRole.InnerText = userInfo.RoleDisplayName;
                    username.InnerText = userInfo.Username;
                    
                    LogNavigationEvent($"用戶已登入: {userInfo.Username} ({userInfo.Role})");
                }
                else
                {
                    // 顯示登入按鈕
                    userInfoDropdown.Visible = false;
                    loginButton.Visible = true;
                    
                    LogNavigationEvent("用戶未登入，顯示登入按鈕");
                }
            }
            catch (Exception ex)
            {
                LogNavigationEvent($"用戶資訊初始化失敗: {ex.Message}");
                
                // 預設顯示登入按鈕
                userInfoDropdown.Visible = false;
                loginButton.Visible = true;
            }
        }

        #endregion

        #region 麵包屑導航

        /// <summary>
        /// 初始化麵包屑導航
        /// </summary>
        private void InitializeBreadcrumb()
        {
            try
            {
                var currentPath = Request.Path;
                var breadcrumbItems = GenerateBreadcrumbItems(currentPath);
                
                if (breadcrumbItems.Count > 1) // 只有在有多個項目時才顯示麵包屑
                {
                    pageHeader.Visible = true;
                    breadcrumb.Controls.Clear();
                    
                    for (int i = 0; i < breadcrumbItems.Count; i++)
                    {
                        var item = breadcrumbItems[i];
                        var breadcrumbItem = new HtmlGenericControl("li");
                        breadcrumbItem.Attributes["class"] = "breadcrumb-item";
                        
                        if (i == breadcrumbItems.Count - 1)
                        {
                            // 最後一個項目（當前頁面）
                            breadcrumbItem.Attributes["class"] += " active";
                            breadcrumbItem.Attributes["aria-current"] = "page";
                            breadcrumbItem.InnerText = item.Text;
                        }
                        else
                        {
                            // 可點擊的項目
                            var link = new HtmlGenericControl("a");
                            link.Attributes["href"] = ResolveUrl(item.Url);
                            link.InnerText = item.Text;
                            breadcrumbItem.Controls.Add(link);
                        }
                        
                        breadcrumb.Controls.Add(breadcrumbItem);
                    }
                }
            }
            catch (Exception ex)
            {
                LogNavigationEvent($"麵包屑導航初始化失敗: {ex.Message}");
                pageHeader.Visible = false;
            }
        }

        /// <summary>
        /// 根據路徑生成麵包屑項目
        /// </summary>
        /// <param name="currentPath">當前路徑</param>
        /// <returns>麵包屑項目列表</returns>
        private List<BreadcrumbItem> GenerateBreadcrumbItems(string currentPath)
        {
            var items = new List<BreadcrumbItem>
            {
                new BreadcrumbItem { Text = "首頁", Url = "~/" }
            };
            
            // 根據路徑判斷麵包屑
            if (currentPath.Contains("/Members/"))
            {
                items.Add(new BreadcrumbItem { Text = "會員管理", Url = "~/Members/" });
            }
            else if (currentPath.Contains("/Activities/"))
            {
                items.Add(new BreadcrumbItem { Text = "活動管理", Url = "~/Activities/" });
            }
            else if (currentPath.Contains("/Admin/"))
            {
                items.Add(new BreadcrumbItem { Text = "系統管理", Url = "~/Admin/" });
                
                if (currentPath.Contains("FirebaseTest"))
                {
                    items.Add(new BreadcrumbItem { Text = "Firebase 測試", Url = "" });
                }
                else if (currentPath.Contains("SecurityTest"))
                {
                    items.Add(new BreadcrumbItem { Text = "安全測試", Url = "" });
                }
            }
            else if (currentPath.Contains("/Account/"))
            {
                items.Add(new BreadcrumbItem { Text = "帳戶管理", Url = "~/Account/" });
            }
            
            return items;
        }

        /// <summary>
        /// 麵包屑項目類別
        /// </summary>
        private class BreadcrumbItem
        {
            public string Text { get; set; }
            public string Url { get; set; }
        }

        #endregion

        #region 事件處理

        /// <summary>
        /// 登出按鈕點擊事件
        /// </summary>
        protected void btnLogout_Click(object sender, EventArgs e)
        {
            try
            {
                // 清除 Session
                Session.Clear();
                Session.Abandon();
                
                // 清除用戶角色
                PermissionService.ClearUserRole();
                
                // 清除 Forms Authentication
                FormsAuthentication.SignOut();
                
                // 清除所有 Cookies
                foreach (string cookieName in Request.Cookies.AllKeys)
                {
                    var cookie = new HttpCookie(cookieName)
                    {
                        Expires = DateTime.Now.AddDays(-1),
                        Value = ""
                    };
                    Response.Cookies.Add(cookie);
                }
                
                LogNavigationEvent("用戶已成功登出");
                
                // 重定向到登入頁面
                Response.Redirect("~/Account/Login.aspx", true);
            }
            catch (Exception ex)
            {
                LogNavigationEvent($"登出失敗: {ex.Message}");
                
                // 即使發生錯誤也要重定向到登入頁面
                Response.Redirect("~/Account/Login.aspx", true);
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 記錄導航事件
        /// </summary>
        /// <param name="message">事件訊息</param>
        private void LogNavigationEvent(string message)
        {
            try
            {
                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [NAVIGATION] {message}";
                var logPath = Server.MapPath("~/App_Data/Logs/navigation.log");
                
                System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(logPath));
                System.IO.File.AppendAllText(logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // 記錄失敗不應影響主要功能
            }
        }

        #endregion
    }
}