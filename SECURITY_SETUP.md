# 🔒 CWDECC-3S 安全加密系統設定指南

## 概述
本指南將協助您正確設定 AES-256 加密系統，確保會員敏感資料的安全性符合企業級標準。

## 🔑 密鑰管理設定

### 1. 生成主密鑰

#### 方法一：使用安全測試頁面 (推薦)
1. 啟動專案並訪問 `/Admin/SecurityTest.aspx`
2. 點擊「產生新主密鑰」按鈕
3. 複製生成的 Base64 編碼密鑰

#### 方法二：使用 PowerShell (Windows)
```powershell
# 生成 256 位 (32 字節) 隨機密鑰
$bytes = New-Object byte[] 32
[Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
[Convert]::ToBase64String($bytes)
```

#### 方法三：使用 C# 程式碼
```csharp
using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
{
    byte[] keyBytes = new byte[32];
    rng.GetBytes(keyBytes);
    string masterKey = Convert.ToBase64String(keyBytes);
    Console.WriteLine(masterKey);
}
```

### 2. 設定主密鑰

#### 生產環境 (推薦)
設定環境變數：
```bash
# Windows
setx CWDECC_MASTER_KEY "your-generated-base64-key-here"

# Linux/macOS
export CWDECC_MASTER_KEY="your-generated-base64-key-here"

# Docker
docker run -e CWDECC_MASTER_KEY="your-generated-base64-key-here" your-app
```

#### 開發環境 (僅限測試)
更新 `Web.config`：
```xml
<add key="EncryptionMasterKey" value="your-generated-base64-key-here" />
```

## 🛡️ 安全要求檢查清單

### ✅ 密鑰安全性
- [ ] 主密鑰長度至少 256 位 (32 字節)
- [ ] 使用密碼學安全的隨機數生成器
- [ ] 密鑰儲存在安全位置 (環境變數或密鑰庫)
- [ ] 定期輪換密鑰 (建議每年)
- [ ] 密鑰訪問權限受到限制

### ✅ 系統配置
- [ ] 啟用 HTTPS (SSL/TLS)
- [ ] 設定適當的安全標頭
- [ ] 配置會話超時
- [ ] 啟用安全日誌記錄

### ✅ 資料保護
- [ ] 敏感欄位自動加密 (姓名、身份證、電話、地址)
- [ ] 資料傳輸加密
- [ ] 資料庫連線加密
- [ ] 適當的資料遮罩

## 🧪 測試驗收

訪問 `/Admin/SecurityTest.aspx` 並執行以下測試：

### 1. 基本功能測試
- [ ] 中文字符加密/解密正常
- [ ] 英文字符加密/解密正常  
- [ ] 數字加密/解密正常
- [ ] 混合字符集測試通過

### 2. Firebase 整合測試
- [ ] 加密資料成功寫入 Firestore
- [ ] 從 Firestore 讀取並解密成功
- [ ] 資料完整性驗證通過

### 3. 安全性測試
- [ ] 密鑰版本控制正常
- [ ] 密鑰輪換隔離機制有效
- [ ] 舊密鑰無法解密新資料

### 4. 性能測試
- [ ] 加密性能滿足需求 (>100 筆/秒)
- [ ] 解密性能滿足需求 (>100 筆/秒)
- [ ] 記憶體使用合理

## 📊 安全架構

```
┌─────────────────────────────────────────────────────────────┐
│                    應用程式層                                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  SecureMember   │    │ SecurityTest    │                │
│  │  (自動加密模型)  │    │  (測試介面)     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 SecureDataProtector                         │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   AES-256-CBC   │  │   PBKDF2-SHA256  │                  │
│  │   加密/解密     │  │   密鑰衍生       │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     密鑰管理                                │
│  環境變數: CWDECC_MASTER_KEY                                │
│  版本控制 + 鹽值管理 + 安全隔離                             │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Firebase Firestore                          │
│            (加密後的敏感資料儲存)                           │
└─────────────────────────────────────────────────────────────┘
```

## 🚨 安全注意事項

### 密鑰管理
- **絕對不要** 將主密鑰硬編碼在程式碼中
- **絕對不要** 將主密鑰提交到版本控制系統
- **絕對不要** 在日誌中記錄主密鑰或明文敏感資料
- **務必** 定期備份密鑰並安全儲存
- **務必** 建立密鑰輪換計劃

### 存取控制
- 限制對加密系統的存取權限
- 實施最小權限原則
- 監控和記錄所有敏感操作
- 定期審查存取權限

### 監控和稽核
- 啟用安全事件日誌記錄
- 監控異常的加密/解密活動
- 定期進行安全評估
- 建立事件回應程序

## 📋 部署檢查清單

### 開發環境
- [ ] 設定測試用主密鑰
- [ ] 啟用詳細日誌記錄
- [ ] 執行完整測試套件

### 測試環境  
- [ ] 使用生產級密鑰
- [ ] 測試密鑰輪換程序
- [ ] 驗證備份和恢復

### 生產環境
- [ ] 設定環境變數密鑰
- [ ] 啟用所有安全標頭
- [ ] 配置監控和警報
- [ ] 建立運營程序文件

## 🆘 故障排除

### 常見問題

**問題**: 加密失敗 "主密鑰未設定"
**解決**: 確認環境變數 `CWDECC_MASTER_KEY` 已正確設定

**問題**: 解密失敗 "無效的加密數據格式"  
**解決**: 檢查密文是否完整，確認沒有被截斷

**問題**: 性能問題
**解決**: 檢查 PBKDF2 迭代次數，考慮調整到適當水平

**問題**: Firebase 整合失敗
**解決**: 確認 Firebase 專案設定正確，網路連線正常

### 支援聯絡
- 技術支援: [<EMAIL>]
- 安全問題: [<EMAIL>]
- 緊急事件: [<EMAIL>]

---

**重要提醒**: 此加密系統處理敏感個人資料，請務必遵循相關法規和組織政策。