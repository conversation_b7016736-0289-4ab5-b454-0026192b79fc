<%@ Page Title="首頁" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="CWDECC_3S.Default" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">

    <div class="jumbotron">
        <h1>CWDECC 3S系統</h1>
        <p class="lead">歡迎使用CWDECC 3S (Social Service System) 社會服務系統</p>
        <p><a href="Members/" class="btn btn-primary btn-lg">開始使用 &raquo;</a></p>
    </div>

    <div class="row">
        <div class="col-md-4">
            <h2>會員管理</h2>
            <p>
                管理會員資料、會員註冊、會員查詢等功能。
            </p>
            <p>
                <a class="btn btn-default" href="Members/">了解更多 &raquo;</a>
            </p>
        </div>
        <div class="col-md-4">
            <h2>活動管理</h2>
            <p>
                管理社會服務活動、活動報名、活動查詢等功能。
            </p>
            <p>
                <a class="btn btn-default" href="Activities/">了解更多 &raquo;</a>
            </p>
        </div>
        <div class="col-md-4">
            <h2>系統管理</h2>
            <p>
                系統設定、用戶權限管理、系統監控等功能。
            </p>
            <p>
                <a class="btn btn-default" href="Admin/">了解更多 &raquo;</a>
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h3>系統狀態</h3>
            <div class="alert alert-info">
                <asp:Label ID="lblSystemStatus" runat="server" Text="系統正常運行中..."></asp:Label>
            </div>
            <div class="well">
                <h4>Firebase 連線測試</h4>
                <asp:Button ID="btnTestFirebase" runat="server" Text="測試 Firebase 連線" CssClass="btn btn-info" OnClick="btnTestFirebase_Click" />
                <asp:Label ID="lblFirebaseStatus" runat="server" CssClass="label label-info"></asp:Label>
            </div>
        </div>
    </div>

</asp:Content>