<%@ Page Title="用戶角色管理" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="UserRoles.aspx.cs" Inherits="CWDECC_3S.Settings.UserRoles" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-users-cog text-primary me-2"></i>用戶角色管理
                </h2>
                <p class="text-muted">管理系統角色和用戶角色分配</p>
            </div>
            <div>
                <asp:Button ID="btnCreateRole" runat="server" Text="新增角色" CssClass="btn btn-success"
                    OnClick="btnCreateRole_Click" />
                <asp:Button ID="btnRefresh" runat="server" Text="重新整理" CssClass="btn btn-outline-primary"
                    OnClick="btnRefresh_Click" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <!-- 角色統計卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">總角色數</h5>
                                <h3><asp:Label ID="lblTotalRoles" runat="server" Text="0"></asp:Label></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-tag fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">啟用角色</h5>
                                <h3><asp:Label ID="lblActiveRoles" runat="server" Text="0"></asp:Label></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">系統角色</h5>
                                <h3><asp:Label ID="lblSystemRoles" runat="server" Text="0"></asp:Label></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-shield-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">自訂角色</h5>
                                <h3><asp:Label ID="lblCustomRoles" runat="server" Text="0"></asp:Label></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-plus fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 角色列表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>角色列表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvRoles" runat="server" CssClass="table table-striped table-hover"
                                AutoGenerateColumns="False" DataKeyNames="Id" EmptyDataText="沒有找到角色資料"
                                OnRowCommand="gvRoles_RowCommand" OnRowDataBound="gvRoles_RowDataBound">
                                <Columns>
                                    <asp:BoundField DataField="Name" HeaderText="角色名稱" SortExpression="Name">
                                        <HeaderStyle Width="25%" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="Description" HeaderText="描述" SortExpression="Description">
                                        <HeaderStyle Width="35%" />
                                    </asp:BoundField>
                                    <asp:TemplateField HeaderText="狀態" SortExpression="IsActive">
                                        <ItemTemplate>
                                            <span class='<%# Convert.ToBoolean(Eval("IsActive")) ? "badge bg-success" : "badge bg-secondary" %>'>
                                                <%# Convert.ToBoolean(Eval("IsActive")) ? "啟用" : "停用" %>
                                            </span>
                                        </ItemTemplate>
                                        <HeaderStyle Width="15%" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="用戶數">
                                        <ItemTemplate>
                                            <asp:Label ID="lblUserCount" runat="server" Text="0" CssClass="badge bg-info"></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle Width="10%" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="操作">
                                        <ItemTemplate>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <asp:LinkButton ID="btnViewUsers" runat="server" 
                                                    CommandName="ViewUsers" CommandArgument='<%# Eval("Id") %>'
                                                    CssClass="btn btn-outline-info btn-sm" 
                                                    ToolTip="查看用戶">
                                                    <i class="fas fa-eye"></i>
                                                </asp:LinkButton>
                                                <asp:LinkButton ID="btnEditRole" runat="server" 
                                                    CommandName="EditRole" CommandArgument='<%# Eval("Id") %>'
                                                    CssClass="btn btn-outline-warning btn-sm" 
                                                    ToolTip="編輯">
                                                    <i class="fas fa-edit"></i>
                                                </asp:LinkButton>
                                                <asp:LinkButton ID="btnDeleteRole" runat="server" 
                                                    CommandName="DeleteRole" CommandArgument='<%# Eval("Id") %>'
                                                    CssClass="btn btn-outline-danger btn-sm" 
                                                    ToolTip="刪除"
                                                    OnClientClick="return confirm('確定要刪除此角色嗎？');">
                                                    <i class="fas fa-trash"></i>
                                                </asp:LinkButton>
                                            </div>
                                        </ItemTemplate>
                                        <HeaderStyle Width="15%" />
                                    </asp:TemplateField>
                                </Columns>
                                <HeaderStyle CssClass="table-dark" />
                                <EmptyDataRowStyle CssClass="text-center text-muted" />
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色詳情/用戶列表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>
                            <asp:Label ID="lblSelectedRoleTitle" runat="server" Text="選擇一個角色查看用戶"></asp:Label>
                        </h5>
                        <asp:Button ID="btnAssignUser" runat="server" Text="分配用戶" CssClass="btn btn-primary btn-sm"
                            OnClick="btnAssignUser_Click" Visible="false" />
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlRoleUsers" runat="server" Visible="false">
                            <div class="table-responsive">
                                <asp:GridView ID="gvRoleUsers" runat="server" CssClass="table table-striped table-hover"
                                    AutoGenerateColumns="False" DataKeyNames="Id" EmptyDataText="此角色沒有分配給任何用戶"
                                    OnRowCommand="gvRoleUsers_RowCommand">
                                    <Columns>
                                        <asp:BoundField DataField="UserName" HeaderText="用戶名" SortExpression="UserName">
                                            <HeaderStyle Width="25%" />
                                        </asp:BoundField>
                                        <asp:BoundField DataField="DisplayName" HeaderText="顯示名稱" SortExpression="DisplayName">
                                            <HeaderStyle Width="25%" />
                                        </asp:BoundField>
                                        <asp:BoundField DataField="Email" HeaderText="電子郵件" SortExpression="Email">
                                            <HeaderStyle Width="30%" />
                                        </asp:BoundField>
                                        <asp:TemplateField HeaderText="狀態">
                                            <ItemTemplate>
                                                <span class='<%# Convert.ToBoolean(Eval("IsEnabled")) ? "badge bg-success" : "badge bg-danger" %>'>
                                                    <%# Convert.ToBoolean(Eval("IsEnabled")) ? "啟用" : "停用" %>
                                                </span>
                                            </ItemTemplate>
                                            <HeaderStyle Width="10%" />
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="操作">
                                            <ItemTemplate>
                                                <asp:LinkButton ID="btnRemoveUser" runat="server" 
                                                    CommandName="RemoveUser" CommandArgument='<%# Eval("Id") %>'
                                                    CssClass="btn btn-outline-danger btn-sm" 
                                                    ToolTip="移除角色"
                                                    OnClientClick="return confirm('確定要移除此用戶的角色嗎？');">
                                                    <i class="fas fa-user-minus"></i>
                                                </asp:LinkButton>
                                            </ItemTemplate>
                                            <HeaderStyle Width="10%" />
                                        </asp:TemplateField>
                                    </Columns>
                                    <HeaderStyle CssClass="table-dark" />
                                    <EmptyDataRowStyle CssClass="text-center text-muted" />
                                </asp:GridView>
                            </div>
                        </asp:Panel>

                        <asp:Panel ID="pnlNoRoleSelected" runat="server" Visible="true">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <p>請從左側角色列表中選擇一個角色來查看其用戶</p>
                            </div>
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/編輯角色模態視窗 -->
    <div class="modal fade" id="roleModal" tabindex="-1" aria-labelledby="roleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="roleModalLabel">
                        <asp:Label ID="lblModalTitle" runat="server" Text="新增角色"></asp:Label>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hdnRoleId" runat="server" />
                    
                    <div class="mb-3">
                        <label for="<%= txtRoleName.ClientID %>" class="form-label">角色名稱 <span class="text-danger">*</span></label>
                        <asp:TextBox ID="txtRoleName" runat="server" CssClass="form-control" 
                            placeholder="請輸入角色名稱" MaxLength="256"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfvRoleName" runat="server" 
                            ControlToValidate="txtRoleName" ErrorMessage="角色名稱不能為空"
                            CssClass="text-danger small" Display="Dynamic" ValidationGroup="RoleModal"></asp:RequiredFieldValidator>
                    </div>

                    <div class="mb-3">
                        <label for="<%= txtDescription.ClientID %>" class="form-label">描述</label>
                        <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" 
                            TextMode="MultiLine" Rows="3" MaxLength="1000"
                            placeholder="請輸入角色描述"></asp:TextBox>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <asp:CheckBox ID="chkIsActive" runat="server" CssClass="form-check-input" Checked="true" />
                            <label class="form-check-label" for="<%= chkIsActive.ClientID %>">
                                啟用此角色
                            </label>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提醒：</strong>建立角色後，您可以在權限管理頁面設定此角色的具體權限。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnSaveRole" runat="server" Text="儲存" CssClass="btn btn-primary"
                        OnClick="btnSaveRole_Click" ValidationGroup="RoleModal" />
                </div>
            </div>
        </div>
    </div>

    <!-- 分配用戶模態視窗 -->
    <div class="modal fade" id="assignUserModal" tabindex="-1" aria-labelledby="assignUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignUserModalLabel">
                        分配用戶到角色：<asp:Label ID="lblAssignRoleName" runat="server" Text="" CssClass="text-primary"></asp:Label>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 可用用戶 -->
                        <div class="col-md-6">
                            <h6>可用用戶</h6>
                            <div class="border rounded p-2" style="height: 300px; overflow-y: auto;">
                                <asp:CheckBoxList ID="cblAvailableUsers" runat="server" CssClass="list-unstyled">
                                </asp:CheckBoxList>
                            </div>
                        </div>

                        <!-- 已分配用戶 -->
                        <div class="col-md-6">
                            <h6>已分配用戶</h6>
                            <div class="border rounded p-2" style="height: 300px; overflow-y: auto;">
                                <asp:Literal ID="ltlCurrentUsers" runat="server"></asp:Literal>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="form-check">
                            <asp:CheckBox ID="chkNotifyUsers" runat="server" CssClass="form-check-input" Checked="true" />
                            <label class="form-check-label" for="<%= chkNotifyUsers.ClientID %>">
                                通知受影響的用戶
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnSaveUserAssignment" runat="server" Text="儲存分配" CssClass="btn btn-primary"
                        OnClick="btnSaveUserAssignment_Click" />
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            // 初始化 DataTables
            if ($('#<%= gvRoles.ClientID %>').length) {
                $('#<%= gvRoles.ClientID %>').DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese-traditional.json"
                    },
                    "pageLength": 10,
                    "order": [[0, "asc"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [4] } // 操作欄不可排序
                    ]
                });
            }

            // 處理模態視窗顯示
            window.showRoleModal = function() {
                $('#roleModal').modal('show');
            };

            window.showAssignUserModal = function() {
                $('#assignUserModal').modal('show');
            };

            // 表格行點擊事件
            $('#<%= gvRoles.ClientID %> tbody').on('click', 'tr', function() {
                // 移除其他行的選中狀態
                $('#<%= gvRoles.ClientID %> tbody tr').removeClass('table-primary');
                
                // 添加選中狀態
                $(this).addClass('table-primary');
            });
        });

        // 顯示載入指示器
        function showLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }

        // 成功訊息
        function showSuccess(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: '成功',
                    text: message,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        }

        // 錯誤訊息
        function showError(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: '錯誤',
                    text: message
                });
            }
        }
    </script>
</asp:Content>