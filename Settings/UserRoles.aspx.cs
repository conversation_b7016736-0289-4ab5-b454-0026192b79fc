using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Models;
using CWDECC_3S.Services;
using Microsoft.AspNet.Identity;

namespace CWDECC_3S.Settings
{
    /// <summary>
    /// 用戶角色管理頁面 - 管理系統角色和用戶角色分配
    /// </summary>
    public partial class UserRoles : System.Web.UI.Page
    {
        private RoleManagementService _roleService;
        private PermissionService _permissionService;
        private string _currentUserId;
        private string _selectedRoleId;

        protected void Page_Init(object sender, EventArgs e)
        {
            _roleService = new RoleManagementService();
            _permissionService = new PermissionService();
            _currentUserId = User.Identity.GetUserId();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限
                if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Read"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    LoadRoleStatistics();
                    LoadRoles();
                }

                // 保持選中的角色
                if (ViewState["SelectedRoleId"] != null)
                {
                    _selectedRoleId = ViewState["SelectedRoleId"].ToString();
                    LoadRoleUsers(_selectedRoleId);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"頁面載入錯誤: {ex.Message}", "danger");
            }
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            // 註冊必要的 JavaScript 和 CSS
            RegisterScripts();
        }

        protected void Page_Unload(object sender, EventArgs e)
        {
            _roleService?.Dispose();
            _permissionService?.Dispose();
        }

        #region 載入資料

        /// <summary>
        /// 載入角色統計資訊
        /// </summary>
        private async void LoadRoleStatistics()
        {
            try
            {
                var statistics = await _roleService.GetRoleStatisticsAsync();
                
                lblTotalRoles.Text = statistics.TotalRoles.ToString();
                lblActiveRoles.Text = statistics.ActiveRoles.ToString();
                lblSystemRoles.Text = statistics.SystemRoles.ToString();
                lblCustomRoles.Text = statistics.CustomRoles.ToString();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入角色統計失敗: {ex.Message}", "warning");
            }
        }

        /// <summary>
        /// 載入角色列表
        /// </summary>
        private void LoadRoles()
        {
            try
            {
                var roles = _roleService.GetAllRoles(true); // 包含停用的角色
                gvRoles.DataSource = roles;
                gvRoles.DataBind();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入角色列表失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 載入指定角色的用戶
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        private async void LoadRoleUsers(string roleId)
        {
            try
            {
                if (string.IsNullOrEmpty(roleId))
                {
                    pnlRoleUsers.Visible = false;
                    pnlNoRoleSelected.Visible = true;
                    btnAssignUser.Visible = false;
                    return;
                }

                var role = _roleService.GetRoleById(roleId);
                if (role == null)
                {
                    ShowMessage("找不到指定的角色", "warning");
                    return;
                }

                lblSelectedRoleTitle.Text = $"角色「{role.Name}」的用戶";
                
                var users = await _roleService.GetUsersInRoleAsync(roleId);
                gvRoleUsers.DataSource = users;
                gvRoleUsers.DataBind();

                pnlRoleUsers.Visible = true;
                pnlNoRoleSelected.Visible = false;
                
                // 只有有編輯權限的用戶才能分配用戶
                btnAssignUser.Visible = _permissionService.HasPermission(_currentUserId, "UserManagement", "Update");

                ViewState["SelectedRoleId"] = roleId;
                _selectedRoleId = roleId;
            }
            catch (Exception ex)
            {
                ShowMessage($"載入角色用戶失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 載入可分配的用戶列表
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        private async void LoadAvailableUsers(string roleId)
        {
            try
            {
                if (string.IsNullOrEmpty(roleId))
                    return;

                var role = _roleService.GetRoleById(roleId);
                if (role == null)
                    return;

                lblAssignRoleName.Text = role.Name;

                // 取得所有用戶
                using (var context = new Data.ApplicationDbContext())
                {
                    var allUsers = context.Users.Where(u => u.IsEnabled).OrderBy(u => u.DisplayName ?? u.UserName).ToList();
                    var roleUsers = await _roleService.GetUsersInRoleAsync(roleId);
                    var roleUserIds = roleUsers.Select(u => u.Id).ToHashSet();

                    // 可分配的用戶（未擁有此角色的用戶）
                    var availableUsers = allUsers.Where(u => !roleUserIds.Contains(u.Id)).ToList();

                    cblAvailableUsers.DataSource = availableUsers;
                    cblAvailableUsers.DataTextField = "DisplayName";
                    cblAvailableUsers.DataValueField = "Id";
                    cblAvailableUsers.DataBind();

                    // 顯示已分配的用戶
                    if (roleUsers.Any())
                    {
                        var currentUsersHtml = "<ul class='list-unstyled'>";
                        foreach (var user in roleUsers)
                        {
                            currentUsersHtml += $"<li class='mb-1'><i class='fas fa-user me-2'></i>{user.DisplayName ?? user.UserName}</li>";
                        }
                        currentUsersHtml += "</ul>";
                        ltlCurrentUsers.Text = currentUsersHtml;
                    }
                    else
                    {
                        ltlCurrentUsers.Text = "<p class='text-muted'>尚未分配任何用戶</p>";
                    }
                }

                ViewState["AssignRoleId"] = roleId;
            }
            catch (Exception ex)
            {
                ShowMessage($"載入可分配用戶失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region GridView 事件

        protected void gvRoles_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                string roleId = e.CommandArgument.ToString();

                switch (e.CommandName)
                {
                    case "ViewUsers":
                        LoadRoleUsers(roleId);
                        break;

                    case "EditRole":
                        if (_permissionService.HasPermission(_currentUserId, "UserManagement", "Update"))
                        {
                            EditRole(roleId);
                        }
                        else
                        {
                            ShowMessage("您沒有編輯角色的權限", "warning");
                        }
                        break;

                    case "DeleteRole":
                        if (_permissionService.HasPermission(_currentUserId, "UserManagement", "Delete"))
                        {
                            DeleteRole(roleId);
                        }
                        else
                        {
                            ShowMessage("您沒有刪除角色的權限", "warning");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"操作失敗: {ex.Message}", "danger");
            }
        }

        protected void gvRoles_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                try
                {
                    var role = (ApplicationRole)e.Row.DataItem;
                    
                    // 載入用戶數量
                    var userCountLabel = e.Row.FindControl("lblUserCount") as Label;
                    if (userCountLabel != null)
                    {
                        var userCount = GetRoleUserCount(role.Id);
                        userCountLabel.Text = userCount.ToString();
                    }

                    // 系統角色的特殊處理
                    var systemRoles = new[] { "Administrator", "StaffMember", "Teacher", "Volunteer", "Member", "Guest" };
                    if (systemRoles.Contains(role.Name))
                    {
                        var deleteButton = e.Row.FindControl("btnDeleteRole") as LinkButton;
                        if (deleteButton != null)
                        {
                            deleteButton.Enabled = false;
                            deleteButton.ToolTip = "系統角色無法刪除";
                            deleteButton.CssClass += " disabled";
                        }
                    }

                    // 權限檢查
                    if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Update"))
                    {
                        var editButton = e.Row.FindControl("btnEditRole") as LinkButton;
                        if (editButton != null)
                        {
                            editButton.Visible = false;
                        }
                    }

                    if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Delete"))
                    {
                        var deleteButton = e.Row.FindControl("btnDeleteRole") as LinkButton;
                        if (deleteButton != null)
                        {
                            deleteButton.Visible = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 記錄錯誤但不影響頁面顯示
                    System.Diagnostics.Debug.WriteLine($"RowDataBound 錯誤: {ex.Message}");
                }
            }
        }

        protected void gvRoleUsers_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "RemoveUser")
                {
                    string userId = e.CommandArgument.ToString();
                    
                    if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Update"))
                    {
                        ShowMessage("您沒有移除用戶角色的權限", "warning");
                        return;
                    }

                    RemoveUserFromRole(userId, _selectedRoleId);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"移除用戶失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region 按鈕事件

        protected void btnCreateRole_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Create"))
                {
                    ShowMessage("您沒有建立角色的權限", "warning");
                    return;
                }

                // 清除表單
                hdnRoleId.Value = "";
                txtRoleName.Text = "";
                txtDescription.Text = "";
                chkIsActive.Checked = true;
                lblModalTitle.Text = "新增角色";

                ScriptManager.RegisterStartupScript(this, GetType(), "showRoleModal", "showRoleModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"開啟新增角色視窗失敗: {ex.Message}", "danger");
            }
        }

        protected void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadRoleStatistics();
                LoadRoles();
                
                // 清除選中狀態
                ViewState["SelectedRoleId"] = null;
                _selectedRoleId = null;
                pnlRoleUsers.Visible = false;
                pnlNoRoleSelected.Visible = true;
                btnAssignUser.Visible = false;

                ShowMessage("頁面已重新整理", "success");
            }
            catch (Exception ex)
            {
                ShowMessage($"重新整理失敗: {ex.Message}", "danger");
            }
        }

        protected async void btnSaveRole_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                    return;

                string roleName = txtRoleName.Text.Trim();
                string description = txtDescription.Text.Trim();
                bool isActive = chkIsActive.Checked;
                string roleId = hdnRoleId.Value;

                Microsoft.AspNet.Identity.IdentityResult result;

                if (string.IsNullOrEmpty(roleId))
                {
                    // 新增角色
                    if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Create"))
                    {
                        ShowMessage("您沒有建立角色的權限", "warning");
                        return;
                    }

                    result = await _roleService.CreateRoleAsync(roleName, description, _currentUserId);
                }
                else
                {
                    // 更新角色
                    if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Update"))
                    {
                        ShowMessage("您沒有編輯角色的權限", "warning");
                        return;
                    }

                    result = await _roleService.UpdateRoleAsync(roleId, roleName, description, isActive, _currentUserId);
                }

                if (result.Succeeded)
                {
                    ShowMessage(string.IsNullOrEmpty(roleId) ? "角色建立成功" : "角色更新成功", "success");
                    LoadRoleStatistics();
                    LoadRoles();

                    // 清除表單
                    hdnRoleId.Value = "";
                    txtRoleName.Text = "";
                    txtDescription.Text = "";
                    chkIsActive.Checked = true;
                }
                else
                {
                    var errors = string.Join("<br/>", result.Errors.Select(e => e.Description));
                    ShowMessage($"操作失敗: {errors}", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"儲存角色失敗: {ex.Message}", "danger");
            }
        }

        protected void btnAssignUser_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedRoleId))
                {
                    ShowMessage("請先選擇一個角色", "warning");
                    return;
                }

                if (!_permissionService.HasPermission(_currentUserId, "UserManagement", "Update"))
                {
                    ShowMessage("您沒有分配用戶角色的權限", "warning");
                    return;
                }

                LoadAvailableUsers(_selectedRoleId);
                ScriptManager.RegisterStartupScript(this, GetType(), "showAssignUserModal", "showAssignUserModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"開啟分配用戶視窗失敗: {ex.Message}", "danger");
            }
        }

        protected async void btnSaveUserAssignment_Click(object sender, EventArgs e)
        {
            try
            {
                string roleId = ViewState["AssignRoleId"]?.ToString();
                if (string.IsNullOrEmpty(roleId))
                {
                    ShowMessage("操作失敗：角色資訊遺失", "danger");
                    return;
                }

                var selectedUserIds = new List<string>();
                foreach (ListItem item in cblAvailableUsers.Items)
                {
                    if (item.Selected)
                    {
                        selectedUserIds.Add(item.Value);
                    }
                }

                if (!selectedUserIds.Any())
                {
                    ShowMessage("請選擇要分配的用戶", "warning");
                    return;
                }

                int successCount = 0;
                var errors = new List<string>();

                foreach (string userId in selectedUserIds)
                {
                    var result = await _roleService.AssignRoleToUserAsync(userId, roleId, _currentUserId);
                    if (result.Succeeded)
                    {
                        successCount++;
                    }
                    else
                    {
                        errors.AddRange(result.Errors.Select(e => e.Description));
                    }
                }

                if (successCount > 0)
                {
                    ShowMessage($"成功分配 {successCount} 個用戶到角色", "success");
                    LoadRoleUsers(roleId); // 重新載入角色用戶
                }

                if (errors.Any())
                {
                    ShowMessage($"部分操作失敗: {string.Join("; ", errors)}", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"分配用戶失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 編輯角色
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        private void EditRole(string roleId)
        {
            try
            {
                var role = _roleService.GetRoleById(roleId);
                if (role == null)
                {
                    ShowMessage("找不到指定的角色", "warning");
                    return;
                }

                hdnRoleId.Value = role.Id;
                txtRoleName.Text = role.Name;
                txtDescription.Text = role.Description ?? "";
                chkIsActive.Checked = role.IsActive;
                lblModalTitle.Text = "編輯角色";

                ScriptManager.RegisterStartupScript(this, GetType(), "showRoleModal", "showRoleModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"載入角色資料失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 刪除角色
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        private async void DeleteRole(string roleId)
        {
            try
            {
                var result = await _roleService.DeleteRoleAsync(roleId, _currentUserId, false);

                if (result.Succeeded)
                {
                    ShowMessage("角色刪除成功", "success");
                    LoadRoleStatistics();
                    LoadRoles();

                    // 如果刪除的是當前選中的角色，清除選中狀態
                    if (_selectedRoleId == roleId)
                    {
                        ViewState["SelectedRoleId"] = null;
                        _selectedRoleId = null;
                        pnlRoleUsers.Visible = false;
                        pnlNoRoleSelected.Visible = true;
                        btnAssignUser.Visible = false;
                    }
                }
                else
                {
                    var errors = string.Join("<br/>", result.Errors.Select(e => e.Description));
                    ShowMessage($"刪除角色失敗: {errors}", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"刪除角色失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 移除用戶的角色
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="roleId">角色 ID</param>
        private async void RemoveUserFromRole(string userId, string roleId)
        {
            try
            {
                var result = await _roleService.RemoveRoleFromUserAsync(userId, roleId, _currentUserId);

                if (result.Succeeded)
                {
                    ShowMessage("用戶角色移除成功", "success");
                    LoadRoleUsers(roleId); // 重新載入角色用戶
                }
                else
                {
                    var errors = string.Join("<br/>", result.Errors.Select(e => e.Description));
                    ShowMessage($"移除用戶角色失敗: {errors}", "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"移除用戶角色失敗: {ex.Message}", "danger");
            }
        }

        /// <summary>
        /// 取得角色的用戶數量
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <returns>用戶數量</returns>
        private int GetRoleUserCount(string roleId)
        {
            try
            {
                using (var context = new Data.ApplicationDbContext())
                {
                    return context.ApplicationUserRoles.Count(ur => ur.RoleId == roleId);
                }
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 顯示訊息
        /// </summary>
        /// <param name="message">訊息內容</param>
        /// <param name="type">訊息類型 (success, warning, danger, info)</param>
        private void ShowMessage(string message, string type)
        {
            string alertClass = $"alert alert-{type} alert-dismissible fade show";
            pnlMessage.CssClass = alertClass;
            ltlMessage.Text = message;
            pnlMessage.Visible = true;
        }

        /// <summary>
        /// 註冊必要的腳本和樣式
        /// </summary>
        private void RegisterScripts()
        {
            // 註冊 Bootstrap 和 DataTables
            if (!Page.ClientScript.IsClientScriptIncludeRegistered("DataTables"))
            {
                string script = @"
                    <link rel='stylesheet' type='text/css' href='https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap4.min.css'>
                    <script type='text/javascript' src='https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js'></script>
                    <script type='text/javascript' src='https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap4.min.js'></script>
                ";

                Page.ClientScript.RegisterClientScriptBlock(GetType(), "DataTables", script, false);
            }

            // 註冊 SweetAlert2
            if (!Page.ClientScript.IsClientScriptIncludeRegistered("SweetAlert2"))
            {
                string script = @"
                    <script src='https://cdn.jsdelivr.net/npm/sweetalert2@11'></script>
                ";

                Page.ClientScript.RegisterClientScriptBlock(GetType(), "SweetAlert2", script, false);
            }
        }

        #endregion
    }
}