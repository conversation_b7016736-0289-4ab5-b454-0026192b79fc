using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Services.Interfaces;
using CWDECC_3S.Data;

namespace CWDECC_3S
{
    public partial class Default : System.Web.UI.Page
    {
        private FirebaseService firebaseService;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                firebaseService = new FirebaseService();
                lblSystemStatus.Text = "系統正常運行中... (" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + ")";
            }
        }

        protected void btnTestFirebase_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new FirebaseService();
                
                // Test Firebase connection
                var testResult = firebaseService.TestConnection();
                
                if (testResult)
                {
                    lblFirebaseStatus.Text = "Firebase 連線成功！";
                    lblFirebaseStatus.CssClass = "label label-success";
                }
                else
                {
                    lblFirebaseStatus.Text = "Firebase 連線失敗！";
                    lblFirebaseStatus.CssClass = "label label-danger";
                }
            }
            catch (Exception ex)
            {
                lblFirebaseStatus.Text = "Firebase 連線錯誤: " + ex.Message;
                lblFirebaseStatus.CssClass = "label label-danger";
            }
        }
    }
}