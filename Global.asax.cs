using System;
using System.Web;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;
using System.Web.SessionState;
using System.Configuration;
using CWDECC_3S.Data;
using CWDECC_3S.Services;

namespace CWDECC_3S
{
    public class Global : HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {
            // Code that runs on application startup
            RegisterBundles(BundleTable.Bundles);
            RegisterRoutes(RouteTable.Routes);
            
            // Initialize application-wide settings
            InitializeApplication();
            
            // Log application start
            LogApplicationEvent("Application started successfully");
        }

        protected void Session_Start(object sender, EventArgs e)
        {
            try
            {
                // 初始化 Session
                InitializeSession();
                
                // 記錄 Session 開始
                LogSessionEvent("SESSION_START");
            }
            catch (Exception ex)
            {
                LogApplicationError(ex);
            }
        }

        private void InitializeSession()
        {
            // 設定 Session 基本屬性
            Session["SessionStartTime"] = DateTime.UtcNow;
            Session["IsAuthenticated"] = false;
            Session["LoginAttempts"] = 0;
            Session["LastActivityTime"] = DateTime.UtcNow;
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            try
            {
                // HTTPS 重定向（生產環境）
                EnforceHTTPS();
                
                // 安全標頭設定
                SetSecurityHeaders();
                
                // Session 有效性檢查
                CheckSessionValidity();
                
                // Culture settings
                var culture = ConfigurationManager.AppSettings["DefaultCulture"] ?? "zh-HK";
                System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo(culture);
                System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(culture);
            }
            catch (Exception ex)
            {
                LogApplicationError(ex);
            }
        }

        private void EnforceHTTPS()
        {
            // 僅在生產環境強制 HTTPS
            if (ConfigurationManager.AppSettings["EnforceHTTPS"] == "true")
            {
                if (!Request.IsSecureConnection && !Request.IsLocal)
                {
                    string httpsUrl = Request.Url.ToString().Replace("http://", "https://");
                    Response.Redirect(httpsUrl, false);
                    Context.ApplicationInstance.CompleteRequest();
                }
            }
        }

        private void SetSecurityHeaders()
        {
            // 設定安全相關的 HTTP 標頭
            if (!Response.HeadersWritten)
            {
                // 防止點擊劫持
                Response.Headers.Add("X-Frame-Options", "SAMEORIGIN");
                
                // 防止 MIME 類型嗅探
                Response.Headers.Add("X-Content-Type-Options", "nosniff");
                
                // XSS 保護
                Response.Headers.Add("X-XSS-Protection", "1; mode=block");
                
                // 內容安全政策（基本設定）
                Response.Headers.Add("Content-Security-Policy", 
                    "default-src 'self'; " +
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net cdnjs.cloudflare.com code.jquery.com; " +
                    "style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; " +
                    "font-src 'self' cdnjs.cloudflare.com; " +
                    "img-src 'self' data:; " +
                    "connect-src 'self'");
                
                // 引薦來源政策
                Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
                
                // 權限政策
                Response.Headers.Add("Permissions-Policy", 
                    "camera=(), microphone=(), geolocation=(), payment=()");
            }
        }

        private void CheckSessionValidity()
        {
            // 跳過不需要 Session 檢查的頁面
            var currentPath = Request.CurrentExecutionFilePath.ToLower();
            if (ShouldSkipSessionCheck(currentPath))
                return;

            // 檢查用戶是否已登入但 Session 可能過期
            if (Session["IsAuthenticated"] as bool? == true)
            {
                try
                {
                    using (var authService = new AuthenticationService())
                    {
                        if (!authService.IsSessionValid())
                        {
                            // Session 無效，重定向到登入頁面
                            RedirectToLogin();
                        }
                        else
                        {
                            // 更新最後活動時間
                            Session["LastActivityTime"] = DateTime.UtcNow;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogApplicationError(ex);
                    // 發生錯誤時，為安全起見重定向到登入頁面
                    RedirectToLogin();
                }
            }
        }

        private bool ShouldSkipSessionCheck(string path)
        {
            var skipPaths = new[]
            {
                "/login.aspx",
                "/styles/",
                "/scripts/",
                "/images/",
                "/favicon.ico",
                "/robots.txt",
                "/sitemap.xml",
                "/content/",
                "/bundles/"
            };

            foreach (var skipPath in skipPaths)
            {
                if (path.Contains(skipPath))
                    return true;
            }

            return false;
        }

        private void RedirectToLogin()
        {
            var loginUrl = "~/Login.aspx";
            var returnUrl = Request.RawUrl;
            
            // 清除 Session
            Session.Clear();
            Session.Abandon();
            
            // 清除 PermissionService
            PermissionService.ClearUserRole();
            
            // 如果不是 AJAX 請求，進行重定向
            if (!IsAjaxRequest())
            {
                if (!string.IsNullOrEmpty(returnUrl) && returnUrl != "/" && !returnUrl.Contains("Login.aspx"))
                {
                    loginUrl += $"?ReturnUrl={HttpUtility.UrlEncode(returnUrl)}";
                }
                
                Response.Redirect(loginUrl, false);
                Context.ApplicationInstance.CompleteRequest();
            }
            else
            {
                // AJAX 請求返回 401 狀態碼
                Response.StatusCode = 401;
                Response.End();
            }
        }

        private bool IsAjaxRequest()
        {
            return Request.Headers["X-Requested-With"] == "XMLHttpRequest";
        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {
            // Code that runs when a request is authenticated
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            // Code that runs when an unhandled error occurs
            Exception exception = Server.GetLastError();
            
            if (exception != null)
            {
                LogApplicationError(exception);
                
                // Handle specific error types
                if (exception is HttpException httpException)
                {
                    var statusCode = httpException.GetHttpCode();
                    switch (statusCode)
                    {
                        case 404:
                            Response.Redirect("~/NotFound.aspx", false);
                            break;
                        case 500:
                            Response.Redirect("~/Error.aspx", false);
                            break;
                        default:
                            Response.Redirect("~/Error.aspx", false);
                            break;
                    }
                }
                else
                {
                    Response.Redirect("~/Error.aspx", false);
                }
                
                Server.ClearError();
            }
        }

        protected void Session_End(object sender, EventArgs e)
        {
            try
            {
                // 記錄 Session 結束
                LogSessionEvent("SESSION_END");
                
                // 清理 Session 相關資源
                CleanupSession();
            }
            catch (Exception ex)
            {
                LogApplicationError(ex);
            }
        }

        private void CleanupSession()
        {
            try
            {
                // 如果用戶已登入，記錄自動登出
                if (Session["IsAuthenticated"] as bool? == true)
                {
                    var userId = Session["UserId"] as string;
                    var userName = Session["UserName"] as string;
                    
                    using (var auditService = new AuditService())
                    {
                        auditService.LogAsync("SESSION_EXPIRED", userId, userName, 
                            GetClientIPAddress(), "Session 過期自動登出", 
                            Request?.UserAgent ?? "unknown").Wait();
                    }
                }
                
                // 清除 PermissionService 中的用戶狀態
                PermissionService.ClearUserRole();
            }
            catch (Exception ex)
            {
                LogApplicationError(ex);
            }
        }

        protected void Application_End(object sender, EventArgs e)
        {
            // Code that runs on application shutdown
            LogApplicationEvent("Application ended");
        }

        private void RegisterBundles(BundleCollection bundles)
        {
            // Register JavaScript bundles
            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Scripts/jquery-{version}.js"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                        "~/Scripts/jquery.validate*"));

            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.js"));

            // Register CSS bundles
            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/bootstrap.css",
                      "~/Styles/site.css"));

            // Enable optimization in release mode
#if !DEBUG
            BundleTable.EnableOptimizations = true;
#endif
        }

        private void RegisterRoutes(RouteCollection routes)
        {
            // Ignore route for WebResource and ScriptResource requests
            routes.Ignore("{resource}.axd/{*pathInfo}");
            
            // Add custom routes here if needed
            // Example:
            // routes.MapPageRoute("MembersList", "Members", "~/Members/List.aspx");
            // routes.MapPageRoute("ActivitiesList", "Activities", "~/Activities/List.aspx");
        }

        private void InitializeApplication()
        {
            try
            {
                // Initialize Firebase connection on application startup
                var firebaseService = new FirebaseService();
                var connectionTest = firebaseService.TestConnection();
                
                LogApplicationEvent($"Firebase connection test: {(connectionTest ? "Success" : "Failed")}");
                
                // Set application-wide properties
                Application["ApplicationName"] = ConfigurationManager.AppSettings["ApplicationName"];
                Application["ApplicationVersion"] = ConfigurationManager.AppSettings["ApplicationVersion"];
                Application["StartTime"] = DateTime.Now;
                
                // Initialize caching if enabled
                var enableCaching = bool.Parse(ConfigurationManager.AppSettings["EnableCaching"] ?? "false");
                if (enableCaching)
                {
                    Application["CachingEnabled"] = true;
                    LogApplicationEvent("Application caching enabled");
                }
            }
            catch (Exception ex)
            {
                LogApplicationError(ex);
            }
        }

        private void LogSessionEvent(string eventType)
        {
            try
            {
                using (var auditService = new AuditService())
                {
                    auditService.LogAsync(eventType, 
                        Session?["UserId"] as string, 
                        Session?["UserName"] as string, 
                        GetClientIPAddress(), 
                        $"Session {eventType}", 
                        Request?.UserAgent ?? "unknown").Wait();
                }
            }
            catch (Exception ex)
            {
                LogApplicationError(ex);
            }
        }

        private string GetClientIPAddress()
        {
            try
            {
                return AuthenticationService.GetClientIPAddress();
            }
            catch
            {
                return "unknown";
            }
        }

        private void LogApplicationEvent(string message)
        {
            try
            {
                var logToFile = bool.Parse(ConfigurationManager.AppSettings["LogToFile"] ?? "false");
                if (logToFile)
                {
                    var logPath = Server.MapPath(ConfigurationManager.AppSettings["LogFilePath"] ?? "~/App_Data/Logs/");
                    
                    // Ensure log directory exists
                    if (!System.IO.Directory.Exists(logPath))
                    {
                        System.IO.Directory.CreateDirectory(logPath);
                    }
                    
                    var logFile = System.IO.Path.Combine(logPath, $"application_{DateTime.Now:yyyyMMdd}.log");
                    var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [INFO] {message}{Environment.NewLine}";
                    
                    System.IO.File.AppendAllText(logFile, logEntry);
                }

                // 同時使用審計服務記錄
                using (var auditService = new AuditService())
                {
                    auditService.LogAsync("APPLICATION_EVENT", null, "system", GetClientIPAddress(), message, "system").Wait();
                }
            }
            catch (Exception)
            {
                // Ignore logging errors to prevent application crashes
            }
        }

        private void LogApplicationError(Exception exception)
        {
            try
            {
                var logToFile = bool.Parse(ConfigurationManager.AppSettings["LogToFile"] ?? "false");
                if (logToFile)
                {
                    var logPath = Server.MapPath(ConfigurationManager.AppSettings["LogFilePath"] ?? "~/App_Data/Logs/");
                    
                    // Ensure log directory exists
                    if (!System.IO.Directory.Exists(logPath))
                    {
                        System.IO.Directory.CreateDirectory(logPath);
                    }
                    
                    var logFile = System.IO.Path.Combine(logPath, $"errors_{DateTime.Now:yyyyMMdd}.log");
                    var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [ERROR] {exception.Message}{Environment.NewLine}";
                    logEntry += $"Stack Trace: {exception.StackTrace}{Environment.NewLine}";
                    logEntry += $"Source: {exception.Source}{Environment.NewLine}{Environment.NewLine}";
                    
                    System.IO.File.AppendAllText(logFile, logEntry);
                }
            }
            catch (Exception)
            {
                // Ignore logging errors to prevent application crashes
            }
        }
    }
}