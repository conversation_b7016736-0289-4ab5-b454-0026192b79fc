# CWDECC 3S Social Service System

## 專案概述
CWDECC 3S (Social Service System) 是一個基於 ASP.NET Web Forms 和 Firebase 的社會服務管理系統，採用三層架構設計。

## 系統架構
```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer (表現層)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Default.aspx │  │ Members/    │  │ Activities/ │         │
│  │             │  │ Default.aspx │  │ Default.aspx │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer (業務層)                   │
│  ┌─────────────────┐        ┌─────────────────┐            │
│  │ MemberService   │        │ ActivityService │            │
│  │ IMemberService  │        │ IActivityService │            │
│  └─────────────────┘        └─────────────────┘            │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Data Layer (資料層)                     │
│  ┌─────────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ FirebaseService │  │ CryptoHelper │  │ValidationHelper│     │
│  │                 │  │             │  │             │     │
│  └─────────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Firebase Firestore                      │
│                    (雲端資料庫)                             │
└─────────────────────────────────────────────────────────────┘
```

## 專案結構
```
CWDECC-3S/
├── Data/                          # 資料層
│   ├── FirebaseService.cs         # Firebase 服務
│   ├── CryptoHelper.cs           # 加密工具
│   └── ValidationHelper.cs       # 驗證工具
├── Services/                      # 服務層
│   ├── Interfaces/               # 服務介面
│   │   ├── IMemberService.cs
│   │   └── IActivityService.cs
│   ├── MemberService.cs          # 會員服務
│   └── ActivityService.cs        # 活動服務
├── Models/                        # 資料模型
│   ├── Member.cs                 # 會員模型
│   └── Activity.cs               # 活動模型
├── Members/                       # 會員管理頁面
│   └── Default.aspx
├── Activities/                    # 活動管理頁面
│   └── Default.aspx
├── Admin/                         # 系統管理頁面
│   └── Default.aspx
├── Styles/                        # 樣式檔案
│   └── site.css
├── Scripts/                       # JavaScript 檔案
├── App_Data/                      # 應用程式資料
├── Properties/                    # 專案屬性
├── Site.Master                    # 主版頁面
├── Default.aspx                   # 首頁
├── Global.asax                    # 應用程式事件
├── Web.config                     # 設定檔
└── packages.config               # NuGet 套件
```

## 技術堆疊
- **前端**: ASP.NET Web Forms, Bootstrap, jQuery
- **後端**: C# .NET Framework 4.8
- **資料庫**: Google Firebase Firestore
- **驗證**: Google Firebase Authentication (計劃中)
- **部署**: IIS

## 設定步驟

### 1. Firebase 設定
1. 在 Firebase Console 建立新專案
2. 啟用 Firestore 資料庫
3. 取得專案設定資訊
4. 更新 `Web.config` 中的 Firebase 設定：
   ```xml
   <add key="FirebaseProjectId" value="your-firebase-project-id" />
   <add key="FirebaseApiKey" value="your-firebase-api-key" />
   ```

### 2. Visual Studio 設定
1. 確保已安裝 .NET Framework 4.8
2. 還原 NuGet 套件
3. 建置專案
4. 設定 IIS Express 或完整 IIS

### 3. 執行測試
1. 啟動專案 (F5)
2. 訪問首頁，點擊「測試 Firebase 連線」
3. 前往會員管理頁面 (`/Members/`)
4. 點擊「測試三層架構」驗證系統功能

## 主要功能

### 會員管理
- 會員註冊和資料管理
- 會員搜尋和查詢
- 會員狀態管理
- 統計報表

### 活動管理
- 活動建立和編輯
- 活動報名管理
- 參與者管理
- 活動狀態追蹤

### 系統管理
- 系統監控
- 用戶權限管理
- 設定管理
- 記錄檢視

## 安全特性
- HTTPS 強制使用
- XSS 防護
- CSRF 防護
- 資料加密
- 輸入驗證
- 安全標頭設定

## 測試驗收標準 (AC)

### ✅ A-1: 專案啟動
- [x] 可以成功啟動專案並顯示 Default.aspx
- [x] Master Page 正常顯示導航選單
- [x] 樣式和佈局正確

### ✅ A-2: 三層架構
- [x] UI 層：Default.aspx 和子頁面正常運作
- [x] Service 層：MemberService, ActivityService 可正常調用
- [x] Data 層：FirebaseService 可進行 CRUD 操作

### 🔄 A-3: Firebase 連線 (需要 Firebase 專案設定)
- [ ] Firebase 連線測試成功
- [ ] 可讀取測試集合
- [ ] 可進行基本的 CRUD 操作

## 後續開發計劃
1. 完整的會員註冊和登入功能
2. 詳細的活動管理介面
3. 報表和統計功能
4. 行動裝置優化
5. 多語言支援
6. API 介面開發

## 開發環境需求
- Visual Studio 2019 或更新版本
- .NET Framework 4.8
- IIS Express 或 IIS
- Google Firebase 專案

## 聯絡資訊
- 專案負責人: [Your Name]
- 電子郵件: [<EMAIL>]
- 專案版本: 1.0.0