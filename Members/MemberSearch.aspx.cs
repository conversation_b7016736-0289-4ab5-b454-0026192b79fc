using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Microsoft.AspNet.Identity;
using CWDECC_3S.Models;
using CWDECC_3S.Services;

namespace CWDECC_3S.Members
{
    /// <summary>
    /// 會員搜尋頁面 - 提供會員資料搜尋、續會、編輯等功能
    /// </summary>
    public partial class MemberSearch : System.Web.UI.Page
    {
        private MemberService _memberService;
        private PermissionService _permissionService;
        private string _currentUserId;

        protected void Page_Init(object sender, EventArgs e)
        {
            _memberService = new MemberService();
            _permissionService = new PermissionService();
            _currentUserId = User.Identity.GetUserId();
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限 - 只有前台職員和社工角色可以操作
                if (!_permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff") &&
                    !_permissionService.HasRolePermission(_currentUserId, "SocialWorker") &&
                    !_permissionService.HasRolePermission(_currentUserId, "Administrator"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    InitializeControls();
                    LoadStatistics();
                    LoadInitialData();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"頁面載入錯誤: {ex.Message}", "danger");
            }
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            RegisterScripts();
        }

        protected void Page_Unload(object sender, EventArgs e)
        {
            _memberService?.Dispose();
            _permissionService?.Dispose();
        }

        #region 初始化

        /// <summary>
        /// 初始化控制項
        /// </summary>
        private void InitializeControls()
        {
            // 初始化下拉選單
            InitializeDropDownLists();

            // 設定 GridView
            gvMembers.PageSize = 20;

            // 設定權限控制
            SetPermissionControls();
        }

        /// <summary>
        /// 初始化下拉選單
        /// </summary>
        private void InitializeDropDownLists()
        {
            // 會籍狀態
            var membershipStatuses = MemberConstants.MembershipStatuses.GetAll();
            ddlMembershipStatus.Items.Clear();
            ddlMembershipStatus.Items.Add(new ListItem("全部", ""));
            foreach (var status in membershipStatuses)
            {
                ddlMembershipStatus.Items.Add(new ListItem(status.Value, status.Key));
            }
            
            // 添加過期狀態選項
            ddlMembershipStatus.Items.Add(new ListItem("過期", "Expired"));
        }

        /// <summary>
        /// 設定權限控制
        /// </summary>
        private void SetPermissionControls()
        {
            // 檢查是否有新增會員權限
            bool canAddMember = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                               _permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff");
            btnAddMember.Visible = canAddMember;

            // 檢查是否有更新狀態權限
            bool canUpdateStatus = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                                  _permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff");
            btnUpdateStatuses.Visible = canUpdateStatus;
        }

        #endregion

        #region 資料載入

        /// <summary>
        /// 載入統計資訊
        /// </summary>
        private async void LoadStatistics()
        {
            try
            {
                var stats = await _memberService.GetMemberStatisticsAsync();

                lblTotalMembers.Text = stats.TotalMembers.ToString();
                lblActiveMembers.Text = stats.ActiveMembers.ToString();
                lblExpiringSoon.Text = stats.ExpiringSoonMembers.ToString();
                lblExpiredMembers.Text = stats.ExpiredMembers.ToString();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入統計資訊失敗: {ex.Message}", "warning");
            }
        }

        /// <summary>
        /// 載入初始資料
        /// </summary>
        private void LoadInitialData()
        {
            // 初始時顯示空的 GridView
            gvMembers.DataSource = new List<Member>();
            gvMembers.DataBind();
            lblRecordCount.Text = "0";
        }

        /// <summary>
        /// 執行搜尋
        /// </summary>
        private async void PerformSearch()
        {
            try
            {
                var keyword = txtKeyword.Text.Trim();
                var searchBy = ddlSearchBy.SelectedValue;

                if (string.IsNullOrEmpty(keyword))
                {
                    ShowMessage("請輸入搜尋關鍵字", "warning");
                    return;
                }

                var results = await _memberService.SearchMembersAsync(keyword, searchBy, _currentUserId);

                // 根據會籍狀態篩選
                if (!string.IsNullOrEmpty(ddlMembershipStatus.SelectedValue))
                {
                    var statusFilter = ddlMembershipStatus.SelectedValue;
                    if (statusFilter == "Expired")
                    {
                        results = results.Where(m => m.IsMembershipExpired).ToList();
                    }
                    else
                    {
                        results = results.Where(m => m.MembershipStatus == statusFilter && !m.IsMembershipExpired).ToList();
                    }
                }

                gvMembers.DataSource = results;
                gvMembers.DataBind();
                
                lblRecordCount.Text = results.Count.ToString();

                if (results.Count == 0)
                {
                    ShowMessage("沒有找到符合條件的會員資料", "info");
                }
                else if (results.Count >= 50)
                {
                    ShowMessage($"找到 {results.Count} 筆資料（已限制顯示前50筆，請縮小搜尋範圍以獲得更精確的結果）", "info");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"搜尋失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region 按鈕事件

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        protected void btnClear_Click(object sender, EventArgs e)
        {
            txtKeyword.Text = "";
            ddlSearchBy.SelectedIndex = 0;
            ddlMembershipStatus.SelectedIndex = 0;
            
            LoadInitialData();
            ShowMessage("搜尋條件已清除", "success");
        }

        protected void btnAddMember_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Members/MemberRegistration.aspx");
        }

        protected async void btnUpdateStatuses_Click(object sender, EventArgs e)
        {
            try
            {
                var updatedCount = await _memberService.UpdateAllMembershipStatusesAsync(_currentUserId);
                
                if (updatedCount > 0)
                {
                    ShowMessage($"已更新 {updatedCount} 個會員的會籍狀態", "success");
                    LoadStatistics();
                    
                    // 如果有搜尋結果，重新搜尋以更新顯示
                    if (!string.IsNullOrEmpty(txtKeyword.Text.Trim()))
                    {
                        PerformSearch();
                    }
                }
                else
                {
                    ShowMessage("沒有需要更新的會籍狀態", "info");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"更新會籍狀態失敗: {ex.Message}", "danger");
            }
        }

        protected async void btnConfirmRenew_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                    return;

                int memberId = int.Parse(hdnRenewMemberId.Value);
                int months = int.Parse(ddlRenewMonths.SelectedValue);
                decimal fee = decimal.Parse(txtRenewFee.Text);

                var result = await _memberService.RenewMembershipAsync(memberId, months, fee, _currentUserId);

                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    LoadStatistics();
                    
                    // 重新搜尋以更新顯示
                    if (!string.IsNullOrEmpty(txtKeyword.Text.Trim()))
                    {
                        PerformSearch();
                    }
                    
                    // 清除續會表單
                    ClearRenewModal();
                }
                else
                {
                    ShowMessage(result.Message, "danger");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"續會失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region GridView 事件

        protected async void gvMembers_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int memberId = int.Parse(e.CommandArgument.ToString());

                switch (e.CommandName)
                {
                    case "ViewProfile":
                        Response.Redirect($"~/Members/MemberProfile.aspx?id={memberId}");
                        break;

                    case "EditMember":
                        Response.Redirect($"~/Members/MemberProfile.aspx?id={memberId}&mode=edit");
                        break;

                    case "RenewMembership":
                        await ShowRenewModal(memberId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"操作失敗: {ex.Message}", "danger");
            }
        }

        protected void gvMembers_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                try
                {
                    var member = (Member)e.Row.DataItem;

                    // 權限控制
                    bool canEdit = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                                  _permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff");
                    
                    bool canRenew = _permissionService.HasRolePermission(_currentUserId, "Administrator") ||
                                   _permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff");

                    var btnEdit = e.Row.FindControl("btnEdit") as LinkButton;
                    var btnRenew = e.Row.FindControl("btnRenew") as LinkButton;

                    if (btnEdit != null) btnEdit.Visible = canEdit;
                    if (btnRenew != null) btnRenew.Visible = canRenew && (member.IsMembershipExpired || member.IsMembershipExpiringSoon);

                    // 設定過期會員的行樣式
                    if (member.IsMembershipExpired)
                    {
                        e.Row.CssClass += " table-danger";
                    }
                    else if (member.IsMembershipExpiringSoon)
                    {
                        e.Row.CssClass += " table-warning";
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"RowDataBound 錯誤: {ex.Message}");
                }
            }
        }

        protected void gvMembers_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvMembers.PageIndex = e.NewPageIndex;
            PerformSearch();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 顯示續會模態視窗
        /// </summary>
        /// <param name="memberId">會員 ID</param>
        private async System.Threading.Tasks.Task ShowRenewModal(int memberId)
        {
            var member = await _memberService.GetMemberByIdAsync(memberId);
            if (member == null)
            {
                ShowMessage("找不到指定的會員資料", "warning");
                return;
            }

            hdnRenewMemberId.Value = member.Id.ToString();
            lblRenewMemberName.Text = member.DisplayName;
            lblRenewCurrentEndDate.Text = member.MembershipEndDate.ToString("yyyy-MM-dd");

            // 清除表單
            ddlRenewMonths.SelectedValue = "12";
            txtRenewFee.Text = "";

            ScriptManager.RegisterStartupScript(this, GetType(), "showRenewModal", "showRenewModal();", true);
        }

        /// <summary>
        /// 清除續會模態視窗
        /// </summary>
        private void ClearRenewModal()
        {
            hdnRenewMemberId.Value = "";
            lblRenewMemberName.Text = "";
            lblRenewCurrentEndDate.Text = "";
            ddlRenewMonths.SelectedIndex = 0;
            txtRenewFee.Text = "";
        }

        /// <summary>
        /// 顯示訊息
        /// </summary>
        /// <param name="message">訊息內容</param>
        /// <param name="type">訊息類型</param>
        private void ShowMessage(string message, string type)
        {
            string alertClass = $"alert alert-{type} alert-dismissible fade show";
            pnlMessage.CssClass = alertClass;
            ltlMessage.Text = message;
            pnlMessage.Visible = true;
        }

        /// <summary>
        /// 註冊前端腳本
        /// </summary>
        private void RegisterScripts()
        {
            // 註冊必要的 JavaScript 庫
            if (!Page.ClientScript.IsClientScriptIncludeRegistered("SweetAlert2"))
            {
                string script = "<script src='https://cdn.jsdelivr.net/npm/sweetalert2@11'></script>";
                Page.ClientScript.RegisterClientScriptBlock(GetType(), "SweetAlert2", script, false);
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 取得性別顯示文字
        /// </summary>
        public string GetGenderDisplayText(string gender)
        {
            return gender == "M" ? "男" : gender == "F" ? "女" : gender;
        }

        /// <summary>
        /// 遮罩電話號碼
        /// </summary>
        public string MaskPhoneNumber(string phone)
        {
            if (string.IsNullOrEmpty(phone) || phone.Length <= 4)
                return phone;

            // 顯示前2位和後2位，中間用星號代替
            var masked = phone.Substring(0, 2) + new string('*', phone.Length - 4) + phone.Substring(phone.Length - 2);
            return masked;
        }

        /// <summary>
        /// 取得會籍狀態標籤顏色
        /// </summary>
        public string GetMembershipStatusBadgeColor(string status, bool isExpired)
        {
            if (isExpired)
                return "danger";

            switch (status)
            {
                case "Active": return "success";
                case "Expired": return "danger";
                case "Suspended": return "warning";
                case "Cancelled": return "secondary";
                default: return "secondary";
            }
        }

        /// <summary>
        /// 取得會籍狀態顯示文字
        /// </summary>
        public string GetMembershipStatusDisplayText(string status, bool isExpired)
        {
            if (isExpired)
                return "過期";

            switch (status)
            {
                case "Active": return "有效";
                case "Expired": return "過期";
                case "Suspended": return "暫停";
                case "Cancelled": return "取消";
                default: return status;
            }
        }

        /// <summary>
        /// 格式化剩餘天數
        /// </summary>
        public string FormatRemainingDays(int daysUntilExpiry)
        {
            if (daysUntilExpiry < 0)
            {
                return $"<span class='text-danger'>已過期 {Math.Abs(daysUntilExpiry)} 天</span>";
            }
            else if (daysUntilExpiry == 0)
            {
                return "<span class='text-warning'>今日到期</span>";
            }
            else if (daysUntilExpiry <= 30)
            {
                return $"<span class='text-warning'>{daysUntilExpiry} 天</span>";
            }
            else
            {
                return $"<span class='text-success'>{daysUntilExpiry} 天</span>";
            }
        }

        #endregion
    }
}