using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Services.Interfaces;
using CWDECC_3S.Services;
using CWDECC_3S.Models;

namespace CWDECC_3S.Members
{
    public partial class Default : System.Web.UI.Page
    {
        private IMemberService memberService;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                memberService = new MemberService();
                
                // Initialize the page
                lblTestResult.Visible = false;
            }
        }

        protected async void btnTestArchitecture_Click(object sender, EventArgs e)
        {
            try
            {
                memberService = new MemberService();
                
                // Test the three-tier architecture
                var testResult = await TestThreeTierArchitecture();
                
                lblTestResult.Text = testResult;
                lblTestResult.CssClass = testResult.Contains("成功") ? "label label-success" : "label label-danger";
                lblTestResult.Visible = true;
            }
            catch (Exception ex)
            {
                lblTestResult.Text = $"架構測試失敗: {ex.Message}";
                lblTestResult.CssClass = "label label-danger";
                lblTestResult.Visible = true;
            }
        }

        protected async void btnGetStatistics_Click(object sender, EventArgs e)
        {
            try
            {
                memberService = new MemberService();
                var statistics = await memberService.GetMemberStatisticsAsync();
                
                var statsHtml = "<div class='well'><h4>會員統計資料</h4>";
                foreach (var stat in statistics)
                {
                    statsHtml += $"<p><strong>{GetStatisticDisplayName(stat.Key)}:</strong> {stat.Value}</p>";
                }
                statsHtml += "</div>";
                
                ltlStatistics.Text = statsHtml;
            }
            catch (Exception ex)
            {
                ltlStatistics.Text = $"<div class='alert alert-danger'>取得統計資料失敗: {ex.Message}</div>";
            }
        }

        protected async void btnLoadMembers_Click(object sender, EventArgs e)
        {
            try
            {
                memberService = new MemberService();
                var members = await memberService.GetAllMembersAsync();
                
                gvMembers.DataSource = members.ToList();
                gvMembers.DataBind();
                
                if (!members.Any())
                {
                    // Create some sample data for testing
                    await CreateSampleMemberData();
                    
                    // Reload after creating sample data
                    members = await memberService.GetAllMembersAsync();
                    gvMembers.DataSource = members.ToList();
                    gvMembers.DataBind();
                }
            }
            catch (Exception ex)
            {
                ltlStatistics.Text = $"<div class='alert alert-danger'>載入會員列表失敗: {ex.Message}</div>";
            }
        }

        private async Task<string> TestThreeTierArchitecture()
        {
            try
            {
                // Test Data Layer (Firebase connection)
                var firebaseService = new Data.FirebaseService();
                var connectionTest = firebaseService.TestConnection();
                
                if (!connectionTest)
                {
                    return "資料層測試失敗：Firebase 連線失敗";
                }

                // Test Service Layer
                memberService = new MemberService();
                var members = await memberService.GetAllMembersAsync();
                
                // Test UI Layer (this page)
                var uiTest = (gvMembers != null && lblTestResult != null);
                
                if (!uiTest)
                {
                    return "UI層測試失敗：控件未正確初始化";
                }

                return $"三層架構測試成功！UI層 ✓ | Service層 ✓ | Data層 ✓ | 目前會員數量: {members.Count()}";
            }
            catch (Exception ex)
            {
                return $"三層架構測試失敗: {ex.Message}";
            }
        }

        private async Task CreateSampleMemberData()
        {
            try
            {
                var sampleMembers = new[]
                {
                    new Member
                    {
                        Name = "張志明",
                        Email = "<EMAIL>",
                        Phone = "9123-4567",
                        Address = "香港九龍旺角彌敦道123號",
                        Gender = "Male",
                        DateOfBirth = new DateTime(1985, 5, 15),
                        Notes = "測試會員資料"
                    },
                    new Member
                    {
                        Name = "李小芳",
                        Email = "<EMAIL>",
                        Phone = "9876-5432",
                        Address = "香港港島中環德輔道中456號",
                        Gender = "Female",
                        DateOfBirth = new DateTime(1990, 8, 22),
                        Notes = "測試會員資料"
                    },
                    new Member
                    {
                        Name = "王大明",
                        Email = "<EMAIL>",
                        Phone = "6789-1234",
                        Address = "香港新界沙田沙田正街789號",
                        Gender = "Male",
                        DateOfBirth = new DateTime(1988, 12, 3),
                        Notes = "測試會員資料"
                    }
                };

                foreach (var member in sampleMembers)
                {
                    await memberService.CreateMemberAsync(member);
                }
            }
            catch (Exception)
            {
                // Ignore errors when creating sample data
            }
        }

        private string GetStatisticDisplayName(string key)
        {
            switch (key)
            {
                case "TotalMembers": return "總會員數";
                case "ActiveMembers": return "活躍會員";
                case "InactiveMembers": return "非活躍會員";
                case "MaleMembers": return "男性會員";
                case "FemaleMembers": return "女性會員";
                case "RecentMembers": return "近期註冊會員";
                default: return key;
            }
        }
    }
}