<%@ Page Title="會員管理" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="CWDECC_3S.Members.Default" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">

    <div class="row">
        <div class="col-md-12">
            <h2>會員管理系統</h2>
            <p class="lead">管理會員資料、註冊和查詢功能</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">系統測試</h3>
                </div>
                <div class="panel-body">
                    <h4>三層架構測試</h4>
                    <p>測試 UI → Service → Data 層的調用</p>
                    
                    <asp:Button ID="btnTestArchitecture" runat="server" 
                        Text="測試三層架構" 
                        CssClass="btn btn-primary" 
                        OnClick="btnTestArchitecture_Click" />
                    
                    <asp:Label ID="lblTestResult" runat="server" 
                        CssClass="label label-info" 
                        Visible="false"></asp:Label>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">會員統計</h3>
                </div>
                <div class="panel-body">
                    <asp:Button ID="btnGetStatistics" runat="server" 
                        Text="取得會員統計" 
                        CssClass="btn btn-info" 
                        OnClick="btnGetStatistics_Click" />
                    
                    <asp:Literal ID="ltlStatistics" runat="server"></asp:Literal>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">會員列表</h3>
                </div>
                <div class="panel-body">
                    <asp:Button ID="btnLoadMembers" runat="server" 
                        Text="載入會員列表" 
                        CssClass="btn btn-success" 
                        OnClick="btnLoadMembers_Click" />
                    
                    <hr />
                    
                    <asp:GridView ID="gvMembers" runat="server" 
                        CssClass="table table-striped table-hover" 
                        AutoGenerateColumns="false" 
                        EmptyDataText="暫無會員資料">
                        <Columns>
                            <asp:BoundField DataField="Id" HeaderText="ID" Visible="false" />
                            <asp:BoundField DataField="Name" HeaderText="姓名" />
                            <asp:BoundField DataField="Email" HeaderText="電子郵件" />
                            <asp:BoundField DataField="Phone" HeaderText="電話" />
                            <asp:BoundField DataField="Status" HeaderText="狀態" />
                            <asp:BoundField DataField="CreatedDate" HeaderText="註冊日期" DataFormatString="{0:yyyy-MM-dd}" />
                        </Columns>
                    </asp:GridView>
                </div>
            </div>
        </div>
    </div>

</asp:Content>