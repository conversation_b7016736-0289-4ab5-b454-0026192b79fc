<%@ Page Title="會員搜尋" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MemberSearch.aspx.cs" Inherits="CWDECC_3S.Members.MemberSearch" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-search text-primary me-2"></i>會員搜尋
                </h2>
                <p class="text-muted">搜尋會員資料，支援會員號碼、姓名、電話模糊匹配</p>
            </div>
            <div>
                <asp:Button ID="btnAddMember" runat="server" Text="新增會員" CssClass="btn btn-success"
                    OnClick="btnAddMember_Click" />
                <asp:Button ID="btnUpdateStatuses" runat="server" Text="更新會籍狀態" CssClass="btn btn-warning"
                    OnClick="btnUpdateStatuses_Click" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <!-- 統計資訊卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-primary text-white rounded-circle shadow">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">總會員數</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblTotalMembers" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-success text-white rounded-circle shadow">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">有效會員</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblActiveMembers" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-warning text-white rounded-circle shadow">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">即將到期</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblExpiringSoon" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="icon icon-shape bg-danger text-white rounded-circle shadow">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="ms-3">
                                <p class="card-category text-muted">已過期</p>
                                <h3 class="card-title mb-0">
                                    <asp:Label ID="lblExpiredMembers" runat="server" Text="0"></asp:Label>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜尋區域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>搜尋條件
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="<%= txtKeyword.ClientID %>">搜尋關鍵字</label>
                            <asp:TextBox ID="txtKeyword" runat="server" CssClass="form-control" 
                                placeholder="請輸入會員號碼、姓名或電話"></asp:TextBox>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="<%= ddlSearchBy.ClientID %>">搜尋類型</label>
                            <asp:DropDownList ID="ddlSearchBy" runat="server" CssClass="form-select">
                                <asp:ListItem Text="全部" Value=""></asp:ListItem>
                                <asp:ListItem Text="會員號碼" Value="MemberNumber"></asp:ListItem>
                                <asp:ListItem Text="姓名" Value="Name"></asp:ListItem>
                                <asp:ListItem Text="電話" Value="Phone"></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="<%= ddlMembershipStatus.ClientID %>">會籍狀態</label>
                            <asp:DropDownList ID="ddlMembershipStatus" runat="server" CssClass="form-select">
                                <asp:ListItem Text="全部" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-grid">
                                <asp:Button ID="btnSearch" runat="server" Text="搜尋" CssClass="btn btn-primary"
                                    OnClick="btnSearch_Click" />
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-3">
                        <div class="form-group">
                            <asp:Button ID="btnClear" runat="server" Text="清除條件" CssClass="btn btn-outline-secondary"
                                OnClick="btnClear_Click" CausesValidation="false" />
                        </div>
                    </div>
                    <div class="col-md-9 text-end">
                        <small class="text-muted">
                            搜尋結果數量：<asp:Label ID="lblRecordCount" runat="server" Text="0"></asp:Label> 筆
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜尋結果 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>搜尋結果
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <asp:GridView ID="gvMembers" runat="server" CssClass="table table-striped table-hover"
                        AutoGenerateColumns="False" EmptyDataText="沒有找到符合條件的會員資料"
                        OnRowCommand="gvMembers_RowCommand" OnRowDataBound="gvMembers_RowDataBound"
                        AllowPaging="True" PageSize="20" OnPageIndexChanging="gvMembers_PageIndexChanging">
                        <Columns>
                            <asp:BoundField DataField="MemberNumber" HeaderText="會員號碼" SortExpression="MemberNumber">
                                <HeaderStyle Width="12%" />
                            </asp:BoundField>
                            <asp:BoundField DataField="FullName" HeaderText="姓名" SortExpression="FullName">
                                <HeaderStyle Width="15%" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="性別">
                                <ItemTemplate>
                                    <%# GetGenderDisplayText(Eval("Gender").ToString()) %>
                                </ItemTemplate>
                                <HeaderStyle Width="8%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="電話">
                                <ItemTemplate>
                                    <%# MaskPhoneNumber(Eval("Phone").ToString()) %>
                                </ItemTemplate>
                                <HeaderStyle Width="12%" />
                            </asp:TemplateField>
                            <asp:BoundField DataField="MembershipEndDate" HeaderText="到期日期" SortExpression="MembershipEndDate" 
                                DataFormatString="{0:yyyy-MM-dd}">
                                <HeaderStyle Width="12%" />
                            </asp:BoundField>
                            <asp:TemplateField HeaderText="會籍狀態">
                                <ItemTemplate>
                                    <span class='badge bg-<%# GetMembershipStatusBadgeColor(Eval("MembershipStatus").ToString(), Convert.ToBoolean(Eval("IsMembershipExpired"))) %>'>
                                        <%# GetMembershipStatusDisplayText(Eval("MembershipStatus").ToString(), Convert.ToBoolean(Eval("IsMembershipExpired"))) %>
                                    </span>
                                </ItemTemplate>
                                <HeaderStyle Width="10%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="剩餘天數">
                                <ItemTemplate>
                                    <%# FormatRemainingDays(Convert.ToInt32(Eval("DaysUntilExpiry"))) %>
                                </ItemTemplate>
                                <HeaderStyle Width="10%" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="操作">
                                <ItemTemplate>
                                    <div class="btn-group" role="group">
                                        <asp:LinkButton ID="btnView" runat="server" 
                                            CommandName="ViewProfile" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-info btn-sm" 
                                            ToolTip="查看檔案">
                                            <i class="fas fa-eye"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnRenew" runat="server" 
                                            CommandName="RenewMembership" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-success btn-sm" 
                                            ToolTip="續會" Visible='<%# Convert.ToBoolean(Eval("IsMembershipExpired")) || Convert.ToBoolean(Eval("IsMembershipExpiringSoon")) %>'>
                                            <i class="fas fa-refresh"></i>
                                        </asp:LinkButton>
                                        <asp:LinkButton ID="btnEdit" runat="server" 
                                            CommandName="EditMember" CommandArgument='<%# Eval("Id") %>'
                                            CssClass="btn btn-outline-warning btn-sm" 
                                            ToolTip="編輯">
                                            <i class="fas fa-edit"></i>
                                        </asp:LinkButton>
                                    </div>
                                </ItemTemplate>
                                <HeaderStyle Width="21%" />
                            </asp:TemplateField>
                        </Columns>
                        <HeaderStyle CssClass="table-dark" />
                        <EmptyDataRowStyle CssClass="text-center text-muted" />
                        <PagerStyle CssClass="pagination-wrapper" />
                    </asp:GridView>
                </div>
            </div>
        </div>

        <!-- 續會模態視窗 -->
        <div class="modal fade" id="renewModal" tabindex="-1" aria-labelledby="renewModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="renewModalLabel">會員續會</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <asp:HiddenField ID="hdnRenewMemberId" runat="server" />
                        
                        <div class="mb-3">
                            <label class="form-label">會員資料</label>
                            <div class="alert alert-info">
                                <strong>會員：</strong><asp:Label ID="lblRenewMemberName" runat="server" Text=""></asp:Label><br />
                                <strong>現到期日：</strong><asp:Label ID="lblRenewCurrentEndDate" runat="server" Text=""></asp:Label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="<%= ddlRenewMonths.ClientID %>" class="form-label">續會期間 <span class="text-danger">*</span></label>
                            <asp:DropDownList ID="ddlRenewMonths" runat="server" CssClass="form-select">
                                <asp:ListItem Text="請選擇" Value=""></asp:ListItem>
                                <asp:ListItem Text="3個月" Value="3"></asp:ListItem>
                                <asp:ListItem Text="6個月" Value="6"></asp:ListItem>
                                <asp:ListItem Text="12個月" Value="12" Selected="True"></asp:ListItem>
                                <asp:ListItem Text="24個月" Value="24"></asp:ListItem>
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvRenewMonths" runat="server" 
                                ControlToValidate="ddlRenewMonths" InitialValue="" ErrorMessage="請選擇續會期間"
                                CssClass="text-danger small" Display="Dynamic" ValidationGroup="RenewModal"></asp:RequiredFieldValidator>
                        </div>

                        <div class="mb-3">
                            <label for="<%= txtRenewFee.ClientID %>" class="form-label">續會費用 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">HK$</span>
                                <asp:TextBox ID="txtRenewFee" runat="server" CssClass="form-control" 
                                    TextMode="Number" step="0.01" min="0" placeholder="0.00"></asp:TextBox>
                            </div>
                            <asp:RequiredFieldValidator ID="rfvRenewFee" runat="server" 
                                ControlToValidate="txtRenewFee" ErrorMessage="請輸入續會費用"
                                CssClass="text-danger small" Display="Dynamic" ValidationGroup="RenewModal"></asp:RequiredFieldValidator>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">新到期日期</label>
                            <div class="form-control-plaintext" id="newEndDatePreview">請選擇續會期間</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <asp:Button ID="btnConfirmRenew" runat="server" Text="確認續會" CssClass="btn btn-success"
                            OnClick="btnConfirmRenew_Click" ValidationGroup="RenewModal" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-stats {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }
        
        .card-stats:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
        }
        
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .btn-group .btn {
            margin-right: 2px;
        }
        
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        
        @media print {
            .btn, .card-header, .pagination-wrapper {
                display: none !important;
            }
            
            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 處理模態視窗顯示
            window.showRenewModal = function() {
                $('#renewModal').modal('show');
            };

            // 計算新到期日期
            $('#<%= ddlRenewMonths.ClientID %>').change(function() {
                calculateNewEndDate();
            });

            function calculateNewEndDate() {
                var months = parseInt($('#<%= ddlRenewMonths.ClientID %>').val());
                var currentEndDate = $('#<%= lblRenewCurrentEndDate.ClientID %>').text();
                
                if (months && currentEndDate) {
                    try {
                        var currentDate = new Date(currentEndDate);
                        var newDate = new Date(currentDate);
                        newDate.setMonth(newDate.getMonth() + months);
                        
                        var formattedDate = newDate.getFullYear() + '-' + 
                            String(newDate.getMonth() + 1).padStart(2, '0') + '-' + 
                            String(newDate.getDate()).padStart(2, '0');
                        
                        $('#newEndDatePreview').text(formattedDate);
                    } catch (e) {
                        $('#newEndDatePreview').text('日期計算錯誤');
                    }
                } else {
                    $('#newEndDatePreview').text('請選擇續會期間');
                }
            }
        });

        // 顯示載入指示器
        function showLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }
    </script>
</asp:Content>