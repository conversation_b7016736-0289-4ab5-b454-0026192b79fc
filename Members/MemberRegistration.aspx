<%@ Page Title="會員註冊" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MemberRegistration.aspx.cs" Inherits="CWDECC_3S.Members.MemberRegistration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-user-plus text-primary me-2"></i>會員註冊
                </h2>
                <p class="text-muted">新增會員資料，系統將自動生成會員號碼與條碼</p>
            </div>
            <div>
                <asp:Button ID="btnBack" runat="server" Text="返回列表" CssClass="btn btn-outline-secondary"
                    OnClick="btnBack_Click" CausesValidation="false" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <div class="row">
            <!-- 左側：註冊表單 -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>會員資料
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- 基本資料 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtFullName.ClientID %>" class="form-label">
                                        姓名 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtFullName" runat="server" CssClass="form-control" 
                                        placeholder="請輸入姓名" MaxLength="100"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvFullName" runat="server" 
                                        ControlToValidate="txtFullName" ErrorMessage="請輸入姓名"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= ddlGender.ClientID %>" class="form-label">
                                        性別 <span class="text-danger">*</span>
                                    </label>
                                    <asp:DropDownList ID="ddlGender" runat="server" CssClass="form-select">
                                        <asp:ListItem Text="請選擇" Value=""></asp:ListItem>
                                        <asp:ListItem Text="男" Value="M"></asp:ListItem>
                                        <asp:ListItem Text="女" Value="F"></asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvGender" runat="server" 
                                        ControlToValidate="ddlGender" InitialValue="" ErrorMessage="請選擇性別"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtDateOfBirth.ClientID %>" class="form-label">
                                        出生日期 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtDateOfBirth" runat="server" CssClass="form-control" 
                                        TextMode="Date"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvDateOfBirth" runat="server" 
                                        ControlToValidate="txtDateOfBirth" ErrorMessage="請選擇出生日期"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                    <asp:CustomValidator ID="cvDateOfBirth" runat="server" 
                                        ControlToValidate="txtDateOfBirth" ErrorMessage="年齡必須滿16歲"
                                        CssClass="text-danger small" Display="Dynamic"
                                        OnServerValidate="cvDateOfBirth_ServerValidate"></asp:CustomValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtHKID.ClientID %>" class="form-label">
                                        身份證號碼
                                    </label>
                                    <asp:TextBox ID="txtHKID" runat="server" CssClass="form-control" 
                                        placeholder="例如：A123456(7)" MaxLength="20"></asp:TextBox>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtPhone.ClientID %>" class="form-label">
                                        電話號碼 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtPhone" runat="server" CssClass="form-control" 
                                        placeholder="請輸入電話號碼" MaxLength="20"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvPhone" runat="server" 
                                        ControlToValidate="txtPhone" ErrorMessage="請輸入電話號碼"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                    <asp:RegularExpressionValidator ID="revPhone" runat="server" 
                                        ControlToValidate="txtPhone" ErrorMessage="電話號碼格式不正確"
                                        ValidationExpression="^[0-9\s\-\+\(\)]{6,20}$"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtEmail.ClientID %>" class="form-label">
                                        電子郵件
                                    </label>
                                    <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" 
                                        TextMode="Email" placeholder="請輸入電子郵件" MaxLength="100"></asp:TextBox>
                                    <asp:RegularExpressionValidator ID="revEmail" runat="server" 
                                        ControlToValidate="txtEmail" ErrorMessage="電子郵件格式不正確"
                                        ValidationExpression="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RegularExpressionValidator>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="<%= txtAddress.ClientID %>" class="form-label">
                                        住址 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" 
                                        TextMode="MultiLine" Rows="3" placeholder="請輸入完整地址" MaxLength="500"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvAddress" runat="server" 
                                        ControlToValidate="txtAddress" ErrorMessage="請輸入住址"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <!-- 會籍資料 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= ddlMemberType.ClientID %>" class="form-label">
                                        會員類型 <span class="text-danger">*</span>
                                    </label>
                                    <asp:DropDownList ID="ddlMemberType" runat="server" CssClass="form-select"
                                        OnSelectedIndexChanged="ddlMemberType_SelectedIndexChanged" AutoPostBack="true">
                                        <asp:ListItem Text="請選擇" Value=""></asp:ListItem>
                                        <asp:ListItem Text="一般會員" Value="Regular"></asp:ListItem>
                                        <asp:ListItem Text="學生會員" Value="Student"></asp:ListItem>
                                        <asp:ListItem Text="長者會員" Value="Senior"></asp:ListItem>
                                        <asp:ListItem Text="家庭會員" Value="Family"></asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvMemberType" runat="server" 
                                        ControlToValidate="ddlMemberType" InitialValue="" ErrorMessage="請選擇會員類型"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtJoinDate.ClientID %>" class="form-label">
                                        入會日期 <span class="text-danger">*</span>
                                    </label>
                                    <asp:TextBox ID="txtJoinDate" runat="server" CssClass="form-control" 
                                        TextMode="Date"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvJoinDate" runat="server" 
                                        ControlToValidate="txtJoinDate" ErrorMessage="請選擇入會日期"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtMembershipFee.ClientID %>" class="form-label">
                                        年費 <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">HK$</span>
                                        <asp:TextBox ID="txtMembershipFee" runat="server" CssClass="form-control" 
                                            TextMode="Number" step="0.01" min="0" placeholder="0.00"></asp:TextBox>
                                    </div>
                                    <asp:RequiredFieldValidator ID="rfvMembershipFee" runat="server" 
                                        ControlToValidate="txtMembershipFee" ErrorMessage="請輸入年費"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                    <asp:RangeValidator ID="rvMembershipFee" runat="server" 
                                        ControlToValidate="txtMembershipFee" Type="Currency" MinimumValue="0" MaximumValue="10000"
                                        ErrorMessage="年費必須在 0-10000 之間" CssClass="text-danger small" Display="Dynamic"></asp:RangeValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= ddlPaymentMethod.ClientID %>" class="form-label">
                                        付款方式 <span class="text-danger">*</span>
                                    </label>
                                    <asp:DropDownList ID="ddlPaymentMethod" runat="server" CssClass="form-select">
                                        <asp:ListItem Text="請選擇" Value=""></asp:ListItem>
                                        <asp:ListItem Text="現金" Value="Cash"></asp:ListItem>
                                        <asp:ListItem Text="八達通" Value="Octopus"></asp:ListItem>
                                        <asp:ListItem Text="信用卡" Value="CreditCard"></asp:ListItem>
                                        <asp:ListItem Text="轉賬" Value="BankTransfer"></asp:ListItem>
                                        <asp:ListItem Text="支票" Value="Cheque"></asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvPaymentMethod" runat="server" 
                                        ControlToValidate="ddlPaymentMethod" InitialValue="" ErrorMessage="請選擇付款方式"
                                        CssClass="text-danger small" Display="Dynamic"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <!-- 緊急聯絡人 -->
                            <div class="col-12">
                                <h6 class="mt-3 mb-3 text-primary">
                                    <i class="fas fa-phone-alt me-2"></i>緊急聯絡人資料
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtEmergencyContact.ClientID %>" class="form-label">
                                        緊急聯絡人姓名
                                    </label>
                                    <asp:TextBox ID="txtEmergencyContact" runat="server" CssClass="form-control" 
                                        placeholder="請輸入緊急聯絡人姓名" MaxLength="100"></asp:TextBox>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= txtEmergencyPhone.ClientID %>" class="form-label">
                                        緊急聯絡電話
                                    </label>
                                    <asp:TextBox ID="txtEmergencyPhone" runat="server" CssClass="form-control" 
                                        placeholder="請輸入緊急聯絡電話" MaxLength="20"></asp:TextBox>
                                </div>
                            </div>

                            <!-- 照片上傳 -->
                            <div class="col-12">
                                <h6 class="mt-3 mb-3 text-primary">
                                    <i class="fas fa-camera me-2"></i>會員照片
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="<%= fuPhoto.ClientID %>" class="form-label">
                                        上傳照片
                                    </label>
                                    <asp:FileUpload ID="fuPhoto" runat="server" CssClass="form-control" 
                                        accept=".jpg,.jpeg,.png,.gif" />
                                    <div class="form-text">
                                        支援格式：JPG, PNG, GIF | 檔案大小限制：5MB
                                    </div>
                                    <asp:CustomValidator ID="cvPhoto" runat="server" 
                                        ErrorMessage="照片格式或大小不符合要求" CssClass="text-danger small" Display="Dynamic"
                                        OnServerValidate="cvPhoto_ServerValidate"></asp:CustomValidator>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">照片預覽</label>
                                    <div class="photo-preview-container">
                                        <asp:Image ID="imgPhotoPreview" runat="server" CssClass="photo-preview d-none" />
                                        <div id="photoPlaceholder" class="photo-placeholder">
                                            <i class="fas fa-user-circle fa-3x text-muted"></i>
                                            <p class="text-muted mt-2">尚未選擇照片</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 備註 -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="<%= txtRemarks.ClientID %>" class="form-label">
                                        備註
                                    </label>
                                    <asp:TextBox ID="txtRemarks" runat="server" CssClass="form-control" 
                                        TextMode="MultiLine" Rows="3" placeholder="請輸入備註資訊（選填）" MaxLength="1000"></asp:TextBox>
                                </div>
                            </div>
                        </div>

                        <!-- 按鈕區域 -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <asp:Button ID="btnClear" runat="server" Text="清除表單" CssClass="btn btn-outline-secondary"
                                OnClick="btnClear_Click" CausesValidation="false" />
                            <asp:Button ID="btnPreview" runat="server" Text="預覽資料" CssClass="btn btn-info"
                                OnClick="btnPreview_Click" />
                            <asp:Button ID="btnSave" runat="server" Text="確認註冊" CssClass="btn btn-success"
                                OnClick="btnSave_Click" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：預覽與說明 -->
            <div class="col-lg-4">
                <!-- 會員號碼預覽 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-id-card me-2"></i>會員號碼預覽
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="member-number-preview">
                            <h4 class="text-primary mb-2">
                                <asp:Label ID="lblPreviewMemberNumber" runat="server" Text="CWD2024000001"></asp:Label>
                            </h4>
                            <small class="text-muted">
                                系統將自動生成唯一會員號碼<br/>
                                格式：CWD + 年度 + 6位序號
                            </small>
                        </div>
                        
                        <!-- 條碼預覽 -->
                        <div class="barcode-preview mt-4">
                            <asp:Image ID="imgBarcodePreview" runat="server" CssClass="img-fluid d-none" />
                            <div id="barcodePlaceholder" class="p-3 border border-dashed rounded">
                                <i class="fas fa-barcode fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">註冊後生成條碼</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 年費說明 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>會員類型與年費
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="fee-info">
                            <div class="d-flex justify-content-between py-2 border-bottom">
                                <span>一般會員</span>
                                <strong>HK$200</strong>
                            </div>
                            <div class="d-flex justify-content-between py-2 border-bottom">
                                <span>學生會員</span>
                                <strong>HK$100</strong>
                            </div>
                            <div class="d-flex justify-content-between py-2 border-bottom">
                                <span>長者會員</span>
                                <strong>HK$120</strong>
                            </div>
                            <div class="d-flex justify-content-between py-2">
                                <span>家庭會員</span>
                                <strong>HK$350</strong>
                            </div>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            * 會籍有效期為一年<br/>
                            * 選擇會員類型後將自動填入建議年費
                        </small>
                    </div>
                </div>

                <!-- 注意事項 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>注意事項
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                所有標示 <span class="text-danger">*</span> 的欄位均為必填
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                會員須年滿16歲方可申請
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                照片檔案限制5MB以內
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                敏感資料將使用AES-256加密保護
                            </li>
                            <li>
                                <i class="fas fa-check text-success me-2"></i>
                                註冊完成後將生成會員卡及條碼
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 預覽模態視窗 -->
    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">
                        <i class="fas fa-eye me-2"></i>會員資料預覽
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold" width="30%">會員號碼：</td>
                                    <td><asp:Label ID="lblPreviewNumber" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">姓名：</td>
                                    <td><asp:Label ID="lblPreviewName" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">性別：</td>
                                    <td><asp:Label ID="lblPreviewGender" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">出生日期：</td>
                                    <td><asp:Label ID="lblPreviewDOB" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">電話：</td>
                                    <td><asp:Label ID="lblPreviewPhone" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">地址：</td>
                                    <td><asp:Label ID="lblPreviewAddress" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">會員類型：</td>
                                    <td><asp:Label ID="lblPreviewType" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">年費：</td>
                                    <td><asp:Label ID="lblPreviewFee" runat="server"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">付款方式：</td>
                                    <td><asp:Label ID="lblPreviewPayment" runat="server"></asp:Label></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4 text-center">
                            <asp:Image ID="imgPreviewPhoto" runat="server" CssClass="img-fluid rounded" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">修改資料</button>
                    <asp:Button ID="btnConfirmSave" runat="server" Text="確認註冊" CssClass="btn btn-success"
                        OnClick="btnSave_Click" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .photo-preview-container {
            position: relative;
            min-height: 200px;
        }
        
        .photo-preview {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .photo-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .member-number-preview {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        
        .barcode-preview img {
            max-width: 100%;
            height: auto;
        }
        
        .fee-info {
            font-size: 0.9rem;
        }
        
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        @media (max-width: 768px) {
            .photo-preview-container {
                min-height: 150px;
            }
            
            .photo-placeholder {
                height: 150px;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 照片上傳預覽
            $('#<%= fuPhoto.ClientID %>').change(function(e) {
                var file = e.target.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#<%= imgPhotoPreview.ClientID %>').attr('src', e.target.result);
                        $('#<%= imgPhotoPreview.ClientID %>').removeClass('d-none');
                        $('#photoPlaceholder').hide();
                    }
                    reader.readAsDataURL(file);
                } else {
                    $('#<%= imgPhotoPreview.ClientID %>').addClass('d-none');
                    $('#photoPlaceholder').show();
                }
            });

            // 會員類型變更時自動填入建議年費
            $('#<%= ddlMemberType.ClientID %>').change(function() {
                var memberType = $(this).val();
                var feeInput = $('#<%= txtMembershipFee.ClientID %>');
                
                switch(memberType) {
                    case 'Regular':
                        feeInput.val('200');
                        break;
                    case 'Student':
                        feeInput.val('100');
                        break;
                    case 'Senior':
                        feeInput.val('120');
                        break;
                    case 'Family':
                        feeInput.val('350');
                        break;
                    default:
                        feeInput.val('');
                        break;
                }
            });

            // 預覽模態視窗
            window.showPreviewModal = function() {
                $('#previewModal').modal('show');
            };
        });

        // 顯示載入指示器
        function showLoading(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: message || '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }

        // 表單提交前顯示載入
        function validateAndSubmit() {
            if (Page_ClientValidate()) {
                showLoading('正在註冊會員...');
                return true;
            }
            return false;
        }
    </script>
</asp:Content>