using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Models;
using CWDECC_3S.Services;
using ZXing;
using ZXing.Common;

namespace CWDECC_3S.Members
{
    public partial class MemberRegistration : System.Web.UI.Page
    {
        private readonly MemberService _memberService;
        private readonly AuditService _auditService;
        private readonly PermissionService _permissionService;
        private readonly string _currentUserId;
        private readonly string _nasBasePath;

        public MemberRegistration()
        {
            _memberService = new MemberService();
            _auditService = new AuditService();
            _permissionService = new PermissionService();
            _currentUserId = HttpContext.Current?.Session["UserId"]?.ToString() ?? "system";
            _nasBasePath = ConfigurationManager.AppSettings["NASBasePath"] ?? @"\\NAS\DECC_Files\Members";
        }

        protected async void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限 - 只有前台職員、社工和管理員角色可以新增會員
                if (!_permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff") &&
                    !_permissionService.HasRolePermission(_currentUserId, "SocialWorker") &&
                    !_permissionService.HasRolePermission(_currentUserId, "Administrator"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    await InitializePageAsync();
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "MemberRegistration", _currentUserId,
                    $"頁面載入錯誤: {ex.Message}", null, "ERROR");
                ShowMessage($"系統錯誤：{ex.Message}", "error");
            }
        }

        private async Task InitializePageAsync()
        {
            try
            {
                // 設定預設值
                txtJoinDate.Text = DateTime.Now.ToString("yyyy-MM-dd");
                
                // 生成預覽會員號碼
                var nextMemberNumber = await GenerateNextMemberNumberAsync();
                lblPreviewMemberNumber.Text = nextMemberNumber;
                
                // 記錄頁面訪問
                await _auditService.LogAsync("PAGE_ACCESS", "MemberRegistration", _currentUserId,
                    "訪問會員註冊頁面", null, "SUCCESS");
            }
            catch (Exception ex)
            {
                ShowMessage($"初始化錯誤：{ex.Message}", "error");
            }
        }

        private async Task<string> GenerateNextMemberNumberAsync()
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var yearPrefix = $"CWD{currentYear}";
                
                // 獲取當年度最大序號
                var maxNumber = await _memberService.GetMaxMemberNumberByYearAsync(currentYear);
                var nextSequence = maxNumber + 1;
                
                return $"{yearPrefix}{nextSequence:D6}";
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "MemberRegistration", _currentUserId,
                    $"生成會員號碼錯誤: {ex.Message}", null, "ERROR");
                return $"CWD{DateTime.Now.Year}000001";
            }
        }

        protected async void ddlMemberType_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                // 根據會員類型自動填入建議年費
                decimal suggestedFee = 0;
                switch (ddlMemberType.SelectedValue)
                {
                    case "Regular":
                        suggestedFee = 200;
                        break;
                    case "Student":
                        suggestedFee = 100;
                        break;
                    case "Senior":
                        suggestedFee = 120;
                        break;
                    case "Family":
                        suggestedFee = 350;
                        break;
                }
                
                if (suggestedFee > 0)
                {
                    txtMembershipFee.Text = suggestedFee.ToString("F2");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"設定年費錯誤：{ex.Message}", "warning");
            }
        }

        protected void cvDateOfBirth_ServerValidate(object source, ServerValidateEventArgs args)
        {
            try
            {
                if (DateTime.TryParse(args.Value, out DateTime birthDate))
                {
                    var age = DateTime.Now.Year - birthDate.Year;
                    if (birthDate > DateTime.Now.AddYears(-age)) age--;
                    
                    args.IsValid = age >= 16;
                }
                else
                {
                    args.IsValid = false;
                }
            }
            catch
            {
                args.IsValid = false;
            }
        }

        protected void cvPhoto_ServerValidate(object source, ServerValidateEventArgs args)
        {
            try
            {
                args.IsValid = ValidatePhotoUpload();
            }
            catch
            {
                args.IsValid = false;
            }
        }

        private bool ValidatePhotoUpload()
        {
            if (!fuPhoto.HasFile)
            {
                return true; // 照片為選填，沒有上傳也算有效
            }

            try
            {
                var file = fuPhoto.PostedFile;
                
                // 檢查檔案大小 (5MB限制)
                if (file.ContentLength > 5 * 1024 * 1024)
                {
                    return false;
                }
                
                // 檢查檔案格式
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return false;
                }
                
                // 檢查檔案內容類型
                var allowedContentTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
                if (!allowedContentTypes.Contains(file.ContentType.ToLower()))
                {
                    return false;
                }
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        protected async void btnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                if (Page.IsValid)
                {
                    // 填充預覽資料
                    await PopulatePreviewDataAsync();
                    
                    // 顯示預覽模態視窗
                    ScriptManager.RegisterStartupScript(this, GetType(), "showPreview", "showPreviewModal();", true);
                    
                    // 記錄預覽操作
                    await _auditService.LogAsync("MEMBER_PREVIEW", "MemberRegistration", _currentUserId,
                        $"預覽會員資料: {txtFullName.Text}", null, "SUCCESS");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"預覽錯誤：{ex.Message}", "error");
            }
        }

        private async Task PopulatePreviewDataAsync()
        {
            try
            {
                var memberNumber = await GenerateNextMemberNumberAsync();
                
                lblPreviewNumber.Text = memberNumber;
                lblPreviewName.Text = txtFullName.Text;
                lblPreviewGender.Text = ddlGender.SelectedItem?.Text ?? "";
                lblPreviewDOB.Text = string.IsNullOrEmpty(txtDateOfBirth.Text) ? "" : 
                    DateTime.Parse(txtDateOfBirth.Text).ToString("yyyy年MM月dd日");
                lblPreviewPhone.Text = txtPhone.Text;
                lblPreviewAddress.Text = txtAddress.Text;
                lblPreviewType.Text = ddlMemberType.SelectedItem?.Text ?? "";
                lblPreviewFee.Text = string.IsNullOrEmpty(txtMembershipFee.Text) ? "" : 
                    $"HK${decimal.Parse(txtMembershipFee.Text):F2}";
                lblPreviewPayment.Text = ddlPaymentMethod.SelectedItem?.Text ?? "";
                
                // 如果有上傳照片，顯示預覽
                if (fuPhoto.HasFile)
                {
                    var photoBytes = fuPhoto.FileBytes;
                    var base64String = Convert.ToBase64String(photoBytes);
                    imgPreviewPhoto.ImageUrl = $"data:image/jpeg;base64,{base64String}";
                    imgPreviewPhoto.Visible = true;
                }
                else
                {
                    imgPreviewPhoto.Visible = false;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"填充預覽資料失敗：{ex.Message}");
            }
        }

        protected async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsValid)
                {
                    ShowMessage("請檢查表單資料是否正確填寫", "warning");
                    return;
                }

                // 建立新會員物件
                var member = await CreateMemberFromFormAsync();
                
                // 儲存會員資料
                await _memberService.CreateMemberAsync(member);
                
                // 處理照片上傳
                if (fuPhoto.HasFile)
                {
                    var photoPath = await SaveMemberPhotoAsync(member.MemberNumber);
                    if (!string.IsNullOrEmpty(photoPath))
                    {
                        member.PhotoPath = photoPath;
                        await _memberService.UpdateMemberAsync(member);
                    }
                }
                
                // 生成條碼
                var barcodePath = await GenerateMemberBarcodeAsync(member.MemberNumber);
                if (!string.IsNullOrEmpty(barcodePath))
                {
                    member.BarcodePath = barcodePath;
                    await _memberService.UpdateMemberAsync(member);
                }
                
                // 記錄審計日誌
                await _auditService.LogAsync("MEMBER_CREATE", "MemberRegistration", _currentUserId,
                    $"新增會員: {member.FullName} (會員號: {member.MemberNumber})", member.Id.ToString(), "SUCCESS");
                
                ShowMessage($"會員註冊成功！會員號碼：{member.MemberNumber}", "success");
                
                // 清除表單
                ClearForm();
                
                // 重新生成預覽會員號碼
                var nextNumber = await GenerateNextMemberNumberAsync();
                lblPreviewMemberNumber.Text = nextNumber;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "MemberRegistration", _currentUserId,
                    $"新增會員錯誤: {ex.Message}", null, "ERROR");
                ShowMessage($"註冊失敗：{ex.Message}", "error");
            }
        }

        private async Task<Member> CreateMemberFromFormAsync()
        {
            try
            {
                var memberNumber = await GenerateNextMemberNumberAsync();
                var joinDate = DateTime.Parse(txtJoinDate.Text);
                var membershipFee = decimal.Parse(txtMembershipFee.Text);
                
                var member = new Member
                {
                    MemberNumber = memberNumber,
                    FullName = txtFullName.Text.Trim(),
                    Gender = ddlGender.SelectedValue,
                    DateOfBirth = DateTime.Parse(txtDateOfBirth.Text),
                    HKID = string.IsNullOrEmpty(txtHKID.Text) ? null : txtHKID.Text.Trim(),
                    Phone = txtPhone.Text.Trim(), // 將自動加密
                    Email = string.IsNullOrEmpty(txtEmail.Text) ? null : txtEmail.Text.Trim(), // 將自動加密
                    Address = txtAddress.Text.Trim(), // 將自動加密
                    MemberType = ddlMemberType.SelectedValue,
                    JoinDate = joinDate,
                    MembershipStartDate = joinDate,
                    MembershipEndDate = joinDate.AddYears(1),
                    MembershipFee = membershipFee,
                    PaymentMethod = ddlPaymentMethod.SelectedValue,
                    EmergencyContactName = string.IsNullOrEmpty(txtEmergencyContact.Text) ? null : txtEmergencyContact.Text.Trim(),
                    EmergencyContactPhone = string.IsNullOrEmpty(txtEmergencyPhone.Text) ? null : txtEmergencyPhone.Text.Trim(),
                    Remarks = string.IsNullOrEmpty(txtRemarks.Text) ? null : txtRemarks.Text.Trim(),
                    Status = "Active",
                    IsActive = true,
                    CreatedBy = _currentUserId,
                    CreatedDate = DateTime.Now,
                    UpdatedBy = _currentUserId,
                    UpdatedDate = DateTime.Now
                };
                
                return member;
            }
            catch (Exception ex)
            {
                throw new Exception($"建立會員物件失敗：{ex.Message}");
            }
        }

        private async Task<string> SaveMemberPhotoAsync(string memberNumber)
        {
            try
            {
                if (!fuPhoto.HasFile) return null;
                
                var file = fuPhoto.PostedFile;
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = $"{memberNumber}_photo{fileExtension}";
                
                // 建立年度目錄
                var yearFolder = DateTime.Now.Year.ToString();
                var directoryPath = Path.Combine(_nasBasePath, yearFolder);
                
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }
                
                var filePath = Path.Combine(directoryPath, fileName);
                
                // 儲存檔案
                file.SaveAs(filePath);
                
                // 返回相對路徑供資料庫儲存
                return Path.Combine(yearFolder, fileName);
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "MemberRegistration", _currentUserId,
                    $"儲存會員照片錯誤: {ex.Message}", null, "ERROR");
                throw new Exception($"照片儲存失敗：{ex.Message}");
            }
        }

        private async Task<string> GenerateMemberBarcodeAsync(string memberNumber)
        {
            try
            {
                // 使用 ZXing 生成 Code 128 條碼
                var writer = new BarcodeWriter
                {
                    Format = BarcodeFormat.CODE_128,
                    Options = new EncodingOptions
                    {
                        Width = 300,
                        Height = 100,
                        Margin = 10
                    }
                };
                
                var barcodeBitmap = writer.Write(memberNumber);
                
                // 建立年度目錄
                var yearFolder = DateTime.Now.Year.ToString();
                var directoryPath = Path.Combine(_nasBasePath, yearFolder, "Barcodes");
                
                if (!Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }
                
                var fileName = $"{memberNumber}_barcode.png";
                var filePath = Path.Combine(directoryPath, fileName);
                
                // 儲存條碼圖片
                barcodeBitmap.Save(filePath, ImageFormat.Png);
                
                // 返回相對路徑供資料庫儲存
                return Path.Combine(yearFolder, "Barcodes", fileName);
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "MemberRegistration", _currentUserId,
                    $"生成會員條碼錯誤: {ex.Message}", null, "ERROR");
                throw new Exception($"條碼生成失敗：{ex.Message}");
            }
        }

        protected void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        protected void btnBack_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Members/MemberSearch.aspx");
        }

        private void ClearForm()
        {
            try
            {
                // 清除所有輸入欄位
                txtFullName.Text = "";
                ddlGender.SelectedIndex = 0;
                txtDateOfBirth.Text = "";
                txtHKID.Text = "";
                txtPhone.Text = "";
                txtEmail.Text = "";
                txtAddress.Text = "";
                ddlMemberType.SelectedIndex = 0;
                txtJoinDate.Text = DateTime.Now.ToString("yyyy-MM-dd");
                txtMembershipFee.Text = "";
                ddlPaymentMethod.SelectedIndex = 0;
                txtEmergencyContact.Text = "";
                txtEmergencyPhone.Text = "";
                txtRemarks.Text = "";
                
                // 清除照片預覽
                imgPhotoPreview.ImageUrl = "";
                imgPhotoPreview.CssClass = "photo-preview d-none";
                
                // 清除預覽資料
                lblPreviewNumber.Text = "";
                lblPreviewName.Text = "";
                lblPreviewGender.Text = "";
                lblPreviewDOB.Text = "";
                lblPreviewPhone.Text = "";
                lblPreviewAddress.Text = "";
                lblPreviewType.Text = "";
                lblPreviewFee.Text = "";
                lblPreviewPayment.Text = "";
                imgPreviewPhoto.ImageUrl = "";
                
                // 重新顯示照片佔位符的JavaScript
                ScriptManager.RegisterStartupScript(this, GetType(), "resetPhoto", 
                    "$('#photoPlaceholder').show(); $('#<%= imgPhotoPreview.ClientID %>').addClass('d-none');", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"清除表單錯誤：{ex.Message}", "warning");
            }
        }

        private void ShowMessage(string message, string type)
        {
            try
            {
                pnlMessage.Visible = true;
                ltlMessage.Text = message;
                
                // 設定訊息樣式
                pnlMessage.CssClass = type switch
                {
                    "success" => "alert alert-success alert-dismissible fade show",
                    "error" => "alert alert-danger alert-dismissible fade show",
                    "warning" => "alert alert-warning alert-dismissible fade show",
                    "info" => "alert alert-info alert-dismissible fade show",
                    _ => "alert alert-info alert-dismissible fade show"
                };
                
                // 自動隱藏成功訊息
                if (type == "success")
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideSuccess",
                        "setTimeout(function() { $('.alert-success').fadeOut(); }, 5000);", true);
                }
            }
            catch
            {
                // 如果顯示訊息失敗，至少記錄到瀏覽器控制台
                ScriptManager.RegisterStartupScript(this, GetType(), "consoleLog",
                    $"console.log('訊息: {message}');", true);
            }
        }
    }
}