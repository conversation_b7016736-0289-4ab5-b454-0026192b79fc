using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Models;
using CWDECC_3S.Services;

namespace CWDECC_3S.Meals
{
    public partial class MealOrdering : System.Web.UI.Page
    {
        private readonly MealService _mealService;
        private readonly AuditService _auditService;
        private readonly PermissionService _permissionService;
        private readonly string _currentUserId;

        public MealOrdering()
        {
            _mealService = new MealService();
            _auditService = new AuditService();
            _permissionService = new PermissionService();
            _currentUserId = HttpContext.Current?.Session["UserId"]?.ToString() ?? "system";
        }

        protected async void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 檢查用戶權限 - 管理員、社工、前台職員可以為會員訂餐
                if (!_permissionService.HasRolePermission(_currentUserId, "Administrator") &&
                    !_permissionService.HasRolePermission(_currentUserId, "SocialWorker") &&
                    !_permissionService.HasRolePermission(_currentUserId, "FrontDeskStaff"))
                {
                    Response.Redirect("~/Unauthorized.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    await InitializePageAsync();
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ERROR", "MealOrdering", _currentUserId,
                    $"頁面載入錯誤: {ex.Message}", null, "ERROR");
                ShowMessage($"系統錯誤：{ex.Message}", "error");
            }
        }

        private async Task InitializePageAsync()
        {
            try
            {
                // 初始化餐點類別
                await LoadMealCategoriesAsync();

                // 載入所有可用餐點
                await LoadMealItemsAsync();

                // 設定預設日期為今天
                txtSingleDate.Text = DateTime.Today.ToString("yyyy-MM-dd");

                // 記錄頁面訪問
                await _auditService.LogAsync("PAGE_ACCESS", "MealOrdering", _currentUserId,
                    "訪問膳食訂購頁面", null, "SUCCESS");
            }
            catch (Exception ex)
            {
                ShowMessage($"初始化錯誤：{ex.Message}", "error");
            }
        }

        private async Task LoadMealCategoriesAsync()
        {
            try
            {
                ddlMealCategory.Items.Clear();
                ddlMealCategory.Items.Add(new ListItem("全部類別", ""));

                foreach (var category in MealConstants.Category.GetAll())
                {
                    ddlMealCategory.Items.Add(new ListItem(category.Value, category.Key));
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入餐點類別錯誤：{ex.Message}", "warning");
            }
        }

        private async Task LoadMealItemsAsync(string category = null)
        {
            try
            {
                var mealItems = await _mealService.GetAvailableMealItemsAsync(category);

                ddlMealItems.Items.Clear();
                ddlMealItems.Items.Add(new ListItem("請選擇餐點", ""));

                foreach (var item in mealItems)
                {
                    ddlMealItems.Items.Add(new ListItem(item.DisplayName, item.Id.ToString()));
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入餐點列表錯誤：{ex.Message}", "warning");
            }
        }

        #region 會員搜尋

        protected async void btnSearchMember_Click(object sender, EventArgs e)
        {
            await SearchMembersAsync();
        }

        protected async void txtMemberSearch_TextChanged(object sender, EventArgs e)
        {
            if (txtMemberSearch.Text.Length >= 2)
            {
                await SearchMembersAsync();
            }
        }

        private async Task SearchMembersAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtMemberSearch.Text))
                {
                    ddlMembers.Items.Clear();
                    ddlMembers.Items.Add(new ListItem("請先搜尋會員", ""));
                    pnlMemberInfo.Visible = false;
                    return;
                }

                var members = await _mealService.SearchMembersAsync(txtMemberSearch.Text);

                ddlMembers.Items.Clear();
                ddlMembers.Items.Add(new ListItem("請選擇會員", ""));

                foreach (var member in members)
                {
                    var displayText = $"{member.FullName} ({member.MemberNumber})";
                    ddlMembers.Items.Add(new ListItem(displayText, member.Id.ToString()));
                }

                if (members.Count == 0)
                {
                    ShowMessage("找不到符合條件的會員", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"搜尋會員錯誤：{ex.Message}", "error");
            }
        }

        protected async void ddlMembers_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadSelectedMemberInfoAsync();
            await UpdateOrderSummaryAsync();
        }

        private async Task LoadSelectedMemberInfoAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(ddlMembers.SelectedValue))
                {
                    pnlMemberInfo.Visible = false;
                    return;
                }

                var memberId = int.Parse(ddlMembers.SelectedValue);
                var member = await _mealService.GetMemberByIdAsync(memberId);

                if (member != null)
                {
                    lblMemberName.Text = member.FullName;
                    lblMemberNumber.Text = member.MemberNumber;
                    lblMemberType.Text = member.MemberTypeDisplayText;
                    lblMemberStatus.Text = member.MembershipStatusDisplayText;
                    pnlMemberInfo.Visible = true;
                }
                else
                {
                    pnlMemberInfo.Visible = false;
                    ShowMessage("無法載入會員資訊", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入會員資訊錯誤：{ex.Message}", "error");
                pnlMemberInfo.Visible = false;
            }
        }

        #endregion

        #region 餐點選擇

        protected async void ddlMealCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadMealItemsAsync(ddlMealCategory.SelectedValue);
            pnlMealDetails.Visible = false;
            await UpdateOrderSummaryAsync();
        }

        protected async void ddlMealItems_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadSelectedMealDetailsAsync();
            await UpdateOrderSummaryAsync();
        }

        private async Task LoadSelectedMealDetailsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(ddlMealItems.SelectedValue))
                {
                    pnlMealDetails.Visible = false;
                    return;
                }

                var mealItemId = int.Parse(ddlMealItems.SelectedValue);
                var mealItem = await _mealService.GetMealItemByIdAsync(mealItemId);

                if (mealItem != null)
                {
                    lblMealName.Text = mealItem.ItemName;
                    lblMealDescription.Text = mealItem.Description ?? "無描述";
                    lblMealCategory.Text = mealItem.CategoryDisplayText;
                    lblMealPrice.Text = mealItem.PriceDisplay;

                    // 過敏原資訊
                    if (!string.IsNullOrEmpty(mealItem.AllergenInfo))
                    {
                        lblAllergenInfo.Text = mealItem.AllergenInfo;
                        pnlAllergenInfo.Visible = true;
                    }
                    else
                    {
                        pnlAllergenInfo.Visible = false;
                    }

                    // 每日限量
                    if (mealItem.DailyLimit.HasValue)
                    {
                        lblDailyLimit.Text = mealItem.DailyLimit.Value.ToString();
                        pnlDailyLimit.Visible = true;
                    }
                    else
                    {
                        pnlDailyLimit.Visible = false;
                    }

                    pnlMealDetails.Visible = true;
                }
                else
                {
                    pnlMealDetails.Visible = false;
                    ShowMessage("無法載入餐點資訊", "warning");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入餐點資訊錯誤：{ex.Message}", "error");
                pnlMealDetails.Visible = false;
            }
        }

        #endregion

        #region 日期選擇

        protected void rbDateMode_CheckedChanged(object sender, EventArgs e)
        {
            pnlCustomDates.Visible = rbCustomDates.Checked;
            UpdateDatePreview();
        }

        protected void btnGenerateDates_Click(object sender, EventArgs e)
        {
            UpdateDatePreview();
        }

        protected void txtQuantity_TextChanged(object sender, EventArgs e)
        {
            UpdateDatePreview();
            UpdateOrderSummary();
        }

        private void UpdateDatePreview()
        {
            try
            {
                var selectedDates = GetSelectedDates();
                if (selectedDates.Any())
                {
                    var dateStrings = selectedDates.Select(d => $"<span class='date-badge'>{d:MM/dd}</span>");
                    ltlDatePreview.Text = string.Join(" ", dateStrings);
                    lblTotalDays.Text = selectedDates.Count.ToString();

                    // 計算預估費用
                    if (!string.IsNullOrEmpty(ddlMealItems.SelectedValue))
                    {
                        var mealItemId = int.Parse(ddlMealItems.SelectedValue);
                        var quantity = int.TryParse(txtQuantity.Text, out int q) ? q : 1;
                        // 這裡需要異步獲取餐點價格，簡化處理
                        lblEstimatedCost.Text = "計算中...";
                    }

                    pnlDatePreview.Visible = true;
                }
                else
                {
                    pnlDatePreview.Visible = false;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"更新日期預覽錯誤：{ex.Message}", "warning");
            }
        }

        private List<DateTime> GetSelectedDates()
        {
            var dates = new List<DateTime>();

            try
            {
                if (rbSingleDate.Checked && !string.IsNullOrEmpty(txtSingleDate.Text))
                {
                    if (DateTime.TryParse(txtSingleDate.Text, out DateTime singleDate))
                    {
                        dates.Add(singleDate);
                    }
                }
                else if (rbDateRange.Checked && !string.IsNullOrEmpty(txtDateFrom.Text) && !string.IsNullOrEmpty(txtDateTo.Text))
                {
                    if (DateTime.TryParse(txtDateFrom.Text, out DateTime fromDate) &&
                        DateTime.TryParse(txtDateTo.Text, out DateTime toDate))
                    {
                        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
                        {
                            dates.Add(date);
                        }
                    }
                }
                else if (rbCustomDates.Checked && !string.IsNullOrEmpty(txtCustomDates.Text))
                {
                    var lines = txtCustomDates.Text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var line in lines)
                    {
                        if (DateTime.TryParse(line.Trim(), out DateTime customDate))
                        {
                            dates.Add(customDate);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"解析日期錯誤：{ex.Message}", "warning");
            }

            return dates.Where(d => d >= DateTime.Today).OrderBy(d => d).ToList();
        }

        #endregion

        #region 快速日期選擇

        protected void btnQuickWeek_Click(object sender, EventArgs e)
        {
            SetWeekdayDates(DateTime.Today, DateTime.Today.AddDays(6));
        }

        protected void btnQuickNextWeek_Click(object sender, EventArgs e)
        {
            var nextMonday = DateTime.Today.AddDays(7 - (int)DateTime.Today.DayOfWeek + 1);
            SetWeekdayDates(nextMonday, nextMonday.AddDays(4));
        }

        protected void btnQuickMonth_Click(object sender, EventArgs e)
        {
            var firstDay = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            var lastDay = firstDay.AddMonths(1).AddDays(-1);
            SetWeekdayDates(firstDay, lastDay);
        }

        private void SetWeekdayDates(DateTime startDate, DateTime endDate)
        {
            var weekdays = new List<string>();
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday && date >= DateTime.Today)
                {
                    weekdays.Add(date.ToString("yyyy-MM-dd"));
                }
            }

            if (weekdays.Any())
            {
                rbCustomDates.Checked = true;
                rbDateMode_CheckedChanged(null, null);
                txtCustomDates.Text = string.Join("\n", weekdays);
                UpdateDatePreview();
            }
        }

        #endregion

        #region 訂單處理

        protected async void btnPreviewOrder_Click(object sender, EventArgs e)
        {
            await GenerateOrderPreviewAsync();
        }

        protected async void btnSubmitOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateOrderForm())
                {
                    ShowMessage("請檢查表單資料是否正確填寫", "warning");
                    return;
                }

                var memberId = int.Parse(ddlMembers.SelectedValue);
                var mealItemId = int.Parse(ddlMealItems.SelectedValue);
                var quantity = int.Parse(txtQuantity.Text);
                var selectedDates = GetSelectedDates();

                if (!selectedDates.Any())
                {
                    ShowMessage("請選擇至少一個用餐日期", "warning");
                    return;
                }

                MealOperationResult result;

                if (selectedDates.Count == 1)
                {
                    result = await _mealService.CreateMealOrderAsync(memberId, mealItemId, 
                        selectedDates[0], quantity, _currentUserId);
                }
                else
                {
                    result = await _mealService.CreateBulkMealOrdersAsync(memberId, mealItemId, 
                        selectedDates, quantity, _currentUserId);
                }

                if (result.Success)
                {
                    ShowMessage(result.Message, "success");
                    await ClearFormAsync();

                    // 記錄成功訂購
                    await _auditService.LogAsync("MEAL_ORDER_SUCCESS", "MealOrdering", _currentUserId,
                        $"成功建立膳食訂單：{selectedDates.Count} 天，訂單數：{result.OrderIds.Count}",
                        string.Join(",", result.OrderIds), "SUCCESS");
                }
                else
                {
                    var errorMessage = result.Message;
                    if (result.Errors.Any())
                    {
                        errorMessage += "<br/>" + string.Join("<br/>", result.Errors);
                    }
                    ShowMessage(errorMessage, "error");
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("MEAL_ORDER_ERROR", "MealOrdering", _currentUserId,
                    $"提交膳食訂單失敗：{ex.Message}", null, "ERROR");
                ShowMessage($"提交訂單失敗：{ex.Message}", "error");
            }
        }

        private bool ValidateOrderForm()
        {
            if (string.IsNullOrEmpty(ddlMembers.SelectedValue))
            {
                ShowMessage("請選擇會員", "warning");
                return false;
            }

            if (string.IsNullOrEmpty(ddlMealItems.SelectedValue))
            {
                ShowMessage("請選擇餐點", "warning");
                return false;
            }

            if (!int.TryParse(txtQuantity.Text, out int quantity) || quantity <= 0)
            {
                ShowMessage("請輸入有效的數量", "warning");
                return false;
            }

            var selectedDates = GetSelectedDates();
            if (!selectedDates.Any())
            {
                ShowMessage("請選擇至少一個用餐日期", "warning");
                return false;
            }

            return true;
        }

        private async Task GenerateOrderPreviewAsync()
        {
            try
            {
                if (!ValidateOrderForm()) return;

                var member = await GetSelectedMemberAsync();
                var mealItem = await GetSelectedMealItemAsync();
                var selectedDates = GetSelectedDates();
                var quantity = int.Parse(txtQuantity.Text);

                var preview = $@"
                    <div class='order-preview'>
                        <h6>訂單詳情</h6>
                        <table class='table table-sm'>
                            <tr><th>會員</th><td>{member?.FullName} ({member?.MemberNumber})</td></tr>
                            <tr><th>餐點</th><td>{mealItem?.ItemName} - {mealItem?.PriceDisplay}</td></tr>
                            <tr><th>每日數量</th><td>{quantity}</td></tr>
                            <tr><th>用餐日期</th><td>{selectedDates.Count} 天</td></tr>
                            <tr><th>總金額</th><td>HK${(mealItem?.Price * quantity * selectedDates.Count):F2}</td></tr>
                            <tr><th>付款方式</th><td>{ddlPaymentMethod.SelectedItem.Text}</td></tr>
                            <tr><th>付款狀態</th><td>{(cbPaidNow.Checked ? "已付款" : "待付款")}</td></tr>
                        </table>
                        <h6>用餐日期明細</h6>
                        <div class='date-list'>
                            {string.Join(", ", selectedDates.Select(d => d.ToString("yyyy-MM-dd")))}
                        </div>
                    </div>";

                ltlOrderPreview.Text = preview;
                ScriptManager.RegisterStartupScript(this, GetType(), "showPreview", "showOrderPreviewModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"生成預覽失敗：{ex.Message}", "error");
            }
        }

        #endregion

        #region 輔助方法

        private async Task<Member> GetSelectedMemberAsync()
        {
            if (string.IsNullOrEmpty(ddlMembers.SelectedValue)) return null;
            var memberId = int.Parse(ddlMembers.SelectedValue);
            
            // 簡化處理，實際應該從 MemberService 獲取
            return new Member { Id = memberId, FullName = ddlMembers.SelectedItem.Text };
        }

        private async Task<MealItem> GetSelectedMealItemAsync()
        {
            if (string.IsNullOrEmpty(ddlMealItems.SelectedValue)) return null;
            var mealItemId = int.Parse(ddlMealItems.SelectedValue);
            return await _mealService.GetMealItemByIdAsync(mealItemId);
        }

        private async Task UpdateOrderSummaryAsync()
        {
            await Task.Run(() => UpdateOrderSummary());
        }

        private void UpdateOrderSummary()
        {
            try
            {
                if (string.IsNullOrEmpty(ddlMembers.SelectedValue) || 
                    string.IsNullOrEmpty(ddlMealItems.SelectedValue))
                {
                    pnlOrderSummary.Visible = false;
                    pnlOrderSummaryEmpty.Visible = true;
                    return;
                }

                lblSummaryMember.Text = ddlMembers.SelectedItem.Text;
                lblSummaryMeal.Text = ddlMealItems.SelectedItem.Text.Split('-')[0].Trim();
                lblSummaryQuantity.Text = txtQuantity.Text;
                
                // 簡化價格處理
                lblSummaryUnitPrice.Text = "計算中...";
                lblSummaryTotalDays.Text = GetSelectedDates().Count.ToString();
                lblSummaryTotalAmount.Text = "計算中...";

                pnlOrderSummary.Visible = true;
                pnlOrderSummaryEmpty.Visible = false;
            }
            catch (Exception ex)
            {
                pnlOrderSummary.Visible = false;
                pnlOrderSummaryEmpty.Visible = true;
            }
        }

        protected void btnViewOrders_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Meals/MealManagement.aspx");
        }

        protected async void btnClearAll_Click(object sender, EventArgs e)
        {
            await ClearFormAsync();
        }

        private async Task ClearFormAsync()
        {
            txtMemberSearch.Text = "";
            ddlMembers.Items.Clear();
            ddlMembers.Items.Add(new ListItem("請先搜尋會員", ""));
            ddlMealCategory.SelectedIndex = 0;
            await LoadMealItemsAsync();
            txtQuantity.Text = "1";
            txtSingleDate.Text = DateTime.Today.ToString("yyyy-MM-dd");
            txtDateFrom.Text = "";
            txtDateTo.Text = "";
            txtCustomDates.Text = "";
            txtSpecialRequests.Text = "";
            rbSingleDate.Checked = true;
            cbPaidNow.Checked = false;
            
            pnlMemberInfo.Visible = false;
            pnlMealDetails.Visible = false;
            pnlOrderSummary.Visible = false;
            pnlOrderSummaryEmpty.Visible = true;
            pnlDatePreview.Visible = false;
        }

        // 輔助方法：取得庫存狀態標籤
        public string GetStockStatusBadge(int availableStock, int requestedQuantity)
        {
            if (availableStock >= requestedQuantity)
            {
                return $"<span class='badge bg-success'>{availableStock} 可用</span>";
            }
            else if (availableStock > 0)
            {
                return $"<span class='badge bg-warning'>{availableStock} 剩餘</span>";
            }
            else
            {
                return "<span class='badge bg-danger'>已售完</span>";
            }
        }

        private void ShowMessage(string message, string type)
        {
            try
            {
                pnlMessage.Visible = true;
                ltlMessage.Text = message;

                pnlMessage.CssClass = type switch
                {
                    "success" => "alert alert-success alert-dismissible fade show",
                    "error" => "alert alert-danger alert-dismissible fade show",
                    "warning" => "alert alert-warning alert-dismissible fade show",
                    "info" => "alert alert-info alert-dismissible fade show",
                    _ => "alert alert-info alert-dismissible fade show"
                };

                if (type == "success")
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideSuccess",
                        "setTimeout(function() { $('.alert-success').fadeOut(); }, 5000);", true);
                }
            }
            catch
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "consoleLog",
                    $"console.log('訊息: {message}');", true);
            }
        }

        #endregion

        protected override void OnUnload(EventArgs e)
        {
            _mealService?.Dispose();
            _auditService?.Dispose();
            base.OnUnload(e);
        }
    }
}