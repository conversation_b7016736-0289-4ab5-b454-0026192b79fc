<%@ Page Title="膳食訂購" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MealOrdering.aspx.cs" Inherits="CWDECC_3S.Meals.MealOrdering" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-utensils text-primary me-2"></i>膳食訂購
                </h2>
                <p class="text-muted">為會員訂購膳食，支援多日批量訂購</p>
            </div>
            <div>
                <asp:Button ID="btnViewOrders" runat="server" Text="查看訂單" CssClass="btn btn-info"
                    OnClick="btnViewOrders_Click" />
                <asp:Button ID="btnClearAll" runat="server" Text="清除所有" CssClass="btn btn-outline-secondary"
                    OnClick="btnClearAll_Click" CausesValidation="false" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <div class="row">
            <!-- 左側：會員搜尋與餐點選擇 -->
            <div class="col-lg-8">
                <!-- 會員搜尋 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-search me-2"></i>會員搜尋
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="<%= txtMemberSearch.ClientID %>">搜尋會員（會員號 / 姓名）</label>
                                    <div class="input-group">
                                        <asp:TextBox ID="txtMemberSearch" runat="server" CssClass="form-control" 
                                            placeholder="請輸入會員號或姓名" AutoPostBack="true"
                                            OnTextChanged="txtMemberSearch_TextChanged"></asp:TextBox>
                                        <asp:Button ID="btnSearchMember" runat="server" Text="搜尋" CssClass="btn btn-primary"
                                            OnClick="btnSearchMember_Click" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="<%= ddlMembers.ClientID %>">選擇會員</label>
                                    <asp:DropDownList ID="ddlMembers" runat="server" CssClass="form-select"
                                        AutoPostBack="true" OnSelectedIndexChanged="ddlMembers_SelectedIndexChanged">
                                        <asp:ListItem Text="請先搜尋會員" Value=""></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 會員資訊顯示 -->
                        <asp:Panel ID="pnlMemberInfo" runat="server" Visible="false" CssClass="member-info-panel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <div class="member-avatar me-3">
                                            <i class="fas fa-user-circle fa-3x text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                <asp:Label ID="lblMemberName" runat="server" CssClass="fw-bold"></asp:Label>
                                            </h6>
                                            <p class="mb-0 text-muted">
                                                會員號：<asp:Label ID="lblMemberNumber" runat="server"></asp:Label>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="member-details">
                                        <p class="mb-1">
                                            <small class="text-muted">會員類型：</small>
                                            <asp:Label ID="lblMemberType" runat="server"></asp:Label>
                                        </p>
                                        <p class="mb-0">
                                            <small class="text-muted">會籍狀態：</small>
                                            <asp:Label ID="lblMemberStatus" runat="server"></asp:Label>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 餐點選擇 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-utensils me-2"></i>餐點選擇
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="<%= ddlMealCategory.ClientID %>">餐點類別</label>
                                    <asp:DropDownList ID="ddlMealCategory" runat="server" CssClass="form-select"
                                        AutoPostBack="true" OnSelectedIndexChanged="ddlMealCategory_SelectedIndexChanged">
                                        <asp:ListItem Text="全部類別" Value=""></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="<%= ddlMealItems.ClientID %>">選擇餐點</label>
                                    <asp:DropDownList ID="ddlMealItems" runat="server" CssClass="form-select"
                                        AutoPostBack="true" OnSelectedIndexChanged="ddlMealItems_SelectedIndexChanged">
                                        <asp:ListItem Text="請選擇餐點" Value=""></asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvMealItems" runat="server" 
                                        ControlToValidate="ddlMealItems" InitialValue="" ErrorMessage="請選擇餐點"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="OrderMeal"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>

                        <!-- 餐點詳情 -->
                        <asp:Panel ID="pnlMealDetails" runat="server" Visible="false" CssClass="meal-details-panel">
                            <div class="meal-item-card">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6 class="meal-name">
                                            <asp:Label ID="lblMealName" runat="server"></asp:Label>
                                        </h6>
                                        <p class="meal-description text-muted">
                                            <asp:Label ID="lblMealDescription" runat="server"></asp:Label>
                                        </p>
                                        <div class="meal-info">
                                            <span class="badge bg-info me-2">
                                                <asp:Label ID="lblMealCategory" runat="server"></asp:Label>
                                            </span>
                                            <asp:Panel ID="pnlAllergenInfo" runat="server" Visible="false">
                                                <span class="badge bg-warning text-dark me-2">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    <asp:Label ID="lblAllergenInfo" runat="server"></asp:Label>
                                                </span>
                                            </asp:Panel>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="meal-price">
                                            <span class="price-label">價格：</span>
                                            <span class="price-amount">
                                                <asp:Label ID="lblMealPrice" runat="server"></asp:Label>
                                            </span>
                                        </div>
                                        <asp:Panel ID="pnlDailyLimit" runat="server" Visible="false">
                                            <small class="text-muted">
                                                每日限量：<asp:Label ID="lblDailyLimit" runat="server"></asp:Label>
                                            </small>
                                        </asp:Panel>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 日期選擇 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>用餐日期選擇
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="<%= txtSingleDate.ClientID %>">單日訂購</label>
                                    <asp:TextBox ID="txtSingleDate" runat="server" CssClass="form-control" 
                                        TextMode="Date"></asp:TextBox>
                                    <div class="form-check mt-2">
                                        <asp:RadioButton ID="rbSingleDate" runat="server" GroupName="DateMode" 
                                            CssClass="form-check-input" Checked="true" AutoPostBack="true"
                                            OnCheckedChanged="rbDateMode_CheckedChanged" />
                                        <label class="form-check-label" for="<%= rbSingleDate.ClientID %>">
                                            選擇單一日期
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>多日期範圍訂購</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <asp:TextBox ID="txtDateFrom" runat="server" CssClass="form-control" 
                                                TextMode="Date" placeholder="開始日期"></asp:TextBox>
                                        </div>
                                        <div class="col-6">
                                            <asp:TextBox ID="txtDateTo" runat="server" CssClass="form-control" 
                                                TextMode="Date" placeholder="結束日期"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="form-check mt-2">
                                        <asp:RadioButton ID="rbDateRange" runat="server" GroupName="DateMode" 
                                            CssClass="form-check-input" AutoPostBack="true"
                                            OnCheckedChanged="rbDateMode_CheckedChanged" />
                                        <label class="form-check-label" for="<%= rbDateRange.ClientID %>">
                                            選擇日期範圍
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 自訂日期選擇 -->
                        <asp:Panel ID="pnlCustomDates" runat="server" Visible="false" CssClass="mt-3">
                            <div class="form-check mb-3">
                                <asp:RadioButton ID="rbCustomDates" runat="server" GroupName="DateMode" 
                                    CssClass="form-check-input" AutoPostBack="true"
                                    OnCheckedChanged="rbDateMode_CheckedChanged" />
                                <label class="form-check-label" for="<%= rbCustomDates.ClientID %>">
                                    自訂多個日期
                                </label>
                            </div>
                            <div class="custom-dates-input">
                                <label>選擇多個日期（一行一個日期，格式：YYYY-MM-DD）</label>
                                <asp:TextBox ID="txtCustomDates" runat="server" CssClass="form-control" 
                                    TextMode="MultiLine" Rows="5" placeholder="2024-01-15&#10;2024-01-17&#10;2024-01-19"></asp:TextBox>
                                <small class="form-text text-muted">
                                    每行輸入一個日期，格式為 YYYY-MM-DD
                                </small>
                            </div>
                        </asp:Panel>

                        <!-- 日期預覽 -->
                        <asp:Panel ID="pnlDatePreview" runat="server" Visible="false" CssClass="mt-3">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>選擇的日期
                                </h6>
                                <div class="date-preview-list">
                                    <asp:Literal ID="ltlDatePreview" runat="server"></asp:Literal>
                                </div>
                                <hr>
                                <p class="mb-0">
                                    總計：<strong><asp:Label ID="lblTotalDays" runat="server"></asp:Label></strong> 天
                                    | 預估費用：<strong><asp:Label ID="lblEstimatedCost" runat="server"></asp:Label></strong>
                                </p>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 訂購設定 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>訂購設定
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="<%= txtQuantity.ClientID %>">每日數量</label>
                                    <asp:TextBox ID="txtQuantity" runat="server" CssClass="form-control" 
                                        TextMode="Number" min="1" max="10" Text="1"
                                        AutoPostBack="true" OnTextChanged="txtQuantity_TextChanged"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvQuantity" runat="server" 
                                        ControlToValidate="txtQuantity" ErrorMessage="請輸入數量"
                                        CssClass="text-danger small" Display="Dynamic" ValidationGroup="OrderMeal"></asp:RequiredFieldValidator>
                                    <asp:RangeValidator ID="rvQuantity" runat="server" 
                                        ControlToValidate="txtQuantity" Type="Integer" MinimumValue="1" MaximumValue="10"
                                        ErrorMessage="數量必須在 1-10 之間" CssClass="text-danger small" Display="Dynamic" ValidationGroup="OrderMeal"></asp:RangeValidator>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="<%= ddlPaymentMethod.ClientID %>">付款方式</label>
                                    <asp:DropDownList ID="ddlPaymentMethod" runat="server" CssClass="form-select">
                                        <asp:ListItem Text="現金" Value="Cash"></asp:ListItem>
                                        <asp:ListItem Text="八達通" Value="Octopus"></asp:ListItem>
                                        <asp:ListItem Text="信用卡" Value="CreditCard"></asp:ListItem>
                                        <asp:ListItem Text="銀行轉賬" Value="BankTransfer"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>付款狀態</label>
                                    <div class="form-check">
                                        <asp:CheckBox ID="cbPaidNow" runat="server" CssClass="form-check-input" />
                                        <label class="form-check-label" for="<%= cbPaidNow.ClientID %>">
                                            現在付款
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="<%= txtSpecialRequests.ClientID %>">特殊要求</label>
                                    <asp:TextBox ID="txtSpecialRequests" runat="server" CssClass="form-control" 
                                        TextMode="MultiLine" Rows="2" placeholder="如有特殊飲食要求請註明" MaxLength="500"></asp:TextBox>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <asp:Button ID="btnPreviewOrder" runat="server" Text="預覽訂單" CssClass="btn btn-info me-2"
                                    OnClick="btnPreviewOrder_Click" ValidationGroup="OrderMeal" />
                                <asp:Button ID="btnGenerateDates" runat="server" Text="產生日期" CssClass="btn btn-outline-primary"
                                    OnClick="btnGenerateDates_Click" CausesValidation="false" />
                            </div>
                            <div>
                                <asp:Button ID="btnSubmitOrder" runat="server" Text="確認訂購" CssClass="btn btn-success btn-lg"
                                    OnClick="btnSubmitOrder_Click" ValidationGroup="OrderMeal" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：訂購摘要與日期檢查 -->
            <div class="col-lg-4">
                <!-- 訂購摘要 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>訂購摘要
                        </h5>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlOrderSummary" runat="server" Visible="false">
                            <div class="order-summary">
                                <div class="summary-item">
                                    <span class="item-label">會員：</span>
                                    <span class="item-value">
                                        <asp:Label ID="lblSummaryMember" runat="server"></asp:Label>
                                    </span>
                                </div>
                                <div class="summary-item">
                                    <span class="item-label">餐點：</span>
                                    <span class="item-value">
                                        <asp:Label ID="lblSummaryMeal" runat="server"></asp:Label>
                                    </span>
                                </div>
                                <div class="summary-item">
                                    <span class="item-label">每日數量：</span>
                                    <span class="item-value">
                                        <asp:Label ID="lblSummaryQuantity" runat="server"></asp:Label>
                                    </span>
                                </div>
                                <div class="summary-item">
                                    <span class="item-label">單價：</span>
                                    <span class="item-value">
                                        <asp:Label ID="lblSummaryUnitPrice" runat="server"></asp:Label>
                                    </span>
                                </div>
                                <div class="summary-item">
                                    <span class="item-label">總天數：</span>
                                    <span class="item-value">
                                        <asp:Label ID="lblSummaryTotalDays" runat="server"></asp:Label>
                                    </span>
                                </div>
                                <hr>
                                <div class="summary-item total">
                                    <span class="item-label"><strong>總金額：</strong></span>
                                    <span class="item-value">
                                        <strong><asp:Label ID="lblSummaryTotalAmount" runat="server"></asp:Label></strong>
                                    </span>
                                </div>
                            </div>
                        </asp:Panel>
                        <asp:Panel ID="pnlOrderSummaryEmpty" runat="server" Visible="true">
                            <div class="text-center text-muted">
                                <i class="fas fa-clipboard fa-3x mb-3"></i>
                                <p>請先選擇會員和餐點</p>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 庫存檢查 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-boxes me-2"></i>庫存檢查
                        </h5>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlStockCheck" runat="server" Visible="false">
                            <asp:Repeater ID="rptStockStatus" runat="server">
                                <ItemTemplate>
                                    <div class="stock-item d-flex justify-content-between align-items-center mb-2">
                                        <span class="date"><%# Eval("Date", "{0:MM/dd}") %></span>
                                        <span class="stock-badge">
                                            <%# GetStockStatusBadge(Convert.ToInt32(Eval("AvailableStock")), Convert.ToInt32(Eval("RequestedQuantity"))) %>
                                        </span>
                                    </div>
                                </ItemTemplate>
                            </asp:Repeater>
                        </asp:Panel>
                        <asp:Panel ID="pnlStockCheckEmpty" runat="server" Visible="true">
                            <div class="text-center text-muted">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <p>選擇餐點和日期後顯示庫存狀態</p>
                            </div>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnQuickWeek" runat="server" Text="本週工作日" CssClass="btn btn-outline-primary btn-sm"
                                OnClick="btnQuickWeek_Click" CausesValidation="false" />
                            <asp:Button ID="btnQuickNextWeek" runat="server" Text="下週工作日" CssClass="btn btn-outline-primary btn-sm"
                                OnClick="btnQuickNextWeek_Click" CausesValidation="false" />
                            <asp:Button ID="btnQuickMonth" runat="server" Text="本月工作日" CssClass="btn btn-outline-success btn-sm"
                                OnClick="btnQuickMonth_Click" CausesValidation="false" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 訂單預覽模態視窗 -->
    <div class="modal fade" id="orderPreviewModal" tabindex="-1" aria-labelledby="orderPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderPreviewModalLabel">
                        <i class="fas fa-eye me-2"></i>訂單預覽
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="order-preview-content">
                        <asp:Literal ID="ltlOrderPreview" runat="server"></asp:Literal>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnConfirmOrder" runat="server" Text="確認訂購" CssClass="btn btn-success"
                        OnClick="btnSubmitOrder_Click" ValidationGroup="OrderMeal" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .member-info-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .meal-details-panel {
            margin-top: 15px;
        }
        
        .meal-item-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        
        .meal-name {
            color: #2c5aa0;
            margin-bottom: 8px;
        }
        
        .meal-price {
            font-size: 1.2em;
        }
        
        .price-amount {
            color: #28a745;
            font-weight: bold;
        }
        
        .order-summary .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .order-summary .summary-item:last-child {
            border-bottom: none;
        }
        
        .order-summary .summary-item.total {
            background: #f8f9fa;
            padding: 12px;
            margin: 0 -15px -15px -15px;
            border-radius: 0 0 8px 8px;
        }
        
        .stock-item {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .custom-dates-input {
            border: 1px dashed #ced4da;
            padding: 15px;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .date-preview-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .date-badge {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.875em;
        }
        
        .card-stats:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        @media print {
            .btn, .card-header, .modal {
                display: none !important;
            }
            
            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 自動完成會員搜尋
            $('#<%= txtMemberSearch.ClientID %>').on('input', function() {
                var value = $(this).val();
                if (value.length >= 2) {
                    // 可以在這裡添加自動完成功能
                }
            });

            // 日期驗證
            function validateDates() {
                var isValid = true;
                var today = new Date();
                today.setHours(0, 0, 0, 0);

                // 驗證單日期
                if ($('#<%= rbSingleDate.ClientID %>').prop('checked')) {
                    var singleDate = new Date($('#<%= txtSingleDate.ClientID %>').val());
                    if (singleDate < today) {
                        alert('用餐日期不能早於今天');
                        isValid = false;
                    }
                }

                // 驗證日期範圍
                if ($('#<%= rbDateRange.ClientID %>').prop('checked')) {
                    var dateFrom = new Date($('#<%= txtDateFrom.ClientID %>').val());
                    var dateTo = new Date($('#<%= txtDateTo.ClientID %>').val());
                    
                    if (dateFrom < today) {
                        alert('開始日期不能早於今天');
                        isValid = false;
                    }
                    
                    if (dateTo < dateFrom) {
                        alert('結束日期不能早於開始日期');
                        isValid = false;
                    }
                }

                return isValid;
            }

            // 綁定表單提交事件
            $('#<%= btnSubmitOrder.ClientID %>').click(function(e) {
                if (!validateDates()) {
                    e.preventDefault();
                    return false;
                }
            });

            // 顯示訂單預覽模態視窗
            window.showOrderPreviewModal = function() {
                $('#orderPreviewModal').modal('show');
            };
        });

        // 顯示載入指示器
        function showLoading(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: message || '處理中...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        // 隱藏載入指示器
        function hideLoading() {
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }
    </script>
</asp:Content>