using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CWDECC_3S.Models;
using CWDECC_3S.Services;
using OfficeOpenXml;

namespace CWDECC_3S.Meals
{
    public partial class MealManagement : System.Web.UI.Page
    {
        private readonly MealService _mealService;
        private DateTime SearchDate
        {
            get
            {
                if (DateTime.TryParse(txtSearchDate.Text, out DateTime date))
                    return date;
                return DateTime.Today;
            }
        }

        public MealManagement()
        {
            _mealService = new MealService();
        }

        protected async void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                await InitializePageAsync();
            }
        }

        private async Task InitializePageAsync()
        {
            try
            {
                // 設定今天為預設查詢日期
                txtSearchDate.Text = DateTime.Today.ToString("yyyy-MM-dd");
                
                // 載入下拉選單資料
                await LoadDropDownDataAsync();
                
                // 載入今天的訂單資料
                await LoadOrderDataAsync();
            }
            catch (Exception ex)
            {
                ShowMessage($"頁面初始化錯誤：{ex.Message}", "error");
            }
        }

        private async Task LoadDropDownDataAsync()
        {
            try
            {
                // 載入訂單狀態
                LoadOrderStatusDropDown();
                
                // 載入付款狀態
                LoadPaymentStatusDropDown();
                
                // 載入餐點類別
                await LoadMealCategoryDropDownAsync();
            }
            catch (Exception ex)
            {
                ShowMessage($"載入選單資料錯誤：{ex.Message}", "error");
            }
        }

        private void LoadOrderStatusDropDown()
        {
            ddlOrderStatus.Items.Clear();
            ddlOrderStatus.Items.Add(new ListItem("全部狀態", ""));
            ddlOrderStatus.Items.Add(new ListItem("已訂購", "Ordered"));
            ddlOrderStatus.Items.Add(new ListItem("已確認", "Confirmed"));
            ddlOrderStatus.Items.Add(new ListItem("已取消", "Cancelled"));
        }

        private void LoadPaymentStatusDropDown()
        {
            ddlPaymentStatus.Items.Clear();
            ddlPaymentStatus.Items.Add(new ListItem("全部狀態", ""));
            ddlPaymentStatus.Items.Add(new ListItem("未付款", "Pending"));
            ddlPaymentStatus.Items.Add(new ListItem("已付款", "Paid"));
            ddlPaymentStatus.Items.Add(new ListItem("已取消", "Cancelled"));
        }

        private async Task LoadMealCategoryDropDownAsync()
        {
            try
            {
                var categories = await _mealService.GetMealCategoriesAsync();
                
                ddlMealCategory.Items.Clear();
                ddlMealCategory.Items.Add(new ListItem("全部類別", ""));
                
                foreach (var category in categories)
                {
                    ddlMealCategory.Items.Add(new ListItem(category.Value, category.Key));
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入餐點類別錯誤：{ex.Message}", "error");
            }
        }

        private async Task LoadOrderDataAsync()
        {
            try
            {
                var searchDate = SearchDate;
                var orderStatus = string.IsNullOrEmpty(ddlOrderStatus.SelectedValue) ? (OrderStatus?)null : 
                    Enum.Parse<OrderStatus>(ddlOrderStatus.SelectedValue);
                var paymentStatus = string.IsNullOrEmpty(ddlPaymentStatus.SelectedValue) ? (PaymentStatus?)null : 
                    Enum.Parse<PaymentStatus>(ddlPaymentStatus.SelectedValue);
                var mealCategory = string.IsNullOrEmpty(ddlMealCategory.SelectedValue) ? (MealCategory?)null : 
                    Enum.Parse<MealCategory>(ddlMealCategory.SelectedValue);

                // 取得訂單列表
                var orders = await _mealService.GetMealOrdersByDateAsync(
                    searchDate, orderStatus, paymentStatus, mealCategory);

                // 綁定到GridView
                gvOrders.DataSource = orders;
                gvOrders.DataBind();

                // 更新顯示狀態
                UpdateDisplayStatus(orders);

                // 載入統計資料
                await LoadStatisticsAsync(searchDate, orders);
                
                // 載入餐點統計
                await LoadMealStatisticsAsync(searchDate);
            }
            catch (Exception ex)
            {
                ShowMessage($"載入訂單資料錯誤：{ex.Message}", "error");
            }
        }

        private void UpdateDisplayStatus(List<MealOrder> orders)
        {
            if (orders?.Any() == true)
            {
                pnlOrders.Visible = true;
                pnlNoOrders.Visible = false;
                lblRecordCount.Text = $"共 {orders.Count} 筆記錄";
            }
            else
            {
                pnlOrders.Visible = false;
                pnlNoOrders.Visible = true;
                lblRecordCount.Text = "0 筆記錄";
            }

            lblSearchDate.Text = SearchDate.ToString("yyyy年MM月dd日");
        }

        private async Task LoadStatisticsAsync(DateTime date, List<MealOrder> orders)
        {
            try
            {
                if (orders?.Any() != true)
                {
                    pnlStatistics.Visible = false;
                    return;
                }

                // 計算統計資料
                var totalOrders = orders.Count;
                var mainMealCount = orders.Count(o => o.MealItem.Category == MealCategory.MainMeal);
                var sideMealCount = orders.Count(o => o.MealItem.Category == MealCategory.SideMeal);
                var totalRevenue = orders.Where(o => o.PaymentStatus == PaymentStatus.Paid)
                    .Sum(o => o.TotalAmount);

                // 更新統計標籤
                lblTotalOrders.Text = totalOrders.ToString();
                lblMainMealCount.Text = mainMealCount.ToString();
                lblSideMealCount.Text = sideMealCount.ToString();
                lblTotalRevenue.Text = totalRevenue.ToString("F2");

                pnlStatistics.Visible = true;
            }
            catch (Exception ex)
            {
                ShowMessage($"載入統計資料錯誤：{ex.Message}", "error");
            }
        }

        private async Task LoadMealStatisticsAsync(DateTime date)
        {
            try
            {
                var statistics = await _mealService.GetDailyMealStatisticsAsync(date);
                
                if (statistics?.Any() == true)
                {
                    gvMealStatistics.DataSource = statistics;
                    gvMealStatistics.DataBind();
                    pnlMealStatistics.Visible = true;
                }
                else
                {
                    pnlMealStatistics.Visible = false;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"載入餐點統計錯誤：{ex.Message}", "error");
            }
        }

        protected async void txtSearchDate_TextChanged(object sender, EventArgs e)
        {
            await LoadOrderDataAsync();
        }

        protected async void ddlDateRange_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(ddlDateRange.SelectedValue))
                return;

            DateTime targetDate;
            switch (ddlDateRange.SelectedValue)
            {
                case "Today":
                    targetDate = DateTime.Today;
                    break;
                case "Tomorrow":
                    targetDate = DateTime.Today.AddDays(1);
                    break;
                case "DayAfterTomorrow":
                    targetDate = DateTime.Today.AddDays(2);
                    break;
                case "ThisWeek":
                    targetDate = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                    break;
                case "NextWeek":
                    targetDate = DateTime.Today.AddDays(7 - (int)DateTime.Today.DayOfWeek);
                    break;
                default:
                    return;
            }

            txtSearchDate.Text = targetDate.ToString("yyyy-MM-dd");
            await LoadOrderDataAsync();
        }

        protected async void ddlOrderStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadOrderDataAsync();
        }

        protected async void ddlPaymentStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadOrderDataAsync();
        }

        protected async void ddlMealCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadOrderDataAsync();
        }

        protected async void btnSearch_Click(object sender, EventArgs e)
        {
            await LoadOrderDataAsync();
        }

        protected async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadOrderDataAsync();
        }

        protected void btnNewOrder_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Meals/MealOrdering.aspx");
        }

        protected async void gvOrders_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            gvOrders.PageIndex = e.NewPageIndex;
            await LoadOrderDataAsync();
        }

        protected async void gvOrders_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (!int.TryParse(e.CommandArgument.ToString(), out int orderId))
                return;

            try
            {
                switch (e.CommandName)
                {
                    case "ViewDetails":
                        await ShowOrderDetailsAsync(orderId);
                        break;
                    case "CancelOrder":
                        await CancelOrderAsync(orderId);
                        break;
                    case "UpdatePayment":
                        await ShowPaymentUpdateModalAsync(orderId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"操作錯誤：{ex.Message}", "error");
            }
        }

        protected void gvOrders_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                var order = (MealOrder)e.Row.DataItem;
                
                // 設定狀態樣式
                SetOrderStatusStyle(e.Row, order);
                
                // 設定按鈕可見性
                SetButtonVisibility(e.Row, order);
            }
        }

        private void SetOrderStatusStyle(GridViewRow row, MealOrder order)
        {
            // 根據訂單狀態設定行樣式
            switch (order.OrderStatus)
            {
                case OrderStatus.Ordered:
                    row.Cells[7].CssClass = "order-status-ordered";
                    break;
                case OrderStatus.Confirmed:
                    row.Cells[7].CssClass = "order-status-confirmed";
                    break;
                case OrderStatus.Cancelled:
                    row.Cells[7].CssClass = "order-status-cancelled";
                    break;
            }

            // 根據付款狀態設定樣式
            switch (order.PaymentStatus)
            {
                case PaymentStatus.Pending:
                    row.Cells[8].CssClass = "payment-status-pending";
                    break;
                case PaymentStatus.Paid:
                    row.Cells[8].CssClass = "payment-status-paid";
                    break;
                case PaymentStatus.Cancelled:
                    row.Cells[8].CssClass = "payment-status-cancelled";
                    break;
            }
        }

        private void SetButtonVisibility(GridViewRow row, MealOrder order)
        {
            var btnCancelOrder = (Button)row.FindControl("btnCancelOrder");
            var btnUpdatePayment = (Button)row.FindControl("btnUpdatePayment");

            if (btnCancelOrder != null)
            {
                btnCancelOrder.Visible = order.CanCancel();
            }

            if (btnUpdatePayment != null)
            {
                btnUpdatePayment.Visible = order.PaymentStatus == PaymentStatus.Pending;
            }
        }

        private async Task ShowOrderDetailsAsync(int orderId)
        {
            try
            {
                var order = await _mealService.GetMealOrderByIdAsync(orderId);
                if (order == null)
                {
                    ShowMessage("找不到指定的訂單", "error");
                    return;
                }

                var detailsHtml = GenerateOrderDetailsHtml(order);
                ltlOrderDetails.Text = detailsHtml;
                
                ScriptManager.RegisterStartupScript(this, GetType(), "showOrderDetails",
                    "showOrderDetailsModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"載入訂單詳情錯誤：{ex.Message}", "error");
            }
        }

        private string GenerateOrderDetailsHtml(MealOrder order)
        {
            var sb = new StringBuilder();
            sb.AppendLine("<div class='order-details'>");
            sb.AppendLine($"<h6>基本資訊</h6>");
            sb.AppendLine($"<p><strong>訂單號碼：</strong>{order.OrderNumber}</p>");
            sb.AppendLine($"<p><strong>會員：</strong>{order.Member.FullName} ({order.Member.MemberNumber})</p>");
            sb.AppendLine($"<p><strong>用餐日期：</strong>{order.MealDate:yyyy年MM月dd日}</p>");
            sb.AppendLine($"<p><strong>訂購時間：</strong>{order.OrderDate:yyyy-MM-dd HH:mm:ss}</p>");
            
            sb.AppendLine($"<h6 class='mt-3'>餐點資訊</h6>");
            sb.AppendLine($"<p><strong>餐點名稱：</strong>{order.MealItem.ItemName}</p>");
            sb.AppendLine($"<p><strong>餐點類別：</strong>{order.MealItem.CategoryDisplayText}</p>");
            sb.AppendLine($"<p><strong>數量：</strong>{order.Quantity}</p>");
            sb.AppendLine($"<p><strong>單價：</strong>HK${order.UnitPrice:F2}</p>");
            sb.AppendLine($"<p><strong>總金額：</strong>HK${order.TotalAmount:F2}</p>");

            if (!string.IsNullOrEmpty(order.MealItem.Description))
            {
                sb.AppendLine($"<p><strong>餐點描述：</strong>{order.MealItem.Description}</p>");
            }

            if (!string.IsNullOrEmpty(order.MealItem.AllergenInfo))
            {
                sb.AppendLine($"<p><strong>過敏原資訊：</strong>{order.MealItem.AllergenInfo}</p>");
            }

            sb.AppendLine($"<h6 class='mt-3'>狀態資訊</h6>");
            sb.AppendLine($"<p><strong>訂單狀態：</strong>{order.OrderStatusDisplayText}</p>");
            sb.AppendLine($"<p><strong>付款狀態：</strong>{order.PaymentStatusDisplayText}</p>");
            sb.AppendLine($"<p><strong>付款方式：</strong>{order.PaymentMethodDisplayText}</p>");

            if (!string.IsNullOrEmpty(order.SpecialRequests))
            {
                sb.AppendLine($"<h6 class='mt-3'>特殊要求</h6>");
                sb.AppendLine($"<p>{order.SpecialRequests}</p>");
            }

            if (!string.IsNullOrEmpty(order.Remarks))
            {
                sb.AppendLine($"<h6 class='mt-3'>備註</h6>");
                sb.AppendLine($"<p>{order.Remarks}</p>");
            }

            sb.AppendLine($"<h6 class='mt-3'>操作記錄</h6>");
            sb.AppendLine($"<p><strong>訂購人：</strong>{order.OrderedBy}</p>");
            sb.AppendLine($"<p><strong>創建時間：</strong>{order.CreatedAt:yyyy-MM-dd HH:mm:ss}</p>");
            
            if (order.UpdatedAt.HasValue)
            {
                sb.AppendLine($"<p><strong>更新時間：</strong>{order.UpdatedAt:yyyy-MM-dd HH:mm:ss}</p>");
            }

            sb.AppendLine("</div>");
            return sb.ToString();
        }

        private async Task CancelOrderAsync(int orderId)
        {
            try
            {
                var result = await _mealService.CancelMealOrderAsync(orderId, User.Identity.Name);
                
                if (result.Success)
                {
                    ShowMessage("訂單取消成功", "success");
                    await LoadOrderDataAsync();
                }
                else
                {
                    ShowMessage($"取消訂單失敗：{result.Message}", "error");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"取消訂單錯誤：{ex.Message}", "error");
            }
        }

        private async Task ShowPaymentUpdateModalAsync(int orderId)
        {
            try
            {
                var order = await _mealService.GetMealOrderByIdAsync(orderId);
                if (order == null)
                {
                    ShowMessage("找不到指定的訂單", "error");
                    return;
                }

                hfSelectedOrderId.Value = orderId.ToString();
                ddlUpdatePaymentMethod.SelectedValue = order.PaymentMethod.ToString();
                txtPaymentRemarks.Text = order.Remarks;

                ScriptManager.RegisterStartupScript(this, GetType(), "showPaymentUpdate",
                    "showPaymentUpdateModal();", true);
            }
            catch (Exception ex)
            {
                ShowMessage($"載入付款資訊錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnConfirmPayment_Click(object sender, EventArgs e)
        {
            try
            {
                if (!int.TryParse(hfSelectedOrderId.Value, out int orderId))
                {
                    ShowMessage("無效的訂單ID", "error");
                    return;
                }

                var paymentMethod = Enum.Parse<PaymentMethod>(ddlUpdatePaymentMethod.SelectedValue);
                var remarks = txtPaymentRemarks.Text.Trim();

                var result = await _mealService.UpdatePaymentStatusAsync(
                    orderId, PaymentStatus.Paid, paymentMethod, remarks, User.Identity.Name);

                if (result.Success)
                {
                    ShowMessage("付款狀態更新成功", "success");
                    await LoadOrderDataAsync();
                }
                else
                {
                    ShowMessage($"更新付款狀態失敗：{result.Message}", "error");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"更新付款狀態錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnExportCSV_Click(object sender, EventArgs e)
        {
            try
            {
                await ExportDataAsync("csv");
            }
            catch (Exception ex)
            {
                ShowMessage($"匯出CSV錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                await ExportDataAsync("excel");
            }
            catch (Exception ex)
            {
                ShowMessage($"匯出Excel錯誤：{ex.Message}", "error");
            }
        }

        protected async void btnExportDailyReport_Click(object sender, EventArgs e)
        {
            try
            {
                await ExportDailyReportAsync();
            }
            catch (Exception ex)
            {
                ShowMessage($"匯出每日報表錯誤：{ex.Message}", "error");
            }
        }

        private async Task ExportDataAsync(string format)
        {
            try
            {
                var searchDate = SearchDate;
                var orders = await _mealService.GetMealOrdersByDateAsync(searchDate);

                if (!orders.Any())
                {
                    ShowMessage("沒有資料可匯出", "warning");
                    return;
                }

                string fileName;
                string contentType;
                byte[] data;

                if (format.ToLower() == "csv")
                {
                    fileName = $"膳食訂單_{searchDate:yyyyMMdd}.csv";
                    contentType = "text/csv";
                    data = GenerateCSVData(orders);
                }
                else
                {
                    fileName = $"膳食訂單_{searchDate:yyyyMMdd}.xlsx";
                    contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    data = GenerateExcelData(orders);
                }

                // 設定回應標頭並輸出文件
                Response.Clear();
                Response.ContentType = contentType;
                Response.AddHeader("Content-Disposition", $"attachment; filename=\"{fileName}\"");
                Response.BinaryWrite(data);
                Response.End();
            }
            catch (Exception ex)
            {
                ShowMessage($"匯出資料錯誤：{ex.Message}", "error");
            }
        }

        private byte[] GenerateCSVData(List<MealOrder> orders)
        {
            var csv = new StringBuilder();
            
            // CSV 標頭
            csv.AppendLine("訂單號碼,會員姓名,會員號,餐點名稱,餐點類別,數量,單價,總金額,訂單狀態,付款狀態,付款方式,用餐日期,訂購時間,訂購人,特殊要求,備註");

            // 資料行
            foreach (var order in orders)
            {
                csv.AppendLine($"\"{order.OrderNumber}\"," +
                              $"\"{order.Member.FullName}\"," +
                              $"\"{order.Member.MemberNumber}\"," +
                              $"\"{order.MealItem.ItemName}\"," +
                              $"\"{order.MealItem.CategoryDisplayText}\"," +
                              $"{order.Quantity}," +
                              $"{order.UnitPrice:F2}," +
                              $"{order.TotalAmount:F2}," +
                              $"\"{order.OrderStatusDisplayText}\"," +
                              $"\"{order.PaymentStatusDisplayText}\"," +
                              $"\"{order.PaymentMethodDisplayText}\"," +
                              $"\"{order.MealDate:yyyy-MM-dd}\"," +
                              $"\"{order.OrderDate:yyyy-MM-dd HH:mm:ss}\"," +
                              $"\"{order.OrderedBy}\"," +
                              $"\"{order.SpecialRequests}\"," +
                              $"\"{order.Remarks}\"");
            }

            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        private byte[] GenerateExcelData(List<MealOrder> orders)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("膳食訂單");

                // 設定標頭
                var headers = new[]
                {
                    "訂單號碼", "會員姓名", "會員號", "餐點名稱", "餐點類別", "數量", "單價", "總金額",
                    "訂單狀態", "付款狀態", "付款方式", "用餐日期", "訂購時間", "訂購人", "特殊要求", "備註"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                }

                // 填入資料
                for (int i = 0; i < orders.Count; i++)
                {
                    var order = orders[i];
                    var row = i + 2;

                    worksheet.Cells[row, 1].Value = order.OrderNumber;
                    worksheet.Cells[row, 2].Value = order.Member.FullName;
                    worksheet.Cells[row, 3].Value = order.Member.MemberNumber;
                    worksheet.Cells[row, 4].Value = order.MealItem.ItemName;
                    worksheet.Cells[row, 5].Value = order.MealItem.CategoryDisplayText;
                    worksheet.Cells[row, 6].Value = order.Quantity;
                    worksheet.Cells[row, 7].Value = order.UnitPrice;
                    worksheet.Cells[row, 8].Value = order.TotalAmount;
                    worksheet.Cells[row, 9].Value = order.OrderStatusDisplayText;
                    worksheet.Cells[row, 10].Value = order.PaymentStatusDisplayText;
                    worksheet.Cells[row, 11].Value = order.PaymentMethodDisplayText;
                    worksheet.Cells[row, 12].Value = order.MealDate.ToString("yyyy-MM-dd");
                    worksheet.Cells[row, 13].Value = order.OrderDate.ToString("yyyy-MM-dd HH:mm:ss");
                    worksheet.Cells[row, 14].Value = order.OrderedBy;
                    worksheet.Cells[row, 15].Value = order.SpecialRequests;
                    worksheet.Cells[row, 16].Value = order.Remarks;
                }

                // 自動調整欄寬
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
        }

        private async Task ExportDailyReportAsync()
        {
            try
            {
                var searchDate = SearchDate;
                var orders = await _mealService.GetMealOrdersByDateAsync(searchDate);
                var statistics = await _mealService.GetDailyMealStatisticsAsync(searchDate);

                var fileName = $"每日膳食統計報表_{searchDate:yyyyMMdd}.xlsx";

                using (var package = new ExcelPackage())
                {
                    // 建立報表工作表
                    var reportSheet = package.Workbook.Worksheets.Add("每日統計報表");
                    
                    // 報表標題
                    reportSheet.Cells[1, 1].Value = $"{searchDate:yyyy年MM月dd日} 膳食統計報表";
                    reportSheet.Cells[1, 1].Style.Font.Size = 16;
                    reportSheet.Cells[1, 1].Style.Font.Bold = true;

                    // 統計摘要
                    var row = 3;
                    reportSheet.Cells[row, 1].Value = "統計摘要";
                    reportSheet.Cells[row, 1].Style.Font.Bold = true;
                    row++;

                    reportSheet.Cells[row, 1].Value = "總訂單數：";
                    reportSheet.Cells[row, 2].Value = orders.Count;
                    row++;

                    reportSheet.Cells[row, 1].Value = "主餐訂單：";
                    reportSheet.Cells[row, 2].Value = orders.Count(o => o.MealItem.Category == MealCategory.MainMeal);
                    row++;

                    reportSheet.Cells[row, 1].Value = "副餐訂單：";
                    reportSheet.Cells[row, 2].Value = orders.Count(o => o.MealItem.Category == MealCategory.SideMeal);
                    row++;

                    reportSheet.Cells[row, 1].Value = "總營收：";
                    reportSheet.Cells[row, 2].Value = $"HK${orders.Where(o => o.PaymentStatus == PaymentStatus.Paid).Sum(o => o.TotalAmount):F2}";
                    row += 2;

                    // 餐點統計
                    if (statistics?.Any() == true)
                    {
                        reportSheet.Cells[row, 1].Value = "餐點統計";
                        reportSheet.Cells[row, 1].Style.Font.Bold = true;
                        row++;

                        var statHeaders = new[] { "餐點名稱", "類別", "訂購數量", "剩餘庫存", "收入" };
                        for (int i = 0; i < statHeaders.Length; i++)
                        {
                            reportSheet.Cells[row, i + 1].Value = statHeaders[i];
                            reportSheet.Cells[row, i + 1].Style.Font.Bold = true;
                        }
                        row++;

                        foreach (var stat in statistics)
                        {
                            reportSheet.Cells[row, 1].Value = stat.ItemName;
                            reportSheet.Cells[row, 2].Value = stat.Category;
                            reportSheet.Cells[row, 3].Value = stat.OrderedQuantity;
                            reportSheet.Cells[row, 4].Value = stat.AvailableQuantity;
                            reportSheet.Cells[row, 5].Value = stat.Revenue;
                            row++;
                        }
                    }

                    // 建立詳細訂單工作表
                    var orderSheet = package.Workbook.Worksheets.Add("詳細訂單");
                    
                    // 訂單詳細資料
                    var orderHeaders = new[]
                    {
                        "訂單號碼", "會員姓名", "會員號", "餐點名稱", "數量", "金額", "狀態"
                    };

                    for (int i = 0; i < orderHeaders.Length; i++)
                    {
                        orderSheet.Cells[1, i + 1].Value = orderHeaders[i];
                        orderSheet.Cells[1, i + 1].Style.Font.Bold = true;
                    }

                    for (int i = 0; i < orders.Count; i++)
                    {
                        var order = orders[i];
                        var orderRow = i + 2;

                        orderSheet.Cells[orderRow, 1].Value = order.OrderNumber;
                        orderSheet.Cells[orderRow, 2].Value = order.Member.FullName;
                        orderSheet.Cells[orderRow, 3].Value = order.Member.MemberNumber;
                        orderSheet.Cells[orderRow, 4].Value = order.MealItem.ItemName;
                        orderSheet.Cells[orderRow, 5].Value = order.Quantity;
                        orderSheet.Cells[orderRow, 6].Value = order.TotalAmount;
                        orderSheet.Cells[orderRow, 7].Value = order.OrderStatusDisplayText;
                    }

                    // 自動調整欄寬
                    reportSheet.Cells.AutoFitColumns();
                    orderSheet.Cells.AutoFitColumns();

                    // 輸出文件
                    var data = package.GetAsByteArray();
                    Response.Clear();
                    Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    Response.AddHeader("Content-Disposition", $"attachment; filename=\"{fileName}\"");
                    Response.BinaryWrite(data);
                    Response.End();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"匯出每日報表錯誤：{ex.Message}", "error");
            }
        }

        private void ShowMessage(string message, string type)
        {
            string cssClass = type switch
            {
                "success" => "alert-success",
                "error" => "alert-danger",
                "warning" => "alert-warning",
                "info" => "alert-info",
                _ => "alert-info"
            };

            pnlMessage.CssClass = $"alert {cssClass}";
            ltlMessage.Text = message;
            pnlMessage.Visible = true;

            // 自動隱藏訊息
            ScriptManager.RegisterStartupScript(this, GetType(), "hideMessage",
                "setTimeout(function() { $('.alert').fadeOut(); }, 5000);", true);
        }
    }
}