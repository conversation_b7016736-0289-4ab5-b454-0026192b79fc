<%@ Page Title="膳食管理" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MealManagement.aspx.cs" Inherits="CWDECC_3S.Meals.MealManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-tasks text-primary me-2"></i>膳食管理
                </h2>
                <p class="text-muted">查看每日餐單、管理訂單及匯出報表</p>
            </div>
            <div>
                <asp:Button ID="btnNewOrder" runat="server" Text="新增訂單" CssClass="btn btn-success"
                    OnClick="btnNewOrder_Click" />
                <asp:Button ID="btnRefresh" runat="server" Text="重新載入" CssClass="btn btn-outline-primary"
                    OnClick="btnRefresh_Click" CausesValidation="false" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <div class="row">
            <!-- 左側：搜尋與篩選 -->
            <div class="col-lg-3">
                <!-- 日期選擇 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-day me-2"></i>日期篩選
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="<%= txtSearchDate.ClientID %>">查詢日期</label>
                            <asp:TextBox ID="txtSearchDate" runat="server" CssClass="form-control" 
                                TextMode="Date" AutoPostBack="true" OnTextChanged="txtSearchDate_TextChanged"></asp:TextBox>
                        </div>
                        <div class="form-group mb-3">
                            <label for="<%= ddlDateRange.ClientID %>">快速選擇</label>
                            <asp:DropDownList ID="ddlDateRange" runat="server" CssClass="form-select"
                                AutoPostBack="true" OnSelectedIndexChanged="ddlDateRange_SelectedIndexChanged">
                                <asp:ListItem Text="請選擇" Value=""></asp:ListItem>
                                <asp:ListItem Text="今天" Value="Today"></asp:ListItem>
                                <asp:ListItem Text="明天" Value="Tomorrow"></asp:ListItem>
                                <asp:ListItem Text="後天" Value="DayAfterTomorrow"></asp:ListItem>
                                <asp:ListItem Text="本週" Value="ThisWeek"></asp:ListItem>
                                <asp:ListItem Text="下週" Value="NextWeek"></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <asp:Button ID="btnSearch" runat="server" Text="搜尋" CssClass="btn btn-primary w-100"
                            OnClick="btnSearch_Click" />
                    </div>
                </div>

                <!-- 狀態篩選 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-filter me-2"></i>狀態篩選
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="<%= ddlOrderStatus.ClientID %>">訂單狀態</label>
                            <asp:DropDownList ID="ddlOrderStatus" runat="server" CssClass="form-select"
                                AutoPostBack="true" OnSelectedIndexChanged="ddlOrderStatus_SelectedIndexChanged">
                                <asp:ListItem Text="全部狀態" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="form-group mb-3">
                            <label for="<%= ddlPaymentStatus.ClientID %>">付款狀態</label>
                            <asp:DropDownList ID="ddlPaymentStatus" runat="server" CssClass="form-select"
                                AutoPostBack="true" OnSelectedIndexChanged="ddlPaymentStatus_SelectedIndexChanged">
                                <asp:ListItem Text="全部狀態" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="form-group">
                            <label for="<%= ddlMealCategory.ClientID %>">餐點類別</label>
                            <asp:DropDownList ID="ddlMealCategory" runat="server" CssClass="form-select"
                                AutoPostBack="true" OnSelectedIndexChanged="ddlMealCategory_SelectedIndexChanged">
                                <asp:ListItem Text="全部類別" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                </div>

                <!-- 匯出功能 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-download me-2"></i>匯出報表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <asp:Button ID="btnExportCSV" runat="server" Text="匯出 CSV" CssClass="btn btn-outline-success btn-sm"
                                OnClick="btnExportCSV_Click" />
                            <asp:Button ID="btnExportExcel" runat="server" Text="匯出 Excel" CssClass="btn btn-outline-info btn-sm"
                                OnClick="btnExportExcel_Click" />
                            <asp:Button ID="btnExportDailyReport" runat="server" Text="每日統計報表" CssClass="btn btn-outline-warning btn-sm"
                                OnClick="btnExportDailyReport_Click" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：訂單列表 -->
            <div class="col-lg-9">
                <!-- 統計資訊 -->
                <asp:Panel ID="pnlStatistics" runat="server" Visible="false" CssClass="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <h4 class="card-title">
                                    <asp:Label ID="lblTotalOrders" runat="server"></asp:Label>
                                </h4>
                                <p class="card-text">總訂單數</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <h4 class="card-title">
                                    <asp:Label ID="lblMainMealCount" runat="server"></asp:Label>
                                </h4>
                                <p class="card-text">主餐訂單</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <h4 class="card-title">
                                    <asp:Label ID="lblSideMealCount" runat="server"></asp:Label>
                                </h4>
                                <p class="card-text">副餐訂單</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark text-center">
                            <div class="card-body">
                                <h4 class="card-title">
                                    HK$<asp:Label ID="lblTotalRevenue" runat="server"></asp:Label>
                                </h4>
                                <p class="card-text">總金額</p>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <!-- 訂單列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>膳食訂單列表
                        </h5>
                        <div>
                            <asp:Label ID="lblSearchDate" runat="server" CssClass="badge bg-primary me-2"></asp:Label>
                            <asp:Label ID="lblRecordCount" runat="server" CssClass="badge bg-secondary"></asp:Label>
                        </div>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlOrders" runat="server" Visible="false">
                            <div class="table-responsive">
                                <asp:GridView ID="gvOrders" runat="server" CssClass="table table-hover table-striped"
                                    AutoGenerateColumns="false" DataKeyNames="Id" AllowPaging="true" PageSize="20"
                                    OnPageIndexChanging="gvOrders_PageIndexChanging" OnRowCommand="gvOrders_RowCommand"
                                    OnRowDataBound="gvOrders_RowDataBound" EmptyDataText="沒有找到符合條件的訂單">
                                    <Columns>
                                        <asp:BoundField DataField="OrderNumber" HeaderText="訂單號碼" SortExpression="OrderNumber" />
                                        <asp:BoundField DataField="Member.FullName" HeaderText="會員姓名" SortExpression="Member.FullName" />
                                        <asp:BoundField DataField="Member.MemberNumber" HeaderText="會員號" SortExpression="Member.MemberNumber" />
                                        <asp:BoundField DataField="MealItem.ItemName" HeaderText="餐點名稱" SortExpression="MealItem.ItemName" />
                                        <asp:BoundField DataField="MealItem.CategoryDisplayText" HeaderText="類別" SortExpression="MealItem.Category" />
                                        <asp:BoundField DataField="Quantity" HeaderText="數量" SortExpression="Quantity" />
                                        <asp:BoundField DataField="TotalAmount" HeaderText="金額" SortExpression="TotalAmount" DataFormatString="HK${0:F2}" />
                                        <asp:BoundField DataField="OrderStatusDisplayText" HeaderText="狀態" SortExpression="OrderStatus" />
                                        <asp:BoundField DataField="PaymentStatusDisplayText" HeaderText="付款" SortExpression="PaymentStatus" />
                                        <asp:BoundField DataField="OrderDate" HeaderText="訂購時間" SortExpression="OrderDate" DataFormatString="{0:MM/dd HH:mm}" />
                                        <asp:TemplateField HeaderText="操作" ItemStyle-Width="150px">
                                            <ItemTemplate>
                                                <asp:Button ID="btnViewDetails" runat="server" Text="詳情" CssClass="btn btn-info btn-sm me-1"
                                                    CommandName="ViewDetails" CommandArgument='<%# Eval("Id") %>' />
                                                <asp:Button ID="btnCancelOrder" runat="server" Text="取消" CssClass="btn btn-danger btn-sm"
                                                    CommandName="CancelOrder" CommandArgument='<%# Eval("Id") %>'
                                                    OnClientClick="return confirm('確定要取消此訂單嗎？');"
                                                    Visible='<%# Convert.ToBoolean(Eval("CanCancel")) %>' />
                                                <asp:Button ID="btnUpdatePayment" runat="server" Text="付款" CssClass="btn btn-success btn-sm"
                                                    CommandName="UpdatePayment" CommandArgument='<%# Eval("Id") %>'
                                                    Visible='<%# Eval("PaymentStatus").ToString() == "Pending" %>' />
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                    <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" 
                                        NextPageText="下一頁" PreviousPageText="上一頁" />
                                    <PagerStyle CssClass="pagination justify-content-center" />
                                </asp:GridView>
                            </div>
                        </asp:Panel>

                        <asp:Panel ID="pnlNoOrders" runat="server" Visible="true" CssClass="text-center py-5">
                            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">沒有找到訂單</h5>
                            <p class="text-muted">請選擇查詢日期或調整篩選條件</p>
                        </asp:Panel>
                    </div>
                </div>

                <!-- 餐點統計 -->
                <asp:Panel ID="pnlMealStatistics" runat="server" Visible="false" CssClass="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>餐點統計
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <asp:GridView ID="gvMealStatistics" runat="server" CssClass="table table-sm"
                                AutoGenerateColumns="false" EmptyDataText="沒有統計資料">
                                <Columns>
                                    <asp:BoundField DataField="ItemName" HeaderText="餐點名稱" />
                                    <asp:BoundField DataField="Category" HeaderText="類別" />
                                    <asp:BoundField DataField="OrderedQuantity" HeaderText="訂購數量" />
                                    <asp:BoundField DataField="AvailableQuantity" HeaderText="剩餘庫存" />
                                    <asp:BoundField DataField="Revenue" HeaderText="收入" DataFormatString="HK${0:F2}" />
                                </Columns>
                            </asp:GridView>
                        </div>
                    </div>
                </asp:Panel>
            </div>
        </div>
    </div>

    <!-- 訂單詳情模態視窗 -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailsModalLabel">
                        <i class="fas fa-info-circle me-2"></i>訂單詳情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:Literal ID="ltlOrderDetails" runat="server"></asp:Literal>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 付款狀態更新模態視窗 -->
    <div class="modal fade" id="paymentUpdateModal" tabindex="-1" aria-labelledby="paymentUpdateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentUpdateModalLabel">
                        <i class="fas fa-credit-card me-2"></i>更新付款狀態
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hfSelectedOrderId" runat="server" />
                    <div class="form-group mb-3">
                        <label for="<%= ddlUpdatePaymentMethod.ClientID %>">付款方式</label>
                        <asp:DropDownList ID="ddlUpdatePaymentMethod" runat="server" CssClass="form-select">
                            <asp:ListItem Text="現金" Value="Cash"></asp:ListItem>
                            <asp:ListItem Text="八達通" Value="Octopus"></asp:ListItem>
                            <asp:ListItem Text="信用卡" Value="CreditCard"></asp:ListItem>
                            <asp:ListItem Text="銀行轉賬" Value="BankTransfer"></asp:ListItem>
                        </asp:DropDownList>
                    </div>
                    <div class="form-group">
                        <label for="<%= txtPaymentRemarks.ClientID %>">付款備註</label>
                        <asp:TextBox ID="txtPaymentRemarks" runat="server" CssClass="form-control" 
                            TextMode="MultiLine" Rows="3" placeholder="選填"></asp:TextBox>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnConfirmPayment" runat="server" Text="確認付款" CssClass="btn btn-success"
                        OnClick="btnConfirmPayment_Click" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            white-space: nowrap;
        }
        
        .btn-group-sm .btn {
            margin-right: 2px;
        }
        
        .card-stats:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .order-status-ordered {
            background-color: #17a2b8;
            color: white;
        }
        
        .order-status-confirmed {
            background-color: #28a745;
            color: white;
        }
        
        .order-status-cancelled {
            background-color: #dc3545;
            color: white;
        }
        
        .payment-status-pending {
            background-color: #ffc107;
            color: black;
        }
        
        .payment-status-paid {
            background-color: #28a745;
            color: white;
        }
        
        .payment-status-cancelled {
            background-color: #6c757d;
            color: white;
        }
        
        @media print {
            .btn, .card-header, .modal, .pagination {
                display: none !important;
            }
            
            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
            
            .table th, .table td {
                border: 1px solid #000 !important;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 初始化日期選擇器
            $('#<%= txtSearchDate.ClientID %>').val(new Date().toISOString().split('T')[0]);

            // 顯示載入指示器
            window.showLoading = function(message) {
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: message || '處理中...',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });
                }
            };

            // 隱藏載入指示器
            window.hideLoading = function() {
                if (typeof Swal !== 'undefined') {
                    Swal.close();
                }
            };

            // 顯示訂單詳情模態視窗
            window.showOrderDetailsModal = function() {
                $('#orderDetailsModal').modal('show');
            };

            // 顯示付款更新模態視窗
            window.showPaymentUpdateModal = function() {
                $('#paymentUpdateModal').modal('show');
            };

            // 表格行點擊效果
            $('.table tbody tr').hover(
                function() { $(this).addClass('table-active'); },
                function() { $(this).removeClass('table-active'); }
            );
        });

        // 確認取消操作
        function confirmCancel(orderNumber) {
            return confirm('確定要取消訂單 "' + orderNumber + '" 嗎？\n\n注意：取消後將釋放庫存，此操作無法撤回。');
        }

        // 匯出前確認
        function confirmExport(type) {
            var date = $('#<%= txtSearchDate.ClientID %>').val();
            if (!date) {
                alert('請先選擇查詢日期');
                return false;
            }
            return confirm('確定要匯出 ' + date + ' 的' + type + '資料嗎？');
        }
    </script>
</asp:Content>