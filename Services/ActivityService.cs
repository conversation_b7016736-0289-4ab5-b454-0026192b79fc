using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 活動服務 - 處理活動管理、報名與狀態相關功能
    /// </summary>
    public class ActivityService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        public ActivityService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
        }

        public ActivityService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _auditService = new AuditService();
        }

        #region 活動查詢與搜尋

        /// <summary>
        /// 取得所有活動
        /// </summary>
        /// <returns>活動列表</returns>
        public async Task<List<Activity>> GetAllActivitiesAsync()
        {
            try
            {
                return await _context.Set<Activity>()
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.ActivityDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetAllActivities", "ActivityManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 根據 ID 取得活動
        /// </summary>
        /// <param name="activityId">活動 ID</param>
        /// <returns>活動資料</returns>
        public async Task<Activity> GetActivityByIdAsync(int activityId)
        {
            if (activityId <= 0)
                throw new ArgumentException("活動 ID 必須大於 0", nameof(activityId));

            try
            {
                return await _context.Set<Activity>()
                    .Include(a => a.ActivityRegistrations)
                    .FirstOrDefaultAsync(a => a.Id == activityId);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetActivityById", "ActivityManagement", ex, 
                    $"activityId: {activityId}");
                throw;
            }
        }

        /// <summary>
        /// 搜尋活動
        /// </summary>
        /// <param name="criteria">搜尋條件</param>
        /// <param name="searchUserId">搜尋用戶 ID</param>
        /// <returns>搜尋結果</returns>
        public async Task<(List<Activity> Activities, int TotalCount)> SearchActivitiesAsync(
            ActivitySearchCriteria criteria, string searchUserId)
        {
            try
            {
                var query = _context.Set<Activity>().AsQueryable();

                // 只顯示啟用的活動
                query = query.Where(a => a.IsActive);

                // 關鍵字搜尋
                if (!string.IsNullOrEmpty(criteria.Keyword))
                {
                    var keyword = criteria.Keyword.Trim().ToLower();
                    query = query.Where(a => 
                        a.ActivityName.ToLower().Contains(keyword) ||
                        a.Description.ToLower().Contains(keyword) ||
                        a.Venue.ToLower().Contains(keyword) ||
                        a.ContactPerson.ToLower().Contains(keyword)
                    );
                }

                // 狀態篩選
                if (!string.IsNullOrEmpty(criteria.Status))
                {
                    query = query.Where(a => a.Status == criteria.Status);
                }

                // 對象限制篩選
                if (!string.IsNullOrEmpty(criteria.TargetAudience))
                {
                    query = query.Where(a => a.TargetAudience == criteria.TargetAudience);
                }

                // 日期範圍篩選
                if (criteria.DateFrom.HasValue)
                {
                    query = query.Where(a => a.ActivityDate >= criteria.DateFrom.Value);
                }

                if (criteria.DateTo.HasValue)
                {
                    query = query.Where(a => a.ActivityDate <= criteria.DateTo.Value);
                }

                // 費用範圍篩選
                if (criteria.MinFee.HasValue)
                {
                    query = query.Where(a => a.ActivityFee >= criteria.MinFee.Value);
                }

                if (criteria.MaxFee.HasValue)
                {
                    query = query.Where(a => a.ActivityFee <= criteria.MaxFee.Value);
                }

                // 總數
                var totalCount = await query.CountAsync();

                // 排序
                switch (criteria.SortField?.ToLower())
                {
                    case "activityname":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(a => a.ActivityName) : 
                            query.OrderBy(a => a.ActivityName);
                        break;
                    case "status":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(a => a.Status) : 
                            query.OrderBy(a => a.Status);
                        break;
                    case "maxparticipants":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(a => a.MaxParticipants) : 
                            query.OrderBy(a => a.MaxParticipants);
                        break;
                    default:
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(a => a.ActivityDate) : 
                            query.OrderBy(a => a.ActivityDate);
                        break;
                }

                // 分頁
                var activities = await query
                    .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                    .Take(criteria.PageSize)
                    .ToListAsync();

                // 記錄搜尋審計日誌
                await _auditService.LogAsync("ACTIVITY_SEARCH", "ActivityManagement", searchUserId,
                    $"關鍵字: {criteria.Keyword} | 結果: {totalCount}筆", null, "SUCCESS");

                return (activities, totalCount);
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ACTIVITY_SEARCH", "ActivityManagement", searchUserId,
                    $"搜尋失敗: {ex.Message}", null, "ERROR");
                
                _auditService.LogError("SearchActivities", "ActivityManagement", ex, 
                    $"Criteria: {Newtonsoft.Json.JsonConvert.SerializeObject(criteria)}");
                throw;
            }
        }

        #endregion

        #region 活動新增、編輯、刪除

        /// <summary>
        /// 新增活動
        /// </summary>
        /// <param name="activity">活動資料</param>
        /// <param name="createdBy">建立者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<ActivityOperationResult> CreateActivityAsync(Activity activity, string createdBy)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    if (activity == null)
                        throw new ArgumentNullException(nameof(activity));

                    // 驗證活動資料
                    var validationErrors = activity.Validate();
                    if (validationErrors.Any())
                    {
                        return new ActivityOperationResult
                        {
                            Success = false,
                            Message = "活動資料驗證失敗",
                            Errors = validationErrors
                        };
                    }

                    // 設定建立資訊
                    activity.CreatedBy = createdBy;
                    activity.CreatedDate = DateTime.Now;
                    activity.UpdatedBy = createdBy;
                    activity.UpdatedDate = DateTime.Now;
                    activity.Status = ActivityConstants.Status.Registration;

                    _context.Set<Activity>().Add(activity);
                    await _context.SaveChangesAsync();

                    transaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("ACTIVITY_CREATE", "ActivityManagement", createdBy,
                        $"新增活動: {activity.ActivityName} | 日期: {activity.ActivityDate:yyyy-MM-dd}", 
                        activity.Id.ToString(), "SUCCESS");

                    return new ActivityOperationResult
                    {
                        Success = true,
                        Message = "活動新增成功",
                        ActivityId = activity.Id,
                        Data = activity
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    
                    await _auditService.LogAsync("ACTIVITY_CREATE", "ActivityManagement", createdBy,
                        $"新增活動失敗: {ex.Message}", null, "ERROR");
                    
                    _auditService.LogError("CreateActivity", "ActivityManagement", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 更新活動
        /// </summary>
        /// <param name="activity">活動資料</param>
        /// <param name="updatedBy">更新者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<ActivityOperationResult> UpdateActivityAsync(Activity activity, string updatedBy)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    if (activity == null)
                        throw new ArgumentNullException(nameof(activity));

                    var existingActivity = await GetActivityByIdAsync(activity.Id);
                    if (existingActivity == null)
                    {
                        return new ActivityOperationResult
                        {
                            Success = false,
                            Message = $"找不到 ID 為 {activity.Id} 的活動"
                        };
                    }

                    // 驗證活動資料
                    var validationErrors = activity.Validate();
                    if (validationErrors.Any())
                    {
                        return new ActivityOperationResult
                        {
                            Success = false,
                            Message = "活動資料驗證失敗",
                            Errors = validationErrors
                        };
                    }

                    // 更新欄位
                    existingActivity.ActivityName = activity.ActivityName;
                    existingActivity.Description = activity.Description;
                    existingActivity.ActivityDate = activity.ActivityDate;
                    existingActivity.StartTime = activity.StartTime;
                    existingActivity.EndTime = activity.EndTime;
                    existingActivity.MaxParticipants = activity.MaxParticipants;
                    existingActivity.ActivityFee = activity.ActivityFee;
                    existingActivity.TargetAudience = activity.TargetAudience;
                    existingActivity.Venue = activity.Venue;
                    existingActivity.ContactPerson = activity.ContactPerson;
                    existingActivity.ContactPhone = activity.ContactPhone;
                    existingActivity.RegistrationDeadline = activity.RegistrationDeadline;
                    existingActivity.Remarks = activity.Remarks;
                    existingActivity.UpdatedBy = updatedBy;
                    existingActivity.UpdatedDate = DateTime.Now;

                    // 更新狀態
                    existingActivity.UpdateStatus();

                    await _context.SaveChangesAsync();
                    transaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("ACTIVITY_UPDATE", "ActivityManagement", updatedBy,
                        $"更新活動: {existingActivity.ActivityName}", 
                        activity.Id.ToString(), "SUCCESS");

                    return new ActivityOperationResult
                    {
                        Success = true,
                        Message = "活動更新成功",
                        ActivityId = activity.Id,
                        Data = existingActivity
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    
                    await _auditService.LogAsync("ACTIVITY_UPDATE", "ActivityManagement", updatedBy,
                        $"更新活動失敗: {ex.Message}", activity?.Id.ToString(), "ERROR");
                    
                    _auditService.LogError("UpdateActivity", "ActivityManagement", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 刪除活動（同時刪除報名記錄）
        /// </summary>
        /// <param name="activityId">活動 ID</param>
        /// <param name="deletedBy">刪除者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<ActivityOperationResult> DeleteActivityAsync(int activityId, string deletedBy)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var activity = await GetActivityByIdAsync(activityId);
                    if (activity == null)
                    {
                        return new ActivityOperationResult
                        {
                            Success = false,
                            Message = $"找不到 ID 為 {activityId} 的活動"
                        };
                    }

                    var activityName = activity.ActivityName;

                    // 先刪除所有報名記錄
                    var registrations = await _context.Set<ActivityRegistration>()
                        .Where(r => r.ActivityId == activityId)
                        .ToListAsync();

                    var registrationCount = registrations.Count;
                    
                    if (registrations.Any())
                    {
                        _context.Set<ActivityRegistration>().RemoveRange(registrations);
                    }

                    // 再刪除活動
                    _context.Set<Activity>().Remove(activity);
                    
                    await _context.SaveChangesAsync();
                    transaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("ACTIVITY_DELETE", "ActivityManagement", deletedBy,
                        $"刪除活動: {activityName} | 同時刪除 {registrationCount} 筆報名記錄", 
                        activityId.ToString(), "SUCCESS");

                    return new ActivityOperationResult
                    {
                        Success = true,
                        Message = $"活動刪除成功，同時刪除了 {registrationCount} 筆報名記錄",
                        ActivityId = activityId
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    
                    await _auditService.LogAsync("ACTIVITY_DELETE", "ActivityManagement", deletedBy,
                        $"刪除活動失敗: {ex.Message}", activityId.ToString(), "ERROR");
                    
                    _auditService.LogError("DeleteActivity", "ActivityManagement", ex, 
                        $"activityId: {activityId}");
                    throw;
                }
            }
        }

        #endregion

        #region 狀態管理

        /// <summary>
        /// 更新所有活動狀態
        /// </summary>
        /// <param name="updatedBy">更新者 ID</param>
        /// <returns>更新的活動數量</returns>
        public async Task<int> UpdateAllActivityStatusesAsync(string updatedBy)
        {
            try
            {
                var activities = await _context.Set<Activity>()
                    .Where(a => a.IsActive)
                    .ToListAsync();

                var updatedCount = 0;

                foreach (var activity in activities)
                {
                    var oldStatus = activity.Status;
                    activity.UpdateStatus();
                    
                    if (oldStatus != activity.Status)
                    {
                        activity.UpdatedBy = updatedBy;
                        activity.UpdatedDate = DateTime.Now;
                        updatedCount++;
                    }
                }

                if (updatedCount > 0)
                {
                    await _context.SaveChangesAsync();

                    // 記錄審計日誌
                    await _auditService.LogAsync("UPDATE_ACTIVITY_STATUS", "ActivityManagement", updatedBy,
                        $"批量更新活動狀態，共 {updatedCount} 個活動", null, "SUCCESS");
                }

                return updatedCount;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("UPDATE_ACTIVITY_STATUS", "ActivityManagement", updatedBy,
                    $"批量更新活動狀態失敗: {ex.Message}", null, "ERROR");
                
                _auditService.LogError("UpdateAllActivityStatuses", "ActivityManagement", ex);
                throw;
            }
        }

        #endregion

        #region 統計與報表

        /// <summary>
        /// 取得活動統計資訊
        /// </summary>
        /// <returns>統計資訊</returns>
        public async Task<ActivityStatistics> GetActivityStatisticsAsync()
        {
            try
            {
                var activities = await _context.Set<Activity>()
                    .Where(a => a.IsActive)
                    .ToListAsync();

                var today = DateTime.Today;

                return new ActivityStatistics
                {
                    TotalActivities = activities.Count,
                    UpcomingActivities = activities.Count(a => a.ActivityDate >= today),
                    OngoingRegistrations = activities.Count(a => a.Status == ActivityConstants.Status.Registration),
                    CompletedActivities = activities.Count(a => a.IsCompleted),
                    TotalParticipants = activities.Sum(a => a.CurrentParticipants),
                    TotalRevenue = activities.Where(a => a.ActivityFee.HasValue).Sum(a => a.ActivityFee.Value * a.CurrentParticipants),
                    FullActivities = activities.Count(a => a.IsFull),
                    AverageParticipationRate = activities.Count > 0 ? 
                        activities.Where(a => a.MaxParticipants > 0).Average(a => (double)a.CurrentParticipants / a.MaxParticipants * 100) : 0
                };
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetActivityStatistics", "ActivityManagement", ex);
                throw;
            }
        }

        #endregion

        #region IDisposable

        private bool _disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                _auditService?.Dispose();
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}