using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 角色管理服務 - 處理角色的增刪修查與用戶角色分配
    /// </summary>
    public class RoleManagementService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly AuditService _auditService;

        public RoleManagementService()
        {
            _context = new ApplicationDbContext();
            _roleManager = new RoleManager<ApplicationRole>(new RoleStore<ApplicationRole>(_context));
            _userManager = new UserManager<ApplicationUser>(new UserStore<ApplicationUser>(_context));
            _auditService = new AuditService();
        }

        public RoleManagementService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _roleManager = new RoleManager<ApplicationRole>(new RoleStore<ApplicationRole>(_context));
            _userManager = new UserManager<ApplicationUser>(new UserStore<ApplicationUser>(_context));
            _auditService = new AuditService();
        }

        #region 角色管理

        /// <summary>
        /// 取得所有角色
        /// </summary>
        /// <param name="includeInactive">是否包含停用的角色</param>
        /// <returns>角色清單</returns>
        public List<ApplicationRole> GetAllRoles(bool includeInactive = false)
        {
            try
            {
                var query = _context.ApplicationRoles.AsQueryable();

                if (!includeInactive)
                {
                    query = query.Where(r => r.IsActive);
                }

                return query.OrderBy(r => r.Name).ToList();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetAllRoles", "RoleManagement", ex, 
                    $"includeInactive: {includeInactive}");
                throw;
            }
        }

        /// <summary>
        /// 根據 ID 取得角色
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <returns>角色物件</returns>
        public ApplicationRole GetRoleById(string roleId)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            try
            {
                return _context.ApplicationRoles.FirstOrDefault(r => r.Id == roleId);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetRoleById", "RoleManagement", ex, 
                    $"roleId: {roleId}");
                throw;
            }
        }

        /// <summary>
        /// 根據名稱取得角色
        /// </summary>
        /// <param name="roleName">角色名稱</param>
        /// <returns>角色物件</returns>
        public ApplicationRole GetRoleByName(string roleName)
        {
            if (string.IsNullOrEmpty(roleName))
                throw new ArgumentException("角色名稱不能為空", nameof(roleName));

            try
            {
                return _context.ApplicationRoles.FirstOrDefault(r => r.Name == roleName);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetRoleByName", "RoleManagement", ex, 
                    $"roleName: {roleName}");
                throw;
            }
        }

        /// <summary>
        /// 建立新角色
        /// </summary>
        /// <param name="roleName">角色名稱</param>
        /// <param name="description">角色描述</param>
        /// <param name="createdBy">建立者用戶 ID</param>
        /// <returns>建立結果</returns>
        public async Task<IdentityResult> CreateRoleAsync(string roleName, string description, string createdBy)
        {
            if (string.IsNullOrEmpty(roleName))
                throw new ArgumentException("角色名稱不能為空", nameof(roleName));

            if (string.IsNullOrEmpty(createdBy))
                throw new ArgumentException("建立者不能為空", nameof(createdBy));

            try
            {
                // 檢查角色是否已存在
                if (await _roleManager.RoleExistsAsync(roleName))
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleExists", 
                        Description = $"角色 '{roleName}' 已存在" 
                    });
                }

                var role = new ApplicationRole(roleName)
                {
                    Description = description,
                    CreatedBy = createdBy,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };

                var result = await _roleManager.CreateAsync(role);

                if (result.Succeeded)
                {
                    await _auditService.LogAsync("CREATE_ROLE", "RoleManagement", createdBy,
                        $"建立角色: {roleName}", role.Id, "SUCCESS");
                }
                else
                {
                    await _auditService.LogAsync("CREATE_ROLE", "RoleManagement", createdBy,
                        $"建立角色失敗: {roleName} - {string.Join(", ", result.Errors.Select(e => e.Description))}", 
                        null, "FAILED");
                }

                return result;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("CREATE_ROLE", "RoleManagement", createdBy,
                    $"建立角色異常: {roleName} - {ex.Message}", null, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="roleName">新的角色名稱</param>
        /// <param name="description">新的角色描述</param>
        /// <param name="isActive">是否啟用</param>
        /// <param name="modifiedBy">修改者用戶 ID</param>
        /// <returns>更新結果</returns>
        public async Task<IdentityResult> UpdateRoleAsync(string roleId, string roleName, string description, 
            bool isActive, string modifiedBy)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(roleName))
                throw new ArgumentException("角色名稱不能為空", nameof(roleName));

            if (string.IsNullOrEmpty(modifiedBy))
                throw new ArgumentException("修改者不能為空", nameof(modifiedBy));

            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleNotFound", 
                        Description = "找不到指定的角色" 
                    });
                }

                // 檢查新名稱是否與其他角色重複
                if (role.Name != roleName)
                {
                    var existingRole = await _roleManager.FindByNameAsync(roleName);
                    if (existingRole != null && existingRole.Id != roleId)
                    {
                        return IdentityResult.Failed(new IdentityError 
                        { 
                            Code = "RoleNameExists", 
                            Description = $"角色名稱 '{roleName}' 已存在" 
                        });
                    }
                }

                var oldData = $"Name: {role.Name}, Description: {role.Description}, IsActive: {role.IsActive}";

                role.Name = roleName;
                role.Description = description;
                role.IsActive = isActive;
                role.ModifiedBy = modifiedBy;
                role.ModifiedDate = DateTime.UtcNow;

                var result = await _roleManager.UpdateAsync(role);

                if (result.Succeeded)
                {
                    var newData = $"Name: {roleName}, Description: {description}, IsActive: {isActive}";
                    await _auditService.LogAsync("UPDATE_ROLE", "RoleManagement", modifiedBy,
                        $"更新角色: {roleId} | 舊值: {oldData} | 新值: {newData}", roleId, "SUCCESS");
                }
                else
                {
                    await _auditService.LogAsync("UPDATE_ROLE", "RoleManagement", modifiedBy,
                        $"更新角色失敗: {roleId} - {string.Join(", ", result.Errors.Select(e => e.Description))}", 
                        roleId, "FAILED");
                }

                return result;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("UPDATE_ROLE", "RoleManagement", modifiedBy,
                    $"更新角色異常: {roleId} - {ex.Message}", roleId, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 刪除角色
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="deletedBy">刪除者用戶 ID</param>
        /// <param name="forceDelete">是否強制刪除（即使有用戶使用此角色）</param>
        /// <returns>刪除結果</returns>
        public async Task<IdentityResult> DeleteRoleAsync(string roleId, string deletedBy, bool forceDelete = false)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(deletedBy))
                throw new ArgumentException("刪除者不能為空", nameof(deletedBy));

            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleNotFound", 
                        Description = "找不到指定的角色" 
                    });
                }

                // 檢查是否有用戶正在使用此角色
                var usersInRole = await GetUsersInRoleAsync(roleId);
                if (usersInRole.Any() && !forceDelete)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleInUse", 
                        Description = $"角色 '{role.Name}' 正被 {usersInRole.Count} 個用戶使用，無法刪除" 
                    });
                }

                // 檢查是否為系統內建角色
                var systemRoles = new[] { "Administrator", "StaffMember", "Teacher", "Volunteer", "Member", "Guest" };
                if (systemRoles.Contains(role.Name))
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "SystemRole", 
                        Description = "無法刪除系統內建角色" 
                    });
                }

                // 如果強制刪除，先移除所有用戶的角色關聯
                if (forceDelete && usersInRole.Any())
                {
                    foreach (var user in usersInRole)
                    {
                        await _userManager.RemoveFromRoleAsync(user.Id, role.Name);
                    }
                }

                // 刪除角色相關的權限記錄
                var rolePermissions = _context.RolePermissions.Where(rp => rp.RoleId == roleId);
                _context.RolePermissions.RemoveRange(rolePermissions);

                var result = await _roleManager.DeleteAsync(role);

                if (result.Succeeded)
                {
                    await _auditService.LogAsync("DELETE_ROLE", "RoleManagement", deletedBy,
                        $"刪除角色: {role.Name} (ID: {roleId}), 強制刪除: {forceDelete}, 影響用戶數: {usersInRole.Count}", 
                        roleId, "SUCCESS");
                }
                else
                {
                    await _auditService.LogAsync("DELETE_ROLE", "RoleManagement", deletedBy,
                        $"刪除角色失敗: {role.Name} - {string.Join(", ", result.Errors.Select(e => e.Description))}", 
                        roleId, "FAILED");
                }

                return result;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("DELETE_ROLE", "RoleManagement", deletedBy,
                    $"刪除角色異常: {roleId} - {ex.Message}", roleId, "ERROR");
                throw;
            }
        }

        #endregion

        #region 用戶角色管理

        /// <summary>
        /// 取得角色中的所有用戶
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <returns>用戶清單</returns>
        public async Task<List<ApplicationUser>> GetUsersInRoleAsync(string roleId)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                    return new List<ApplicationUser>();

                var userIds = _context.ApplicationUserRoles
                    .Where(ur => ur.RoleId == roleId)
                    .Select(ur => ur.UserId)
                    .ToList();

                return _context.Users
                    .Where(u => userIds.Contains(u.Id))
                    .OrderBy(u => u.DisplayName ?? u.UserName)
                    .ToList();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetUsersInRole", "RoleManagement", ex, 
                    $"roleId: {roleId}");
                throw;
            }
        }

        /// <summary>
        /// 取得用戶的所有角色
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <returns>角色清單</returns>
        public async Task<List<ApplicationRole>> GetUserRolesAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentException("用戶 ID 不能為空", nameof(userId));

            try
            {
                var roleIds = _context.ApplicationUserRoles
                    .Where(ur => ur.UserId == userId)
                    .Select(ur => ur.RoleId)
                    .ToList();

                return _context.ApplicationRoles
                    .Where(r => roleIds.Contains(r.Id) && r.IsActive)
                    .OrderBy(r => r.Name)
                    .ToList();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetUserRoles", "RoleManagement", ex, 
                    $"userId: {userId}");
                throw;
            }
        }

        /// <summary>
        /// 為用戶分配角色
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="roleId">角色 ID</param>
        /// <param name="assignedBy">分配者用戶 ID</param>
        /// <returns>分配結果</returns>
        public async Task<IdentityResult> AssignRoleToUserAsync(string userId, string roleId, string assignedBy)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentException("用戶 ID 不能為空", nameof(userId));

            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(assignedBy))
                throw new ArgumentException("分配者不能為空", nameof(assignedBy));

            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "UserNotFound", 
                        Description = "找不到指定的用戶" 
                    });
                }

                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleNotFound", 
                        Description = "找不到指定的角色" 
                    });
                }

                if (!role.IsActive)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleInactive", 
                        Description = "無法分配已停用的角色" 
                    });
                }

                // 檢查用戶是否已有此角色
                if (await _userManager.IsInRoleAsync(userId, role.Name))
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "UserAlreadyInRole", 
                        Description = $"用戶已擁有角色 '{role.Name}'" 
                    });
                }

                var result = await _userManager.AddToRoleAsync(userId, role.Name);

                if (result.Succeeded)
                {
                    // 更新 ApplicationUserRole 記錄
                    var userRole = new ApplicationUserRole
                    {
                        UserId = userId,
                        RoleId = roleId,
                        AssignedBy = assignedBy,
                        AssignedDate = DateTime.UtcNow
                    };

                    _context.ApplicationUserRoles.Add(userRole);
                    await _context.SaveChangesAsync();

                    await _auditService.LogAsync("ASSIGN_ROLE", "RoleManagement", assignedBy,
                        $"分配角色: {role.Name} 給用戶 {user.UserName}", userId, "SUCCESS");
                }
                else
                {
                    await _auditService.LogAsync("ASSIGN_ROLE", "RoleManagement", assignedBy,
                        $"分配角色失敗: {role.Name} 給用戶 {user.UserName} - {string.Join(", ", result.Errors.Select(e => e.Description))}", 
                        userId, "FAILED");
                }

                return result;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ASSIGN_ROLE", "RoleManagement", assignedBy,
                    $"分配角色異常: roleId {roleId} 給用戶 {userId} - {ex.Message}", userId, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 移除用戶的角色
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="roleId">角色 ID</param>
        /// <param name="removedBy">移除者用戶 ID</param>
        /// <returns>移除結果</returns>
        public async Task<IdentityResult> RemoveRoleFromUserAsync(string userId, string roleId, string removedBy)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentException("用戶 ID 不能為空", nameof(userId));

            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(removedBy))
                throw new ArgumentException("移除者不能為空", nameof(removedBy));

            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "UserNotFound", 
                        Description = "找不到指定的用戶" 
                    });
                }

                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "RoleNotFound", 
                        Description = "找不到指定的角色" 
                    });
                }

                // 檢查用戶是否有此角色
                if (!await _userManager.IsInRoleAsync(userId, role.Name))
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "UserNotInRole", 
                        Description = $"用戶沒有角色 '{role.Name}'" 
                    });
                }

                // 檢查是否為用戶的最後一個角色
                var userRoles = await GetUserRolesAsync(userId);
                if (userRoles.Count == 1 && userRoles.First().Id == roleId)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "LastRole", 
                        Description = "無法移除用戶的最後一個角色" 
                    });
                }

                var result = await _userManager.RemoveFromRoleAsync(userId, role.Name);

                if (result.Succeeded)
                {
                    // 移除 ApplicationUserRole 記錄
                    var userRole = _context.ApplicationUserRoles
                        .FirstOrDefault(ur => ur.UserId == userId && ur.RoleId == roleId);
                    if (userRole != null)
                    {
                        _context.ApplicationUserRoles.Remove(userRole);
                        await _context.SaveChangesAsync();
                    }

                    await _auditService.LogAsync("REMOVE_ROLE", "RoleManagement", removedBy,
                        $"移除角色: {role.Name} 從用戶 {user.UserName}", userId, "SUCCESS");
                }
                else
                {
                    await _auditService.LogAsync("REMOVE_ROLE", "RoleManagement", removedBy,
                        $"移除角色失敗: {role.Name} 從用戶 {user.UserName} - {string.Join(", ", result.Errors.Select(e => e.Description))}", 
                        userId, "FAILED");
                }

                return result;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("REMOVE_ROLE", "RoleManagement", removedBy,
                    $"移除角色異常: roleId {roleId} 從用戶 {userId} - {ex.Message}", userId, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 批量更新用戶角色
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="newRoleIds">新的角色 ID 清單</param>
        /// <param name="updatedBy">更新者用戶 ID</param>
        /// <returns>更新結果</returns>
        public async Task<IdentityResult> UpdateUserRolesAsync(string userId, List<string> newRoleIds, string updatedBy)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentException("用戶 ID 不能為空", nameof(userId));

            if (newRoleIds == null || !newRoleIds.Any())
                throw new ArgumentException("至少需要指定一個角色", nameof(newRoleIds));

            if (string.IsNullOrEmpty(updatedBy))
                throw new ArgumentException("更新者不能為空", nameof(updatedBy));

            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "UserNotFound", 
                        Description = "找不到指定的用戶" 
                    });
                }

                // 取得當前角色
                var currentRoles = await GetUserRolesAsync(userId);
                var currentRoleIds = currentRoles.Select(r => r.Id).ToList();

                // 計算要新增和移除的角色
                var rolesToAdd = newRoleIds.Except(currentRoleIds).ToList();
                var rolesToRemove = currentRoleIds.Except(newRoleIds).ToList();

                var errors = new List<IdentityError>();

                // 移除角色
                foreach (var roleId in rolesToRemove)
                {
                    var result = await RemoveRoleFromUserAsync(userId, roleId, updatedBy);
                    if (!result.Succeeded)
                    {
                        errors.AddRange(result.Errors);
                    }
                }

                // 新增角色
                foreach (var roleId in rolesToAdd)
                {
                    var result = await AssignRoleToUserAsync(userId, roleId, updatedBy);
                    if (!result.Succeeded)
                    {
                        errors.AddRange(result.Errors);
                    }
                }

                if (errors.Any())
                {
                    return IdentityResult.Failed(errors.ToArray());
                }

                await _auditService.LogAsync("UPDATE_USER_ROLES", "RoleManagement", updatedBy,
                    $"更新用戶 {user.UserName} 角色 | 新增: {string.Join(",", rolesToAdd)} | 移除: {string.Join(",", rolesToRemove)}", 
                    userId, "SUCCESS");

                return IdentityResult.Success;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("UPDATE_USER_ROLES", "RoleManagement", updatedBy,
                    $"更新用戶角色異常: {userId} - {ex.Message}", userId, "ERROR");
                throw;
            }
        }

        #endregion

        #region 角色統計和驗證

        /// <summary>
        /// 取得角色統計資訊
        /// </summary>
        /// <returns>角色統計</returns>
        public async Task<RoleStatistics> GetRoleStatisticsAsync()
        {
            try
            {
                var statistics = new RoleStatistics
                {
                    TotalRoles = await _context.ApplicationRoles.CountAsync(),
                    ActiveRoles = await _context.ApplicationRoles.CountAsync(r => r.IsActive),
                    InactiveRoles = await _context.ApplicationRoles.CountAsync(r => !r.IsActive),
                    SystemRoles = await _context.ApplicationRoles.CountAsync(r => 
                        r.Name == "Administrator" || r.Name == "StaffMember" || 
                        r.Name == "Teacher" || r.Name == "Volunteer" || 
                        r.Name == "Member" || r.Name == "Guest"),
                    CustomRoles = await _context.ApplicationRoles.CountAsync(r => 
                        r.Name != "Administrator" && r.Name != "StaffMember" && 
                        r.Name != "Teacher" && r.Name != "Volunteer" && 
                        r.Name != "Member" && r.Name != "Guest")
                };

                // 計算每個角色的用戶數量
                statistics.RoleUserCounts = await _context.ApplicationRoles
                    .Where(r => r.IsActive)
                    .Select(r => new RoleUserCount
                    {
                        RoleId = r.Id,
                        RoleName = r.Name,
                        UserCount = _context.ApplicationUserRoles.Count(ur => ur.RoleId == r.Id)
                    })
                    .ToListAsync();

                return statistics;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetRoleStatistics", "RoleManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 驗證角色名稱是否可用
        /// </summary>
        /// <param name="roleName">角色名稱</param>
        /// <param name="excludeRoleId">排除的角色 ID（更新時使用）</param>
        /// <returns>是否可用</returns>
        public async Task<bool> IsRoleNameAvailableAsync(string roleName, string excludeRoleId = null)
        {
            if (string.IsNullOrEmpty(roleName))
                return false;

            try
            {
                var query = _context.ApplicationRoles.Where(r => r.Name == roleName);

                if (!string.IsNullOrEmpty(excludeRoleId))
                {
                    query = query.Where(r => r.Id != excludeRoleId);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("IsRoleNameAvailable", "RoleManagement", ex, 
                    $"roleName: {roleName}, excludeRoleId: {excludeRoleId}");
                return false;
            }
        }

        #endregion

        #region IDisposable

        private bool _disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                _roleManager?.Dispose();
                _userManager?.Dispose();
                _auditService?.Dispose();
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }

    #region 輔助類別

    /// <summary>
    /// 角色統計資訊
    /// </summary>
    public class RoleStatistics
    {
        public int TotalRoles { get; set; }
        public int ActiveRoles { get; set; }
        public int InactiveRoles { get; set; }
        public int SystemRoles { get; set; }
        public int CustomRoles { get; set; }
        public List<RoleUserCount> RoleUserCounts { get; set; } = new List<RoleUserCount>();
    }

    /// <summary>
    /// 角色用戶數量統計
    /// </summary>
    public class RoleUserCount
    {
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public int UserCount { get; set; }
    }

    #endregion
}