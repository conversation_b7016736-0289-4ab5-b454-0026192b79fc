using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;
using System.Linq;
using CWDECC_3S.Models;
using CWDECC_3S.Data;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 安全服務 - 暴力破解防護和安全監控
    /// </summary>
    public class SecurityService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        // 暴力破解防護參數
        private const int MAX_LOGIN_ATTEMPTS_PER_USER = 5;     // 用戶層面：5次失敗鎖定帳戶
        private const int MAX_LOGIN_ATTEMPTS_PER_IP = 10;      // IP層面：10次失敗鎖定IP
        private const int LOCKOUT_DURATION_MINUTES = 30;      // 鎖定時間：30分鐘
        private const int IP_LOCKOUT_DURATION_MINUTES = 60;   // IP鎖定時間：60分鐘
        private const int SUSPICIOUS_ACTIVITY_THRESHOLD = 20; // 可疑活動閾值

        public SecurityService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
        }

        #region 暴力破解防護

        /// <summary>
        /// 檢查用戶是否被鎖定
        /// </summary>
        public async Task<UserLockoutStatus> CheckUserLockoutAsync(string userName)
        {
            try
            {
                var user = await FindUserByNameOrEmailAsync(userName);
                if (user == null)
                {
                    return new UserLockoutStatus 
                    { 
                        IsLocked = false, 
                        Message = "用戶不存在" 
                    };
                }

                if (user.IsLockedOut)
                {
                    var remainingTime = user.LockoutEndDateUtc?.Subtract(DateTime.UtcNow);
                    return new UserLockoutStatus
                    {
                        IsLocked = true,
                        Message = $"帳戶已被鎖定，剩餘時間：{remainingTime?.Minutes ?? 0} 分鐘",
                        LockoutEndTime = user.LockoutEndDateUtc,
                        FailedAttempts = user.AccessFailedCount
                    };
                }

                return new UserLockoutStatus
                {
                    IsLocked = false,
                    FailedAttempts = user.AccessFailedCount,
                    RemainingAttempts = MAX_LOGIN_ATTEMPTS_PER_USER - user.AccessFailedCount
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", null, userName, GetClientIP(), 
                    $"檢查用戶鎖定狀態錯誤: {ex.Message}", GetUserAgent());
                throw;
            }
        }

        /// <summary>
        /// 檢查 IP 是否被封鎖
        /// </summary>
        public async Task<IPLockoutStatus> CheckIPLockoutAsync(string ipAddress)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.AddMinutes(-IP_LOCKOUT_DURATION_MINUTES);
                
                // 查詢該 IP 在指定時間內的失敗登入次數
                var failedLoginCount = await _auditService.GetFailedLoginCountAsync(ipAddress, cutoffTime);

                // 檢查是否有活躍的 IP 封鎖記錄
                var activeBlocks = await GetActiveIPBlocksAsync(ipAddress);

                if (activeBlocks.Any() || failedLoginCount >= MAX_LOGIN_ATTEMPTS_PER_IP)
                {
                    // 如果沒有現有封鎖記錄但達到閾值，創建新的封鎖記錄
                    if (!activeBlocks.Any() && failedLoginCount >= MAX_LOGIN_ATTEMPTS_PER_IP)
                    {
                        await CreateIPBlockAsync(ipAddress, $"自動封鎖 - {IP_LOCKOUT_DURATION_MINUTES}分鐘內失敗登入{failedLoginCount}次");
                    }

                    var blockEndTime = DateTime.UtcNow.AddMinutes(IP_LOCKOUT_DURATION_MINUTES);
                    var remainingTime = blockEndTime.Subtract(DateTime.UtcNow);

                    return new IPLockoutStatus
                    {
                        IsBlocked = true,
                        Message = $"IP 已被封鎖，剩餘時間：{remainingTime.Minutes} 分鐘",
                        BlockEndTime = blockEndTime,
                        FailedAttempts = failedLoginCount,
                        Reason = $"在 {IP_LOCKOUT_DURATION_MINUTES} 分鐘內嘗試登入失敗 {failedLoginCount} 次"
                    };
                }

                return new IPLockoutStatus
                {
                    IsBlocked = false,
                    FailedAttempts = failedLoginCount,
                    RemainingAttempts = MAX_LOGIN_ATTEMPTS_PER_IP - failedLoginCount
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", null, null, ipAddress, 
                    $"檢查 IP 封鎖狀態錯誤: {ex.Message}", GetUserAgent());
                throw;
            }
        }

        /// <summary>
        /// 記錄失敗的登入嘗試
        /// </summary>
        public async Task RecordFailedLoginAsync(string userName, string ipAddress, string reason)
        {
            try
            {
                var user = await FindUserByNameOrEmailAsync(userName);
                
                if (user != null)
                {
                    // 增加用戶失敗計數
                    user.RecordFailedLogin(ipAddress);
                    
                    // 如果達到鎖定閾值，鎖定用戶
                    if (user.AccessFailedCount >= MAX_LOGIN_ATTEMPTS_PER_USER)
                    {
                        user.LockoutEndDateUtc = DateTime.UtcNow.AddMinutes(LOCKOUT_DURATION_MINUTES);
                        
                        await _auditService.LogUserLockoutAsync(user.Id, user.UserName, ipAddress, 
                            GetUserAgent(), $"連續登入失敗{user.AccessFailedCount}次");
                    }

                    // 更新用戶記錄
                    _context.Entry(user).State = System.Data.Entity.EntityState.Modified;
                    await _context.SaveChangesAsync();
                }

                // 記錄失敗登入
                await _auditService.LogLoginFailureAsync(user?.Id, userName, ipAddress, GetUserAgent(), reason);

                // 檢查是否需要封鎖 IP
                await CheckAndBlockSuspiciousIPAsync(ipAddress);
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", user?.Id, userName, ipAddress, 
                    $"記錄失敗登入錯誤: {ex.Message}", GetUserAgent());
                throw;
            }
        }

        /// <summary>
        /// 重設用戶登入失敗計數
        /// </summary>
        public async Task ResetUserFailedAttemptsAsync(string userName)
        {
            try
            {
                var user = await FindUserByNameOrEmailAsync(userName);
                if (user != null)
                {
                    user.ResetAccessFailedCount();
                    _context.Entry(user).State = System.Data.Entity.EntityState.Modified;
                    await _context.SaveChangesAsync();

                    await _auditService.LogAsync("USER_UNLOCK", user.Id, user.UserName, GetClientIP(), 
                        "用戶登入失敗計數已重設", GetUserAgent());
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", null, userName, GetClientIP(), 
                    $"重設用戶失敗計數錯誤: {ex.Message}", GetUserAgent());
                throw;
            }
        }

        #endregion

        #region IP 封鎖管理

        /// <summary>
        /// 檢查並封鎖可疑 IP
        /// </summary>
        private async Task CheckAndBlockSuspiciousIPAsync(string ipAddress)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.AddMinutes(-IP_LOCKOUT_DURATION_MINUTES);
                var failedCount = await _auditService.GetFailedLoginCountAsync(ipAddress, cutoffTime);

                if (failedCount >= MAX_LOGIN_ATTEMPTS_PER_IP)
                {
                    await CreateIPBlockAsync(ipAddress, $"自動封鎖 - {IP_LOCKOUT_DURATION_MINUTES}分鐘內失敗登入{failedCount}次");
                    
                    await _auditService.LogSecurityEventAsync(null, null, ipAddress, GetUserAgent(), 
                        "IP_AUTO_BLOCKED", $"IP 自動封鎖 - 失敗登入次數：{failedCount}");
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", null, null, ipAddress, 
                    $"檢查可疑 IP 錯誤: {ex.Message}", GetUserAgent());
            }
        }

        /// <summary>
        /// 創建 IP 封鎖記錄
        /// </summary>
        private async Task CreateIPBlockAsync(string ipAddress, string reason)
        {
            try
            {
                var blockRecord = new IPBlockRecord
                {
                    IPAddress = ipAddress,
                    BlockedAt = DateTime.UtcNow,
                    BlockedUntil = DateTime.UtcNow.AddMinutes(IP_LOCKOUT_DURATION_MINUTES),
                    Reason = reason,
                    IsActive = true,
                    CreatedBy = "SYSTEM"
                };

                _context.IPBlockRecords.Add(blockRecord);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", null, null, ipAddress, 
                    $"創建 IP 封鎖記錄錯誤: {ex.Message}", GetUserAgent());
            }
        }

        /// <summary>
        /// 取得活躍的 IP 封鎖記錄
        /// </summary>
        private async Task<List<IPBlockRecord>> GetActiveIPBlocksAsync(string ipAddress)
        {
            try
            {
                var now = DateTime.UtcNow;
                return _context.IPBlockRecords
                    .Where(b => b.IPAddress == ipAddress && 
                               b.IsActive && 
                               b.BlockedUntil > now)
                    .ToList();
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", null, null, ipAddress, 
                    $"取得 IP 封鎖記錄錯誤: {ex.Message}", GetUserAgent());
                return new List<IPBlockRecord>();
            }
        }

        /// <summary>
        /// 解除 IP 封鎖
        /// </summary>
        public async Task<bool> UnblockIPAsync(string ipAddress, string adminUserId)
        {
            try
            {
                var activeBlocks = await GetActiveIPBlocksAsync(ipAddress);
                var unblocked = false;

                foreach (var block in activeBlocks)
                {
                    block.IsActive = false;
                    block.UnblockedAt = DateTime.UtcNow;
                    block.UnblockedBy = adminUserId;
                    _context.Entry(block).State = System.Data.Entity.EntityState.Modified;
                    unblocked = true;
                }

                if (unblocked)
                {
                    await _context.SaveChangesAsync();
                    await _auditService.LogAsync("IP_UNBLOCKED", adminUserId, null, ipAddress, 
                        $"管理員手動解除 IP 封鎖", GetUserAgent());
                }

                return unblocked;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", adminUserId, null, ipAddress, 
                    $"解除 IP 封鎖錯誤: {ex.Message}", GetUserAgent());
                return false;
            }
        }

        #endregion

        #region 安全監控

        /// <summary>
        /// 檢測可疑活動
        /// </summary>
        public async Task<SuspiciousActivityReport> DetectSuspiciousActivityAsync(string ipAddress = null, string userId = null)
        {
            try
            {
                var report = new SuspiciousActivityReport
                {
                    IPAddress = ipAddress,
                    UserId = userId,
                    DetectionTime = DateTime.UtcNow,
                    Activities = new List<SuspiciousActivity>()
                };

                var cutoffTime = DateTime.UtcNow.AddHours(-1); // 檢查過去1小時

                // 檢查登入失敗模式
                await DetectFailedLoginPatternsAsync(report, cutoffTime);

                // 檢查異常存取模式
                await DetectAbnormalAccessPatternsAsync(report, cutoffTime);

                // 檢查多重帳戶嘗試
                await DetectMultipleAccountAttemptsAsync(report, cutoffTime);

                // 計算風險分數
                report.RiskScore = CalculateRiskScore(report.Activities);

                // 如果風險分數高，記錄安全事件
                if (report.RiskScore >= 70)
                {
                    await _auditService.LogSecurityEventAsync(userId, null, ipAddress, GetUserAgent(),
                        "HIGH_RISK_ACTIVITY", $"檢測到高風險活動，風險分數：{report.RiskScore}");
                }

                return report;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SECURITY_ERROR", userId, null, ipAddress, 
                    $"檢測可疑活動錯誤: {ex.Message}", GetUserAgent());
                throw;
            }
        }

        private async Task DetectFailedLoginPatternsAsync(SuspiciousActivityReport report, DateTime cutoffTime)
        {
            var failedLogins = await _auditService.GetActionAuditLogsAsync("LOGIN_FAILED", cutoffTime);
            
            if (report.IPAddress != null)
            {
                var ipFailures = failedLogins.Where(log => log.IPAddress == report.IPAddress).Count();
                if (ipFailures >= 5)
                {
                    report.Activities.Add(new SuspiciousActivity
                    {
                        Type = "RAPID_FAILED_LOGINS",
                        Description = $"1小時內失敗登入{ipFailures}次",
                        RiskLevel = ipFailures >= 10 ? "High" : "Medium",
                        Count = ipFailures
                    });
                }
            }

            if (report.UserId != null)
            {
                var userFailures = failedLogins.Where(log => log.UserId == report.UserId).Count();
                if (userFailures >= 3)
                {
                    report.Activities.Add(new SuspiciousActivity
                    {
                        Type = "USER_ACCOUNT_ATTACK",
                        Description = $"用戶帳戶1小時內失敗登入{userFailures}次",
                        RiskLevel = userFailures >= 5 ? "High" : "Medium",
                        Count = userFailures
                    });
                }
            }
        }

        private async Task DetectAbnormalAccessPatternsAsync(SuspiciousActivityReport report, DateTime cutoffTime)
        {
            if (report.IPAddress == null) return;

            // 檢查異常時間存取（如深夜時段）
            var currentHour = DateTime.Now.Hour;
            if (currentHour >= 0 && currentHour <= 5) // 凌晨0點到5點
            {
                var nightAccess = await _auditService.GetActionAuditLogsAsync("LOGIN_SUCCESS", cutoffTime);
                var ipNightAccess = nightAccess.Where(log => log.IPAddress == report.IPAddress).Count();
                
                if (ipNightAccess > 0)
                {
                    report.Activities.Add(new SuspiciousActivity
                    {
                        Type = "OFF_HOURS_ACCESS",
                        Description = $"非正常時間存取（深夜{currentHour}點）",
                        RiskLevel = "Medium",
                        Count = ipNightAccess
                    });
                }
            }

            // 檢查快速連續存取
            var allAccess = await _auditService.GetUserAuditLogsAsync(report.UserId, cutoffTime);
            if (allAccess.Count >= SUSPICIOUS_ACTIVITY_THRESHOLD)
            {
                report.Activities.Add(new SuspiciousActivity
                {
                    Type = "RAPID_REQUESTS",
                    Description = $"1小時內快速連續操作{allAccess.Count}次",
                    RiskLevel = allAccess.Count >= 50 ? "High" : "Medium",
                    Count = allAccess.Count
                });
            }
        }

        private async Task DetectMultipleAccountAttemptsAsync(SuspiciousActivityReport report, DateTime cutoffTime)
        {
            if (report.IPAddress == null) return;

            // 檢查同一 IP 嘗試多個不同帳戶
            var loginAttempts = await _auditService.GetActionAuditLogsAsync("LOGIN_FAILED", cutoffTime);
            var ipAttempts = loginAttempts.Where(log => log.IPAddress == report.IPAddress);
            var uniqueUsers = ipAttempts.Select(log => log.UserName).Distinct().Count();

            if (uniqueUsers >= 5)
            {
                report.Activities.Add(new SuspiciousActivity
                {
                    Type = "MULTIPLE_ACCOUNT_ATTEMPTS",
                    Description = $"同一IP嘗試登入{uniqueUsers}個不同帳戶",
                    RiskLevel = uniqueUsers >= 10 ? "High" : "Medium",
                    Count = uniqueUsers
                });
            }
        }

        private int CalculateRiskScore(List<SuspiciousActivity> activities)
        {
            var score = 0;
            
            foreach (var activity in activities)
            {
                switch (activity.RiskLevel)
                {
                    case "High":
                        score += 30;
                        break;
                    case "Medium":
                        score += 20;
                        break;
                    case "Low":
                        score += 10;
                        break;
                }

                // 根據數量增加分數
                score += Math.Min(activity.Count * 2, 20);
            }

            return Math.Min(score, 100); // 最大100分
        }

        #endregion

        #region 輔助方法

        private async Task<ApplicationUser> FindUserByNameOrEmailAsync(string userName)
        {
            return _context.Users
                .FirstOrDefault(u => u.UserName == userName || u.Email == userName);
        }

        private string GetClientIP()
        {
            return AuthenticationService.GetClientIPAddress();
        }

        private string GetUserAgent()
        {
            return HttpContext.Current?.Request?.UserAgent ?? "unknown";
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _context?.Dispose();
            _auditService?.Dispose();
        }

        #endregion
    }

    #region 結果類別

    public class UserLockoutStatus
    {
        public bool IsLocked { get; set; }
        public string Message { get; set; }
        public DateTime? LockoutEndTime { get; set; }
        public int FailedAttempts { get; set; }
        public int RemainingAttempts { get; set; }
    }

    public class IPLockoutStatus
    {
        public bool IsBlocked { get; set; }
        public string Message { get; set; }
        public DateTime? BlockEndTime { get; set; }
        public int FailedAttempts { get; set; }
        public int RemainingAttempts { get; set; }
        public string Reason { get; set; }
    }

    public class SuspiciousActivityReport
    {
        public string IPAddress { get; set; }
        public string UserId { get; set; }
        public DateTime DetectionTime { get; set; }
        public List<SuspiciousActivity> Activities { get; set; }
        public int RiskScore { get; set; }
    }

    public class SuspiciousActivity
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public string RiskLevel { get; set; }
        public int Count { get; set; }
    }

    /// <summary>
    /// IP 封鎖記錄模型
    /// </summary>
    public class IPBlockRecord
    {
        public int Id { get; set; }
        public string IPAddress { get; set; }
        public DateTime BlockedAt { get; set; }
        public DateTime BlockedUntil { get; set; }
        public DateTime? UnblockedAt { get; set; }
        public string Reason { get; set; }
        public bool IsActive { get; set; }
        public string CreatedBy { get; set; }
        public string UnblockedBy { get; set; }
    }

    #endregion
}