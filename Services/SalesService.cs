using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 銷售服務 - 處理 POS 系統的所有銷售相關功能
    /// </summary>
    public class SalesService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        public SalesService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
        }

        public SalesService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _auditService = new AuditService();
        }

        #region 產品管理

        /// <summary>
        /// 取得所有產品
        /// </summary>
        /// <param name="category">類別篩選</param>
        /// <param name="isActive">是否啟用篩選</param>
        /// <param name="searchKeyword">搜尋關鍵字</param>
        /// <returns>產品列表</returns>
        public async Task<List<Product>> GetProductsAsync(string category = null, bool? isActive = null, string searchKeyword = null)
        {
            try
            {
                var query = _context.Set<Product>().AsQueryable();

                if (!string.IsNullOrEmpty(category))
                    query = query.Where(p => p.Category == category);

                if (isActive.HasValue)
                    query = query.Where(p => p.IsActive == isActive.Value);

                if (!string.IsNullOrEmpty(searchKeyword))
                {
                    searchKeyword = searchKeyword.Trim().ToLower();
                    query = query.Where(p => p.ProductName.ToLower().Contains(searchKeyword) ||
                                           p.ProductCode.ToLower().Contains(searchKeyword) ||
                                           p.Description.ToLower().Contains(searchKeyword));
                }

                return await query
                    .OrderBy(p => p.Category)
                    .ThenBy(p => p.ProductName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetProducts", "SalesService", ex, $"category: {category}, searchKeyword: {searchKeyword}");
                throw;
            }
        }

        /// <summary>
        /// 根據 ID 取得產品
        /// </summary>
        /// <param name="productId">產品 ID</param>
        /// <returns>產品</returns>
        public async Task<Product> GetProductByIdAsync(int productId)
        {
            try
            {
                return await _context.Set<Product>().FindAsync(productId);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetProductById", "SalesService", ex, $"productId: {productId}");
                throw;
            }
        }

        /// <summary>
        /// 建立或更新產品
        /// </summary>
        /// <param name="product">產品資料</param>
        /// <param name="operatorId">操作員ID</param>
        /// <returns>操作結果</returns>
        public async Task<SalesOperationResult> SaveProductAsync(Product product, string operatorId)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    // 驗證產品資料
                    var validationErrors = product.Validate();
                    if (validationErrors.Any())
                    {
                        return new SalesOperationResult
                        {
                            Success = false,
                            Message = "產品資料驗證失敗",
                            Errors = validationErrors
                        };
                    }

                    bool isNewProduct = product.Id == 0;
                    
                    if (isNewProduct)
                    {
                        // 檢查產品編號是否重複
                        if (!string.IsNullOrEmpty(product.ProductCode))
                        {
                            var existingProduct = await _context.Set<Product>()
                                .FirstOrDefaultAsync(p => p.ProductCode == product.ProductCode);
                            
                            if (existingProduct != null)
                            {
                                return new SalesOperationResult
                                {
                                    Success = false,
                                    Message = $"產品編號 {product.ProductCode} 已存在"
                                };
                            }
                        }

                        // 生成產品編號（如果未提供）
                        if (string.IsNullOrEmpty(product.ProductCode))
                        {
                            product.ProductCode = await GenerateProductCodeAsync();
                        }

                        product.CreatedBy = operatorId;
                        product.CreatedDate = DateTime.UtcNow;
                        _context.Set<Product>().Add(product);
                    }
                    else
                    {
                        var existingProduct = await _context.Set<Product>().FindAsync(product.Id);
                        if (existingProduct == null)
                        {
                            return new SalesOperationResult
                            {
                                Success = false,
                                Message = "找不到指定的產品"
                            };
                        }

                        // 更新產品資料
                        existingProduct.ProductName = product.ProductName;
                        existingProduct.Description = product.Description;
                        existingProduct.UnitPrice = product.UnitPrice;
                        existingProduct.StockQuantity = product.StockQuantity;
                        existingProduct.Category = product.Category;
                        existingProduct.MinStockLevel = product.MinStockLevel;
                        existingProduct.IsActive = product.IsActive;
                        existingProduct.IsAvailable = product.IsAvailable;
                        existingProduct.Remarks = product.Remarks;
                        existingProduct.ModifiedBy = operatorId;
                        existingProduct.ModifiedDate = DateTime.UtcNow;
                        
                        if (!string.IsNullOrEmpty(product.ImagePath))
                            existingProduct.ImagePath = product.ImagePath;

                        product = existingProduct;
                    }

                    await _context.SaveChangesAsync();
                    transaction.Commit();

                    // 記錄審計日誌
                    var action = isNewProduct ? "PRODUCT_CREATE" : "PRODUCT_UPDATE";
                    await _auditService.LogAsync(action, operatorId, null, null, 
                        $"{(isNewProduct ? "新增" : "修改")}產品：{product.ProductName} ({product.ProductCode})", 
                        null, "Product", product.Id.ToString());

                    return new SalesOperationResult
                    {
                        Success = true,
                        Message = $"產品{(isNewProduct ? "新增" : "更新")}成功",
                        Data = product
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    _auditService.LogError("SaveProduct", "SalesService", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 刪除產品
        /// </summary>
        /// <param name="productId">產品 ID</param>
        /// <param name="operatorId">操作員ID</param>
        /// <returns>操作結果</returns>
        public async Task<SalesOperationResult> DeleteProductAsync(int productId, string operatorId)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var product = await _context.Set<Product>().FindAsync(productId);
                    if (product == null)
                    {
                        return new SalesOperationResult
                        {
                            Success = false,
                            Message = "找不到指定的產品"
                        };
                    }

                    // 檢查是否有銷售記錄
                    var hasSalesRecords = await _context.Set<SalesItem>()
                        .AnyAsync(si => si.ProductId == productId);

                    if (hasSalesRecords)
                    {
                        // 如果有銷售記錄，只能停用產品
                        product.IsActive = false;
                        product.IsAvailable = false;
                        product.ModifiedBy = operatorId;
                        product.ModifiedDate = DateTime.UtcNow;
                    }
                    else
                    {
                        // 如果沒有銷售記錄，可以直接刪除
                        _context.Set<Product>().Remove(product);
                    }

                    await _context.SaveChangesAsync();
                    transaction.Commit();

                    // 記錄審計日誌
                    var action = hasSalesRecords ? "PRODUCT_DEACTIVATE" : "PRODUCT_DELETE";
                    var message = hasSalesRecords ? $"停用產品：{product.ProductName}" : $"刪除產品：{product.ProductName}";
                    
                    await _auditService.LogAsync(action, operatorId, null, null, message, 
                        null, "Product", productId.ToString());

                    return new SalesOperationResult
                    {
                        Success = true,
                        Message = hasSalesRecords ? "產品已停用" : "產品已刪除"
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    _auditService.LogError("DeleteProduct", "SalesService", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 上傳產品圖片
        /// </summary>
        /// <param name="productId">產品 ID</param>
        /// <param name="imageFile">圖片檔案</param>
        /// <param name="operatorId">操作員ID</param>
        /// <returns>操作結果</returns>
        public async Task<SalesOperationResult> UploadProductImageAsync(int productId, HttpPostedFileBase imageFile, string operatorId)
        {
            try
            {
                var product = await _context.Set<Product>().FindAsync(productId);
                if (product == null)
                {
                    return new SalesOperationResult
                    {
                        Success = false,
                        Message = "找不到指定的產品"
                    };
                }

                if (imageFile == null || imageFile.ContentLength <= 0)
                {
                    return new SalesOperationResult
                    {
                        Success = false,
                        Message = "請選擇有效的圖片檔案"
                    };
                }

                // 檢查檔案類型
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
                var fileExtension = Path.GetExtension(imageFile.FileName).ToLower();
                
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return new SalesOperationResult
                    {
                        Success = false,
                        Message = "只允許上傳 JPG、PNG、GIF、BMP 格式的圖片"
                    };
                }

                // 檢查檔案大小 (5MB)
                if (imageFile.ContentLength > 5 * 1024 * 1024)
                {
                    return new SalesOperationResult
                    {
                        Success = false,
                        Message = "圖片檔案大小不能超過 5MB"
                    };
                }

                // NAS 目錄路徑
                var nasPath = "/DECC_Files/Sales";
                var serverMappedPath = HttpContext.Current.Server.MapPath($"~{nasPath}");
                
                if (!Directory.Exists(serverMappedPath))
                {
                    Directory.CreateDirectory(serverMappedPath);
                }

                // 生成檔案名稱
                var fileName = $"product_{productId}_{DateTime.Now:yyyyMMddHHmmss}{fileExtension}";
                var filePath = Path.Combine(serverMappedPath, fileName);
                var relativePath = $"{nasPath}/{fileName}";

                // 刪除舊圖片
                if (!string.IsNullOrEmpty(product.ImagePath))
                {
                    var oldImagePath = HttpContext.Current.Server.MapPath($"~{product.ImagePath}");
                    if (File.Exists(oldImagePath))
                    {
                        File.Delete(oldImagePath);
                    }
                }

                // 儲存新圖片
                imageFile.SaveAs(filePath);

                // 更新產品圖片路徑
                product.ImagePath = relativePath;
                product.ModifiedBy = operatorId;
                product.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // 記錄審計日誌
                await _auditService.LogAsync("PRODUCT_IMAGE_UPLOAD", operatorId, null, null, 
                    $"上傳產品圖片：{product.ProductName} - {fileName}", 
                    null, "Product", productId.ToString());

                return new SalesOperationResult
                {
                    Success = true,
                    Message = "產品圖片上傳成功",
                    Data = relativePath
                };
            }
            catch (Exception ex)
            {
                _auditService.LogError("UploadProductImage", "SalesService", ex);
                throw;
            }
        }

        #endregion

        #region 銷售交易

        /// <summary>
        /// 建立銷售交易
        /// </summary>
        /// <param name="transaction">交易資料</param>
        /// <param name="operatorId">操作員ID</param>
        /// <param name="operatorName">操作員姓名</param>
        /// <returns>操作結果</returns>
        public async Task<SalesOperationResult> CreateSalesTransactionAsync(SalesTransaction transaction, string operatorId, string operatorName)
        {
            using (var dbTransaction = _context.Database.BeginTransaction())
            {
                try
                {
                    // 設定操作員資訊
                    transaction.OperatorId = operatorId;
                    transaction.OperatorName = operatorName;
                    transaction.TransactionDate = DateTime.Now;
                    transaction.CreatedAt = DateTime.UtcNow;

                    // 生成交易編號
                    transaction.TransactionNumber = await GenerateTransactionNumberAsync();

                    // 驗證交易資料
                    var validationErrors = transaction.Validate();
                    if (validationErrors.Any())
                    {
                        return new SalesOperationResult
                        {
                            Success = false,
                            Message = "交易資料驗證失敗",
                            Errors = validationErrors
                        };
                    }

                    // 處理銷售項目並檢查庫存
                    decimal totalAmount = 0;
                    foreach (var item in transaction.SalesItems)
                    {
                        var product = await _context.Set<Product>().FindAsync(item.ProductId);
                        if (product == null)
                        {
                            return new SalesOperationResult
                            {
                                Success = false,
                                Message = $"找不到產品 ID {item.ProductId}"
                            };
                        }

                        // 檢查庫存
                        if (!product.HasSufficientStock(item.Quantity))
                        {
                            return new SalesOperationResult
                            {
                                Success = false,
                                Message = $"產品 {product.ProductName} 庫存不足，當前庫存：{product.StockQuantity}"
                            };
                        }

                        // 設定產品資訊
                        item.ProductName = product.ProductName;
                        item.UnitPrice = product.UnitPrice;
                        item.CalculateTotalPrice();

                        // 減少庫存
                        product.ReduceStock(item.Quantity, operatorId);

                        totalAmount += item.TotalPrice;
                    }

                    transaction.TotalAmount = totalAmount;
                    transaction.CalculateChange();

                    // 儲存交易
                    _context.Set<SalesTransaction>().Add(transaction);
                    await _context.SaveChangesAsync();

                    dbTransaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("SALES_TRANSACTION_CREATE", operatorId, operatorName, null, 
                        $"建立銷售交易：{transaction.TransactionNumber} - 總金額 ${transaction.TotalAmount:F2}", 
                        null, "SalesTransaction", transaction.Id.ToString());

                    return new SalesOperationResult
                    {
                        Success = true,
                        Message = $"銷售交易建立成功：{transaction.TransactionNumber}",
                        TransactionId = transaction.Id,
                        Data = transaction
                    };
                }
                catch (Exception ex)
                {
                    dbTransaction.Rollback();
                    _auditService.LogError("CreateSalesTransaction", "SalesService", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 處理退貨
        /// </summary>
        /// <param name="originalTransactionId">原始交易ID</param>
        /// <param name="refundItems">退貨項目</param>
        /// <param name="refundReason">退貨原因</param>
        /// <param name="operatorId">操作員ID</param>
        /// <param name="operatorName">操作員姓名</param>
        /// <returns>操作結果</returns>
        public async Task<SalesOperationResult> ProcessRefundAsync(int originalTransactionId, List<SalesItem> refundItems, 
            string refundReason, string operatorId, string operatorName)
        {
            using (var dbTransaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var originalTransaction = await _context.Set<SalesTransaction>()
                        .Include(st => st.SalesItems)
                        .Include(st => st.Member)
                        .FirstOrDefaultAsync(st => st.Id == originalTransactionId);

                    if (originalTransaction == null)
                    {
                        return new SalesOperationResult
                        {
                            Success = false,
                            Message = "找不到原始交易記錄"
                        };
                    }

                    if (!originalTransaction.CanRefund())
                    {
                        return new SalesOperationResult
                        {
                            Success = false,
                            Message = "此交易無法退貨"
                        };
                    }

                    // 建立退貨交易
                    var refundTransaction = new SalesTransaction
                    {
                        TransactionNumber = await GenerateTransactionNumberAsync(),
                        MemberId = originalTransaction.MemberId,
                        CustomerName = originalTransaction.CustomerName,
                        CustomerPhone = originalTransaction.CustomerPhone,
                        TransactionType = TransactionType.Refund,
                        PaymentMethod = originalTransaction.PaymentMethod,
                        Status = TransactionStatus.Completed,
                        TransactionDate = DateTime.Now,
                        OperatorId = operatorId,
                        OperatorName = operatorName,
                        OriginalTransactionId = originalTransactionId,
                        RefundReason = refundReason,
                        CreatedAt = DateTime.UtcNow,
                        SalesItems = new List<SalesItem>()
                    };

                    decimal refundAmount = 0;
                    foreach (var refundItem in refundItems)
                    {
                        // 驗證退貨數量
                        var originalItem = originalTransaction.SalesItems
                            .FirstOrDefault(si => si.ProductId == refundItem.ProductId);
                        
                        if (originalItem == null)
                        {
                            return new SalesOperationResult
                            {
                                Success = false,
                                Message = $"原始交易中沒有產品 ID {refundItem.ProductId}"
                            };
                        }

                        if (refundItem.Quantity > originalItem.Quantity)
                        {
                            return new SalesOperationResult
                            {
                                Success = false,
                                Message = $"退貨數量不能超過原購買數量"
                            };
                        }

                        // 恢復庫存
                        var product = await _context.Set<Product>().FindAsync(refundItem.ProductId);
                        if (product != null)
                        {
                            product.IncreaseStock(refundItem.Quantity, operatorId);
                        }

                        // 建立退貨項目
                        var refundSalesItem = new SalesItem
                        {
                            ProductId = refundItem.ProductId,
                            ProductName = originalItem.ProductName,
                            UnitPrice = originalItem.UnitPrice,
                            Quantity = refundItem.Quantity,
                            DiscountRate = originalItem.DiscountRate,
                            DiscountAmount = originalItem.DiscountAmount
                        };
                        
                        refundSalesItem.CalculateTotalPrice();
                        refundTransaction.SalesItems.Add(refundSalesItem);
                        refundAmount += refundSalesItem.TotalPrice;
                    }

                    refundTransaction.TotalAmount = refundAmount;

                    // 儲存退貨交易
                    _context.Set<SalesTransaction>().Add(refundTransaction);
                    await _context.SaveChangesAsync();

                    dbTransaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("SALES_REFUND_PROCESS", operatorId, operatorName, null, 
                        $"處理退貨：{refundTransaction.TransactionNumber} - 退款金額 ${refundAmount:F2} - 原因：{refundReason}", 
                        null, "SalesTransaction", refundTransaction.Id.ToString());

                    return new SalesOperationResult
                    {
                        Success = true,
                        Message = $"退貨處理成功：{refundTransaction.TransactionNumber}",
                        TransactionId = refundTransaction.Id,
                        Data = refundTransaction
                    };
                }
                catch (Exception ex)
                {
                    dbTransaction.Rollback();
                    _auditService.LogError("ProcessRefund", "SalesService", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 取得銷售交易
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="transactionType">交易類型</param>
        /// <param name="paymentMethod">付款方式</param>
        /// <returns>交易列表</returns>
        public async Task<List<SalesTransaction>> GetSalesTransactionsAsync(DateTime? startDate = null, DateTime? endDate = null, 
            TransactionType? transactionType = null, PaymentMethodType? paymentMethod = null)
        {
            try
            {
                var query = _context.Set<SalesTransaction>()
                    .Include(st => st.Member)
                    .Include(st => st.SalesItems)
                    .ThenInclude(si => si.Product)
                    .AsQueryable();

                if (startDate.HasValue)
                    query = query.Where(st => st.TransactionDate >= startDate.Value);

                if (endDate.HasValue)
                    query = query.Where(st => st.TransactionDate <= endDate.Value);

                if (transactionType.HasValue)
                    query = query.Where(st => st.TransactionType == transactionType.Value);

                if (paymentMethod.HasValue)
                    query = query.Where(st => st.PaymentMethod == paymentMethod.Value);

                return await query
                    .OrderByDescending(st => st.TransactionDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetSalesTransactions", "SalesService", ex);
                throw;
            }
        }

        #endregion

        #region 銷售報表

        /// <summary>
        /// 取得銷售統計
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <returns>銷售統計</returns>
        public async Task<SalesStatistics> GetSalesStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var transactions = await GetSalesTransactionsAsync(startDate, endDate.AddDays(1).AddSeconds(-1));
                
                var sales = transactions.Where(t => t.TransactionType == TransactionType.Sale && t.Status == TransactionStatus.Completed);
                var refunds = transactions.Where(t => t.TransactionType == TransactionType.Refund && t.Status == TransactionStatus.Completed);

                var totalSales = sales.Sum(t => t.TotalAmount);
                var totalRefunds = refunds.Sum(t => t.TotalAmount);

                var statistics = new SalesStatistics
                {
                    Date = startDate,
                    TotalTransactions = sales.Count(),
                    TotalSales = totalSales,
                    TotalRefunds = totalRefunds,
                    NetSales = totalSales - totalRefunds,
                    TotalItemsSold = sales.SelectMany(t => t.SalesItems).Sum(si => si.Quantity),
                    AverageTransactionAmount = sales.Any() ? totalSales / sales.Count() : 0
                };

                // 付款方式統計
                statistics.PaymentMethodBreakdown = sales
                    .GroupBy(t => t.PaymentMethod)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count());

                // 類別銷售統計
                statistics.CategorySalesBreakdown = sales
                    .SelectMany(t => t.SalesItems)
                    .Join(_context.Set<Product>(), si => si.ProductId, p => p.Id, (si, p) => new { si, p })
                    .GroupBy(x => x.p.Category)
                    .ToDictionary(g => g.Key, g => g.Sum(x => x.si.TotalPrice));

                return statistics;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetSalesStatistics", "SalesService", ex);
                throw;
            }
        }

        /// <summary>
        /// 取得產品銷售統計
        /// </summary>
        /// <param name="startDate">開始日期</param>
        /// <param name="endDate">結束日期</param>
        /// <param name="category">產品類別</param>
        /// <returns>產品銷售統計列表</returns>
        public async Task<List<ProductSalesStatistic>> GetProductSalesStatisticsAsync(DateTime startDate, DateTime endDate, string category = null)
        {
            try
            {
                var query = from si in _context.Set<SalesItem>()
                           join st in _context.Set<SalesTransaction>() on si.SalesTransactionId equals st.Id
                           join p in _context.Set<Product>() on si.ProductId equals p.Id
                           where st.TransactionDate >= startDate && st.TransactionDate <= endDate.AddDays(1).AddSeconds(-1)
                                 && st.TransactionType == TransactionType.Sale
                                 && st.Status == TransactionStatus.Completed
                           select new { si, p };

                if (!string.IsNullOrEmpty(category))
                    query = query.Where(x => x.p.Category == category);

                var result = await query
                    .GroupBy(x => new { x.p.Id, x.p.ProductName, x.p.Category, x.p.UnitPrice, x.p.StockQuantity })
                    .Select(g => new ProductSalesStatistic
                    {
                        ProductId = g.Key.Id,
                        ProductName = g.Key.ProductName,
                        Category = g.Key.Category,
                        UnitPrice = g.Key.UnitPrice,
                        CurrentStock = g.Key.StockQuantity,
                        QuantitySold = g.Sum(x => x.si.Quantity),
                        Revenue = g.Sum(x => x.si.TotalPrice)
                    })
                    .OrderByDescending(ps => ps.Revenue)
                    .ToListAsync();

                return result;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetProductSalesStatistics", "SalesService", ex);
                throw;
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 生成產品編號
        /// </summary>
        /// <returns>產品編號</returns>
        private async Task<string> GenerateProductCodeAsync()
        {
            var prefix = "P";
            var datePart = DateTime.Now.ToString("yyyyMMdd");
            
            // 查找當天已存在的最大序號
            var existingCodes = await _context.Set<Product>()
                .Where(p => p.ProductCode.StartsWith(prefix + datePart))
                .Select(p => p.ProductCode)
                .ToListAsync();

            var maxSequence = 0;
            foreach (var code in existingCodes)
            {
                if (code.Length == prefix.Length + datePart.Length + 3)
                {
                    var sequencePart = code.Substring(prefix.Length + datePart.Length);
                    if (int.TryParse(sequencePart, out int sequence))
                    {
                        maxSequence = Math.Max(maxSequence, sequence);
                    }
                }
            }

            return $"{prefix}{datePart}{(maxSequence + 1):D3}";
        }

        /// <summary>
        /// 生成交易編號
        /// </summary>
        /// <returns>交易編號</returns>
        private async Task<string> GenerateTransactionNumberAsync()
        {
            var prefix = "S";
            var datePart = DateTime.Now.ToString("yyyyMMdd");
            
            // 查找當天已存在的最大序號
            var existingNumbers = await _context.Set<SalesTransaction>()
                .Where(st => st.TransactionNumber.StartsWith(prefix + datePart))
                .Select(st => st.TransactionNumber)
                .ToListAsync();

            var maxSequence = 0;
            foreach (var number in existingNumbers)
            {
                if (number.Length == prefix.Length + datePart.Length + 4)
                {
                    var sequencePart = number.Substring(prefix.Length + datePart.Length);
                    if (int.TryParse(sequencePart, out int sequence))
                    {
                        maxSequence = Math.Max(maxSequence, sequence);
                    }
                }
            }

            return $"{prefix}{datePart}{(maxSequence + 1):D4}";
        }

        /// <summary>
        /// 取得產品類別列表
        /// </summary>
        /// <returns>類別列表</returns>
        public List<string> GetProductCategories()
        {
            return ProductCategories.GetCategoryList();
        }

        /// <summary>
        /// 搜尋會員
        /// </summary>
        /// <param name="keyword">搜尋關鍵字</param>
        /// <returns>會員列表</returns>
        public async Task<List<Member>> SearchMembersAsync(string keyword)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(keyword))
                    return new List<Member>();

                keyword = keyword.Trim().ToLower();

                return await _context.Set<Member>()
                    .Where(m => m.IsActive &&
                               (m.MemberNumber.ToLower().Contains(keyword) ||
                                m.FullName.ToLower().Contains(keyword)))
                    .OrderBy(m => m.MemberNumber)
                    .Take(20)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("SearchMembers", "SalesService", ex, $"keyword: {keyword}");
                throw;
            }
        }

        #endregion

        #region IDisposable 實作

        public void Dispose()
        {
            _context?.Dispose();
            _auditService?.Dispose();
        }

        #endregion
    }
}