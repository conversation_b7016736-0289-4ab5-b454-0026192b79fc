using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;
using System.Web.Security;
using System.Data.Entity;
using System.Linq;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using CWDECC_3S.Models;
using CWDECC_3S.Data;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 驗證服務 - 整合 ASP.NET Identity 和 MariaDB
    /// </summary>
    public class AuthenticationService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        public AuthenticationService()
        {
            _context = new ApplicationDbContext();
            var userStore = new UserStore<ApplicationUser>(_context);
            var roleStore = new RoleStore<ApplicationRole>(_context);
            
            _userManager = new UserManager<ApplicationUser>(userStore);
            _roleManager = new RoleManager<ApplicationRole>(roleStore);
            _auditService = new AuditService();

            ConfigureUserManager();
        }

        #region UserManager 配置

        private void ConfigureUserManager()
        {
            // 密碼策略
            _userManager.PasswordValidator = new PasswordValidator
            {
                RequiredLength = 8,
                RequireNonLetterOrDigit = true,
                RequireDigit = true,
                RequireLowercase = true,
                RequireUppercase = true
            };

            // 用戶驗證策略
            _userManager.UserValidator = new UserValidator<ApplicationUser>(_userManager)
            {
                AllowOnlyAlphanumericUserNames = false,
                RequireUniqueEmail = true
            };

            // 鎖定策略
            _userManager.UserLockoutEnabledByDefault = true;
            _userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(30);
            _userManager.MaxFailedAccessAttemptsBeforeLockout = 5;

            // 雙因子驗證（預設關閉）
            _userManager.RegisterTwoFactorProvider("PhoneCode", new PhoneNumberTokenProvider<ApplicationUser>
            {
                MessageFormat = "您的安全代碼是 {0}"
            });

            _userManager.RegisterTwoFactorProvider("EmailCode", new EmailTokenProvider<ApplicationUser>
            {
                Subject = "安全代碼",
                BodyFormat = "您的安全代碼是 {0}"
            });
        }

        #endregion

        #region 用戶驗證

        /// <summary>
        /// 用戶登入驗證
        /// </summary>
        /// <param name="userName">用戶名或電子郵件</param>
        /// <param name="password">密碼</param>
        /// <param name="ipAddress">IP 地址</param>
        /// <param name="userAgent">用戶代理</param>
        /// <returns>登入結果</returns>
        public async Task<LoginResult> AuthenticateAsync(string userName, string password, string ipAddress, string userAgent)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userName) || string.IsNullOrWhiteSpace(password))
                {
                    await _auditService.LogAsync("LOGIN_FAILED", null, userName, ipAddress, "空用戶名或密碼", userAgent);
                    return new LoginResult { Success = false, Message = "請輸入用戶名和密碼" };
                }

                // 安全檢查：IP 封鎖檢查
                using (var securityService = new SecurityService())
                {
                    var ipStatus = await securityService.CheckIPLockoutAsync(ipAddress);
                    if (ipStatus.IsBlocked)
                    {
                        await _auditService.LogSecurityEventAsync(null, userName, ipAddress, userAgent, 
                            "BLOCKED_IP_LOGIN_ATTEMPT", ipStatus.Message);
                        return new LoginResult { Success = false, Message = ipStatus.Message };
                    }

                    // 用戶鎖定檢查
                    var userStatus = await securityService.CheckUserLockoutAsync(userName);
                    if (userStatus.IsLocked)
                    {
                        return new LoginResult { Success = false, Message = userStatus.Message };
                    }
                }

                // 尋找用戶（支援用戶名或電子郵件登入）
                var user = await FindUserAsync(userName);
                
                if (user == null)
                {
                    // 使用 SecurityService 記錄失敗嘗試
                    using (var securityService = new SecurityService())
                    {
                        await securityService.RecordFailedLoginAsync(userName, ipAddress, "用戶不存在");
                    }
                    return new LoginResult { Success = false, Message = "用戶名或密碼錯誤" };
                }

                // 檢查帳戶狀態
                var accountStatus = CheckAccountStatus(user);
                if (!accountStatus.IsValid)
                {
                    await _auditService.LogAsync("LOGIN_FAILED", user.Id, userName, ipAddress, accountStatus.Message, userAgent);
                    return new LoginResult { Success = false, Message = accountStatus.Message };
                }

                // 驗證密碼
                var isValidPassword = await _userManager.CheckPasswordAsync(user, password);
                
                if (!isValidPassword)
                {
                    // 使用 SecurityService 記錄失敗嘗試（包含暴力破解防護）
                    using (var securityService = new SecurityService())
                    {
                        await securityService.RecordFailedLoginAsync(userName, ipAddress, 
                            $"密碼錯誤 (累計失敗次數: {user.AccessFailedCount + 1})");
                    }
                    
                    // 重新檢查用戶狀態（可能已被鎖定）
                    var updatedUser = await FindUserAsync(userName);
                    var message = updatedUser.IsLockedOut ? 
                        $"帳戶已被鎖定至 {updatedUser.LockoutEndDateUtc?.AddHours(8):yyyy-MM-dd HH:mm}" :
                        "用戶名或密碼錯誤";
                    
                    return new LoginResult { Success = false, Message = message };
                }

                // 登入成功 - 重設失敗計數
                using (var securityService = new SecurityService())
                {
                    await securityService.ResetUserFailedAttemptsAsync(userName);
                }

                user.RecordSuccessfulLogin(ipAddress);
                await _userManager.UpdateAsync(user);

                // 建立 Session
                var sessionResult = await CreateUserSessionAsync(user, ipAddress, userAgent);
                
                await _auditService.LogAsync("LOGIN_SUCCESS", user.Id, userName, ipAddress, 
                    $"登入成功 (Session: {sessionResult.SessionId})", userAgent);

                // 檢測並記錄任何可疑活動
                using (var securityService = new SecurityService())
                {
                    var suspiciousReport = await securityService.DetectSuspiciousActivityAsync(ipAddress, user.Id);
                    if (suspiciousReport.RiskScore >= 50)
                    {
                        await _auditService.LogSecurityEventAsync(user.Id, userName, ipAddress, userAgent,
                            "SUSPICIOUS_LOGIN_SUCCESS", $"成功登入但風險分數為 {suspiciousReport.RiskScore}");
                    }
                }

                return new LoginResult 
                { 
                    Success = true, 
                    Message = "登入成功",
                    User = user,
                    SessionId = sessionResult.SessionId,
                    RedirectUrl = GetRedirectUrl(user)
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("LOGIN_ERROR", null, userName, ipAddress, $"系統錯誤: {ex.Message}", userAgent);
                return new LoginResult { Success = false, Message = "系統暫時無法提供服務，請稍後再試" };
            }
        }

        /// <summary>
        /// 尋找用戶（支援用戶名或電子郵件）
        /// </summary>
        private async Task<ApplicationUser> FindUserAsync(string userName)
        {
            var user = await _userManager.FindByNameAsync(userName);
            if (user == null && userName.Contains("@"))
            {
                user = await _userManager.FindByEmailAsync(userName);
            }
            return user;
        }

        /// <summary>
        /// 檢查帳戶狀態
        /// </summary>
        private AccountStatusResult CheckAccountStatus(ApplicationUser user)
        {
            if (!user.IsEnabled)
            {
                return new AccountStatusResult { IsValid = false, Message = "帳戶已被停用，請聯絡系統管理員" };
            }

            if (user.IsLockedOut)
            {
                return new AccountStatusResult 
                { 
                    IsValid = false, 
                    Message = $"帳戶已被鎖定至 {user.LockoutEndDateUtc?.AddHours(8):yyyy-MM-dd HH:mm}，請稍後再試" 
                };
            }

            if (user.IsPasswordExpired)
            {
                return new AccountStatusResult 
                { 
                    IsValid = false, 
                    Message = "密碼已過期，請聯絡管理員重設密碼" 
                };
            }

            return new AccountStatusResult { IsValid = true };
        }

        /// <summary>
        /// 取得重定向 URL
        /// </summary>
        private string GetRedirectUrl(ApplicationUser user)
        {
            // 根據角色決定重定向頁面
            switch (user.Role?.ToLower())
            {
                case "administrator":
                    return "~/Admin/Dashboard.aspx";
                case "staffmember":
                    return "~/Staff/Dashboard.aspx";
                case "teacher":
                    return "~/Teacher/Dashboard.aspx";
                default:
                    return "~/Default.aspx";
            }
        }

        #endregion

        #region Session 管理

        /// <summary>
        /// 建立用戶 Session
        /// </summary>
        private async Task<SessionResult> CreateUserSessionAsync(ApplicationUser user, string ipAddress, string userAgent)
        {
            var sessionId = HttpContext.Current.Session.SessionID;
            var context = HttpContext.Current;

            // 設定 Session 值
            context.Session["UserId"] = user.Id;
            context.Session["UserName"] = user.UserName;
            context.Session["DisplayName"] = user.DisplayName;
            context.Session["Role"] = user.Role;
            context.Session["Email"] = user.Email;
            context.Session["IsAuthenticated"] = true;
            context.Session["LoginTime"] = DateTime.UtcNow;
            context.Session["LastActivityTime"] = DateTime.UtcNow;
            context.Session["IPAddress"] = ipAddress;
            context.Session["UserAgent"] = userAgent;

            // 設定 Session 超時
            context.Session.Timeout = GetSessionTimeoutMinutes(user.Role);

            // 設定 Forms Authentication Cookie
            var ticket = new FormsAuthenticationTicket(
                1,
                user.UserName,
                DateTime.Now,
                DateTime.Now.AddMinutes(context.Session.Timeout),
                false, // 不記住我
                user.Role ?? "Member",
                FormsAuthentication.FormsCookiePath
            );

            var encryptedTicket = FormsAuthentication.Encrypt(ticket);
            var authCookie = new HttpCookie(FormsAuthentication.FormsCookieName, encryptedTicket)
            {
                HttpOnly = true,
                Secure = context.Request.IsSecureConnection,
                SameSite = SameSiteMode.Lax
            };

            context.Response.Cookies.Add(authCookie);

            // 更新 PermissionService
            PermissionService.SetUserRole(user.UserName, user.Role);

            return new SessionResult 
            { 
                Success = true, 
                SessionId = sessionId,
                ExpiresAt = DateTime.UtcNow.AddMinutes(context.Session.Timeout)
            };
        }

        /// <summary>
        /// 取得 Session 超時分鐘數
        /// </summary>
        private int GetSessionTimeoutMinutes(string role)
        {
            // 根據角色設定不同的超時時間
            switch (role?.ToLower())
            {
                case "administrator":
                    return 60; // 管理員 60 分鐘
                case "staffmember":
                    return 45; // 職員 45 分鐘
                default:
                    return 20; // 其他角色 20 分鐘
            }
        }

        /// <summary>
        /// 檢查 Session 是否有效
        /// </summary>
        public bool IsSessionValid()
        {
            var context = HttpContext.Current;
            if (context?.Session == null)
                return false;

            var isAuthenticated = context.Session["IsAuthenticated"] as bool?;
            var lastActivity = context.Session["LastActivityTime"] as DateTime?;

            if (!isAuthenticated.HasValue || !isAuthenticated.Value || !lastActivity.HasValue)
                return false;

            // 檢查是否超時
            var timeoutMinutes = context.Session.Timeout;
            var isExpired = DateTime.UtcNow.Subtract(lastActivity.Value).TotalMinutes > timeoutMinutes;

            if (isExpired)
            {
                ClearSession("SESSION_TIMEOUT");
                return false;
            }

            // 更新最後活動時間
            context.Session["LastActivityTime"] = DateTime.UtcNow;
            return true;
        }

        /// <summary>
        /// 清除 Session
        /// </summary>
        public void ClearSession(string reason = "LOGOUT")
        {
            var context = HttpContext.Current;
            if (context?.Session == null)
                return;

            var userId = context.Session["UserId"] as string;
            var userName = context.Session["UserName"] as string;
            var ipAddress = GetClientIPAddress();

            // 記錄登出
            if (!string.IsNullOrEmpty(userId))
            {
                _auditService.LogAsync(reason, userId, userName, ipAddress, 
                    reason == "SESSION_TIMEOUT" ? "Session 超時" : "用戶登出", 
                    context.Request.UserAgent).Wait();
            }

            // 清除 Session
            context.Session.Clear();
            context.Session.Abandon();

            // 清除 PermissionService
            PermissionService.ClearUserRole();

            // 清除 Forms Authentication Cookie
            FormsAuthentication.SignOut();

            // 清除所有相關 Cookies
            foreach (string cookieName in context.Request.Cookies.AllKeys)
            {
                var cookie = new HttpCookie(cookieName)
                {
                    Expires = DateTime.Now.AddDays(-1),
                    Value = ""
                };
                context.Response.Cookies.Add(cookie);
            }
        }

        #endregion

        #region 密碼管理

        /// <summary>
        /// 生成密碼重設 Token
        /// </summary>
        public async Task<PasswordResetResult> GeneratePasswordResetTokenAsync(string userName, string adminId)
        {
            try
            {
                var user = await FindUserAsync(userName);
                if (user == null)
                {
                    return new PasswordResetResult { Success = false, Message = "找不到指定的用戶" };
                }

                user.GeneratePasswordResetToken(24); // 24小時有效
                await _userManager.UpdateAsync(user);

                var ipAddress = GetClientIPAddress();
                await _auditService.LogAsync("PASSWORD_RESET_REQUEST", user.Id, userName, ipAddress, 
                    $"管理員 {adminId} 產生密碼重設 Token", HttpContext.Current?.Request.UserAgent);

                return new PasswordResetResult 
                { 
                    Success = true, 
                    Message = "密碼重設 Token 已生成",
                    ResetToken = user.PasswordResetToken,
                    ExpiresAt = user.PasswordResetTokenExpiry.Value
                };
            }
            catch (Exception ex)
            {
                return new PasswordResetResult { Success = false, Message = $"生成重設 Token 失敗: {ex.Message}" };
            }
        }

        /// <summary>
        /// 重設密碼
        /// </summary>
        public async Task<PasswordResetResult> ResetPasswordAsync(string userName, string token, string newPassword, string adminId)
        {
            try
            {
                var user = await FindUserAsync(userName);
                if (user == null)
                {
                    return new PasswordResetResult { Success = false, Message = "找不到指定的用戶" };
                }

                if (!user.IsPasswordResetTokenValid(token))
                {
                    return new PasswordResetResult { Success = false, Message = "重設 Token 無效或已過期" };
                }

                // 驗證新密碼
                var passwordValidation = await _userManager.PasswordValidator.ValidateAsync(newPassword);
                if (!passwordValidation.Succeeded)
                {
                    var errors = string.Join(", ", passwordValidation.Errors);
                    return new PasswordResetResult { Success = false, Message = $"密碼不符合要求: {errors}" };
                }

                // 重設密碼
                var removePasswordResult = await _userManager.RemovePasswordAsync(user.Id);
                if (!removePasswordResult.Succeeded)
                {
                    return new PasswordResetResult { Success = false, Message = "移除舊密碼失敗" };
                }

                var addPasswordResult = await _userManager.AddPasswordAsync(user.Id, newPassword);
                if (!addPasswordResult.Succeeded)
                {
                    var errors = string.Join(", ", addPasswordResult.Errors);
                    return new PasswordResetResult { Success = false, Message = $"設定新密碼失敗: {errors}" };
                }

                // 清除重設 Token 並重設失敗計數
                user.ClearPasswordResetToken();
                user.ResetAccessFailedCount();
                user.MustChangePassword = false;
                await _userManager.UpdateAsync(user);

                var ipAddress = GetClientIPAddress();
                await _auditService.LogAsync("PASSWORD_RESET_SUCCESS", user.Id, userName, ipAddress, 
                    $"管理員 {adminId} 重設密碼成功", HttpContext.Current?.Request.UserAgent);

                return new PasswordResetResult { Success = true, Message = "密碼重設成功" };
            }
            catch (Exception ex)
            {
                return new PasswordResetResult { Success = false, Message = $"重設密碼失敗: {ex.Message}" };
            }
        }

        #endregion

        #region 用戶管理

        /// <summary>
        /// 建立新用戶
        /// </summary>
        public async Task<CreateUserResult> CreateUserAsync(ApplicationUser user, string password, string createdBy)
        {
            try
            {
                user.CreatedBy = createdBy;
                user.CreatedDate = DateTime.UtcNow;

                var result = await _userManager.CreateAsync(user, password);
                
                if (result.Succeeded)
                {
                    var ipAddress = GetClientIPAddress();
                    await _auditService.LogAsync("USER_CREATED", user.Id, user.UserName, ipAddress, 
                        $"用戶建立成功，建立者: {createdBy}", HttpContext.Current?.Request.UserAgent);
                    
                    return new CreateUserResult { Success = true, Message = "用戶建立成功", User = user };
                }
                else
                {
                    var errors = string.Join(", ", result.Errors);
                    return new CreateUserResult { Success = false, Message = $"建立用戶失敗: {errors}" };
                }
            }
            catch (Exception ex)
            {
                return new CreateUserResult { Success = false, Message = $"建立用戶失敗: {ex.Message}" };
            }
        }

        /// <summary>
        /// 更新用戶資訊
        /// </summary>
        public async Task<UpdateUserResult> UpdateUserAsync(ApplicationUser user, string modifiedBy)
        {
            try
            {
                user.ModifiedBy = modifiedBy;
                user.ModifiedDate = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                
                if (result.Succeeded)
                {
                    var ipAddress = GetClientIPAddress();
                    await _auditService.LogAsync("USER_UPDATED", user.Id, user.UserName, ipAddress, 
                        $"用戶資訊更新，修改者: {modifiedBy}", HttpContext.Current?.Request.UserAgent);
                    
                    return new UpdateUserResult { Success = true, Message = "用戶資訊更新成功" };
                }
                else
                {
                    var errors = string.Join(", ", result.Errors);
                    return new UpdateUserResult { Success = false, Message = $"更新用戶失敗: {errors}" };
                }
            }
            catch (Exception ex)
            {
                return new UpdateUserResult { Success = false, Message = $"更新用戶失敗: {ex.Message}" };
            }
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 取得客戶端 IP 地址
        /// </summary>
        public static string GetClientIPAddress()
        {
            var context = HttpContext.Current;
            if (context?.Request == null)
                return "unknown";

            var ipAddress = context.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            
            if (string.IsNullOrEmpty(ipAddress) || ipAddress.ToLower() == "unknown")
                ipAddress = context.Request.ServerVariables["HTTP_X_REAL_IP"];
                
            if (string.IsNullOrEmpty(ipAddress) || ipAddress.ToLower() == "unknown")
                ipAddress = context.Request.ServerVariables["REMOTE_ADDR"];
                
            if (string.IsNullOrEmpty(ipAddress) || ipAddress.ToLower() == "unknown")
                ipAddress = context.Request.UserHostAddress;

            // 如果有多個 IP（通過代理），取第一個
            if (!string.IsNullOrEmpty(ipAddress) && ipAddress.Contains(","))
            {
                ipAddress = ipAddress.Split(',')[0].Trim();
            }

            return ipAddress ?? "unknown";
        }

        /// <summary>
        /// 清理資源
        /// </summary>
        public void Dispose()
        {
            _userManager?.Dispose();
            _roleManager?.Dispose();
            _context?.Dispose();
        }

        #endregion
    }

    #region 結果類別

    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public ApplicationUser User { get; set; }
        public string SessionId { get; set; }
        public string RedirectUrl { get; set; }
    }

    public class SessionResult
    {
        public bool Success { get; set; }
        public string SessionId { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class AccountStatusResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
    }

    public class PasswordResetResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string ResetToken { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class CreateUserResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public ApplicationUser User { get; set; }
    }

    public class UpdateUserResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    #endregion
}