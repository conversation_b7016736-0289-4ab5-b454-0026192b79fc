using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 角色權限服務 - 管理用戶角色和權限驗證
    /// </summary>
    public class PermissionService
    {
        #region 角色定義

        public static class Roles
        {
            public const string Administrator = "Administrator";
            public const string StaffMember = "StaffMember";
            public const string Teacher = "Teacher";
            public const string Volunteer = "Volunteer";
            public const string Member = "Member";
            public const string Guest = "Guest";
        }

        public static class Permissions
        {
            public const string ViewMembers = "ViewMembers";
            public const string ManageMembers = "ManageMembers";
            public const string ViewActivities = "ViewActivities";
            public const string ManageActivities = "ManageActivities";
            public const string ViewReports = "ViewReports";
            public const string ManageSystem = "ManageSystem";
            public const string ViewDashboard = "ViewDashboard";
            public const string ManageUsers = "ManageUsers";
            public const string ViewFirebase = "ViewFirebase";
            public const string ViewSecurity = "ViewSecurity";
        }

        #endregion

        #region 角色權限配置

        private static readonly Dictionary<string, string[]> RolePermissions = new Dictionary<string, string[]>
        {
            {
                Roles.Administrator,
                new[]
                {
                    Permissions.ViewMembers, Permissions.ManageMembers,
                    Permissions.ViewActivities, Permissions.ManageActivities,
                    Permissions.ViewReports, Permissions.ManageSystem,
                    Permissions.ViewDashboard, Permissions.ManageUsers,
                    Permissions.ViewFirebase, Permissions.ViewSecurity
                }
            },
            {
                Roles.StaffMember,
                new[]
                {
                    Permissions.ViewMembers, Permissions.ManageMembers,
                    Permissions.ViewActivities, Permissions.ManageActivities,
                    Permissions.ViewReports, Permissions.ViewDashboard
                }
            },
            {
                Roles.Teacher,
                new[]
                {
                    Permissions.ViewMembers, Permissions.ViewActivities,
                    Permissions.ManageActivities, Permissions.ViewDashboard
                }
            },
            {
                Roles.Volunteer,
                new[]
                {
                    Permissions.ViewMembers, Permissions.ViewActivities,
                    Permissions.ViewDashboard
                }
            },
            {
                Roles.Member,
                new[]
                {
                    Permissions.ViewActivities, Permissions.ViewDashboard
                }
            },
            {
                Roles.Guest,
                new[]
                {
                    Permissions.ViewActivities
                }
            }
        };

        #endregion

        #region 導航選單配置

        /// <summary>
        /// 導航選單項目
        /// </summary>
        public class NavigationItem
        {
            public string Text { get; set; }
            public string Url { get; set; }
            public string Icon { get; set; }
            public string Permission { get; set; }
            public List<NavigationItem> Children { get; set; } = new List<NavigationItem>();
            public bool IsDropdown => Children.Count > 0;
        }

        /// <summary>
        /// 主導航選單配置
        /// </summary>
        private static readonly List<NavigationItem> MainNavigation = new List<NavigationItem>
        {
            new NavigationItem
            {
                Text = "儀表板",
                Url = "~/Dashboard/",
                Icon = "fas fa-tachometer-alt",
                Permission = Permissions.ViewDashboard
            },
            new NavigationItem
            {
                Text = "會員管理",
                Url = "#",
                Icon = "fas fa-users",
                Permission = Permissions.ViewMembers,
                Children = new List<NavigationItem>
                {
                    new NavigationItem { Text = "會員列表", Url = "~/Members/", Permission = Permissions.ViewMembers },
                    new NavigationItem { Text = "新增會員", Url = "~/Members/Create.aspx", Permission = Permissions.ManageMembers },
                    new NavigationItem { Text = "會員報告", Url = "~/Reports/Members.aspx", Permission = Permissions.ViewReports }
                }
            },
            new NavigationItem
            {
                Text = "活動管理",
                Url = "#",
                Icon = "fas fa-calendar-alt",
                Permission = Permissions.ViewActivities,
                Children = new List<NavigationItem>
                {
                    new NavigationItem { Text = "活動列表", Url = "~/Activities/", Permission = Permissions.ViewActivities },
                    new NavigationItem { Text = "新增活動", Url = "~/Activities/Create.aspx", Permission = Permissions.ManageActivities },
                    new NavigationItem { Text = "活動報告", Url = "~/Reports/Activities.aspx", Permission = Permissions.ViewReports }
                }
            },
            new NavigationItem
            {
                Text = "系統管理",
                Url = "#",
                Icon = "fas fa-cog",
                Permission = Permissions.ManageSystem,
                Children = new List<NavigationItem>
                {
                    new NavigationItem { Text = "用戶管理", Url = "~/Admin/Users.aspx", Permission = Permissions.ManageUsers },
                    new NavigationItem { Text = "Firebase 測試", Url = "~/Admin/FirebaseTest.aspx", Permission = Permissions.ViewFirebase },
                    new NavigationItem { Text = "安全測試", Url = "~/Admin/SecurityTest.aspx", Permission = Permissions.ViewSecurity },
                    new NavigationItem { Text = "系統設定", Url = "~/Admin/Settings.aspx", Permission = Permissions.ManageSystem }
                }
            }
        };

        #endregion

        #region 權限檢查方法

        /// <summary>
        /// 檢查當前用戶是否有指定權限
        /// </summary>
        /// <param name="permission">權限名稱</param>
        /// <returns>是否有權限</returns>
        public static bool HasPermission(string permission)
        {
            var user = HttpContext.Current?.User;
            if (user == null || !user.Identity.IsAuthenticated)
            {
                return false;
            }

            var userRole = GetUserRole(user.Identity.Name);
            return HasRolePermission(userRole, permission);
        }

        /// <summary>
        /// 檢查角色是否有指定權限
        /// </summary>
        /// <param name="role">角色名稱</param>
        /// <param name="permission">權限名稱</param>
        /// <returns>是否有權限</returns>
        public static bool HasRolePermission(string role, string permission)
        {
            if (string.IsNullOrEmpty(role) || string.IsNullOrEmpty(permission))
                return false;

            return RolePermissions.ContainsKey(role) && 
                   RolePermissions[role].Contains(permission);
        }

        /// <summary>
        /// 取得用戶角色
        /// </summary>
        /// <param name="username">用戶名稱</param>
        /// <returns>角色名稱</returns>
        public static string GetUserRole(string username)
        {
            if (string.IsNullOrEmpty(username))
                return Roles.Guest;

            // 從 Session 取得用戶角色
            var sessionRole = HttpContext.Current?.Session?["UserRole"]?.ToString();
            if (!string.IsNullOrEmpty(sessionRole))
                return sessionRole;

            // 模擬用戶角色分配（實際應從資料庫取得）
            var userRoleMap = new Dictionary<string, string>
            {
                { "admin", Roles.Administrator },
                { "<EMAIL>", Roles.Administrator },
                { "staff", Roles.StaffMember },
                { "<EMAIL>", Roles.StaffMember },
                { "teacher", Roles.Teacher },
                { "<EMAIL>", Roles.Teacher },
                { "volunteer", Roles.Volunteer },
                { "<EMAIL>", Roles.Volunteer },
                { "member", Roles.Member },
                { "<EMAIL>", Roles.Member }
            };

            return userRoleMap.ContainsKey(username.ToLower()) 
                ? userRoleMap[username.ToLower()] 
                : Roles.Guest;
        }

        /// <summary>
        /// 設定用戶角色到 Session
        /// </summary>
        /// <param name="username">用戶名稱</param>
        /// <param name="role">角色名稱</param>
        public static void SetUserRole(string username, string role)
        {
            if (HttpContext.Current?.Session != null)
            {
                HttpContext.Current.Session["UserRole"] = role;
                HttpContext.Current.Session["Username"] = username;
            }
        }

        /// <summary>
        /// 清除用戶角色 Session
        /// </summary>
        public static void ClearUserRole()
        {
            if (HttpContext.Current?.Session != null)
            {
                HttpContext.Current.Session.Remove("UserRole");
                HttpContext.Current.Session.Remove("Username");
            }
        }

        #endregion

        #region 導航選單生成

        /// <summary>
        /// 取得當前用戶的導航選單
        /// </summary>
        /// <returns>導航選單項目列表</returns>
        public static List<NavigationItem> GetUserNavigation()
        {
            var userNavigation = new List<NavigationItem>();

            foreach (var item in MainNavigation)
            {
                if (HasPermission(item.Permission))
                {
                    var navItem = new NavigationItem
                    {
                        Text = item.Text,
                        Url = item.Url,
                        Icon = item.Icon,
                        Permission = item.Permission
                    };

                    // 檢查子選單權限
                    foreach (var child in item.Children)
                    {
                        if (HasPermission(child.Permission))
                        {
                            navItem.Children.Add(child);
                        }
                    }

                    // 只有有權限的子項目或本身可存取才顯示
                    if (navItem.Children.Count > 0 || !item.IsDropdown)
                    {
                        userNavigation.Add(navItem);
                    }
                }
            }

            return userNavigation;
        }

        /// <summary>
        /// 取得用戶資訊顯示
        /// </summary>
        /// <returns>用戶顯示資訊</returns>
        public static UserDisplayInfo GetUserDisplayInfo()
        {
            var user = HttpContext.Current?.User;
            if (user == null || !user.Identity.IsAuthenticated)
            {
                return new UserDisplayInfo
                {
                    IsAuthenticated = false,
                    DisplayName = "訪客",
                    Role = Roles.Guest,
                    RoleDisplayName = "訪客"
                };
            }

            var username = user.Identity.Name;
            var role = GetUserRole(username);

            return new UserDisplayInfo
            {
                IsAuthenticated = true,
                Username = username,
                DisplayName = GetDisplayName(username),
                Role = role,
                RoleDisplayName = GetRoleDisplayName(role)
            };
        }

        /// <summary>
        /// 取得顯示名稱
        /// </summary>
        /// <param name="username">用戶名稱</param>
        /// <returns>顯示名稱</returns>
        private static string GetDisplayName(string username)
        {
            if (string.IsNullOrEmpty(username))
                return "未知用戶";

            // 從 Session 取得顯示名稱
            var sessionDisplayName = HttpContext.Current?.Session?["DisplayName"]?.ToString();
            if (!string.IsNullOrEmpty(sessionDisplayName))
                return sessionDisplayName;

            // 模擬顯示名稱對應（實際應從資料庫取得）
            var displayNameMap = new Dictionary<string, string>
            {
                { "admin", "系統管理員" },
                { "<EMAIL>", "系統管理員" },
                { "staff", "職員" },
                { "<EMAIL>", "職員" },
                { "teacher", "導師" },
                { "<EMAIL>", "導師" },
                { "volunteer", "義工" },
                { "<EMAIL>", "義工" },
                { "member", "會員" },
                { "<EMAIL>", "會員" }
            };

            return displayNameMap.ContainsKey(username.ToLower()) 
                ? displayNameMap[username.ToLower()] 
                : username;
        }

        /// <summary>
        /// 取得角色顯示名稱
        /// </summary>
        /// <param name="role">角色名稱</param>
        /// <returns>角色顯示名稱</returns>
        private static string GetRoleDisplayName(string role)
        {
            var roleDisplayNames = new Dictionary<string, string>
            {
                { Roles.Administrator, "系統管理員" },
                { Roles.StaffMember, "職員" },
                { Roles.Teacher, "導師" },
                { Roles.Volunteer, "義工" },
                { Roles.Member, "會員" },
                { Roles.Guest, "訪客" }
            };

            return roleDisplayNames.ContainsKey(role) ? roleDisplayNames[role] : role;
        }

        #endregion

        #region 輔助類別

        /// <summary>
        /// 用戶顯示資訊
        /// </summary>
        public class UserDisplayInfo
        {
            public bool IsAuthenticated { get; set; }
            public string Username { get; set; }
            public string DisplayName { get; set; }
            public string Role { get; set; }
            public string RoleDisplayName { get; set; }
        }

        #endregion
    }
}