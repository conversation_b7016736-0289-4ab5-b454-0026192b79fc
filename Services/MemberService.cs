using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 會員管理服務 - 處理會員搜尋、資料管理與會籍相關功能
    /// </summary>
    public class MemberService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        public MemberService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
        }

        public MemberService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _auditService = new AuditService();
        }

        #region 會員搜尋與查詢

        /// <summary>
        /// 搜尋會員（支援模糊匹配）
        /// </summary>
        /// <param name="keyword">搜尋關鍵字（會員號、姓名）</param>
        /// <param name="searchBy">搜尋欄位（MemberNumber, Name, Phone）</param>
        /// <param name="searchUserId">執行搜尋的用戶ID（用於審計）</param>
        /// <returns>搜尋結果</returns>
        public async Task<List<Member>> SearchMembersAsync(string keyword, string searchBy, string searchUserId)
        {
            try
            {
                var query = _context.Set<Member>()
                    .Where(m => m.IsActive);

                if (!string.IsNullOrEmpty(keyword))
                {
                    var searchKeyword = keyword.Trim().ToLower();

                    switch (searchBy?.ToLower())
                    {
                        case "membernumber":
                            query = query.Where(m => m.MemberNumber.ToLower().Contains(searchKeyword));
                            break;

                        case "name":
                            query = query.Where(m => m.FullName.ToLower().Contains(searchKeyword));
                            break;

                        case "phone":
                            // 對於加密電話號碼的搜尋，需要特殊處理
                            // 由於AES加密後無法直接進行LIKE查詢，這裡需要取出所有記錄後在應用層過濾
                            var allMembers = await query.ToListAsync();
                            var phoneFilteredMembers = allMembers.Where(m => 
                                !string.IsNullOrEmpty(m.Phone) && 
                                m.Phone.ToLower().Contains(searchKeyword)
                            ).ToList();

                            // 記錄搜尋審計日誌
                            await _auditService.LogAsync("MEMBER_SEARCH", "MemberManagement", searchUserId,
                                $"電話搜尋: {keyword} | 結果: {phoneFilteredMembers.Count}筆", null, "SUCCESS");

                            return phoneFilteredMembers;

                        default:
                            // 預設搜尋會員號和姓名
                            query = query.Where(m => 
                                m.MemberNumber.ToLower().Contains(searchKeyword) ||
                                m.FullName.ToLower().Contains(searchKeyword)
                            );
                            break;
                    }
                }

                var result = await query
                    .OrderBy(m => m.MemberNumber)
                    .Take(50) // 限制搜尋結果數量以提升性能
                    .ToListAsync();

                // 記錄搜尋審計日誌
                await _auditService.LogAsync("MEMBER_SEARCH", "MemberManagement", searchUserId,
                    $"關鍵字: {keyword} | 搜尋類型: {searchBy} | 結果: {result.Count}筆", null, "SUCCESS");

                return result;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("MEMBER_SEARCH", "MemberManagement", searchUserId,
                    $"搜尋失敗: {ex.Message} | 關鍵字: {keyword}", null, "ERROR");
                
                _auditService.LogError("SearchMembers", "MemberManagement", ex, 
                    $"Keyword: {keyword}, SearchBy: {searchBy}");
                throw;
            }
        }

        /// <summary>
        /// 根據搜尋條件取得會員列表
        /// </summary>
        /// <param name="criteria">搜尋條件</param>
        /// <param name="searchUserId">執行搜尋的用戶ID</param>
        /// <returns>會員列表和總數</returns>
        public async Task<(List<Member> Members, int TotalCount)> SearchMembersAsync(MemberSearchCriteria criteria, string searchUserId)
        {
            try
            {
                var query = _context.Set<Member>().AsQueryable();

                // 應用搜尋條件
                if (!string.IsNullOrEmpty(criteria.Keyword))
                {
                    var keyword = criteria.Keyword.Trim().ToLower();
                    query = query.Where(m => 
                        m.MemberNumber.ToLower().Contains(keyword) ||
                        m.FullName.ToLower().Contains(keyword)
                    );
                }

                if (!string.IsNullOrEmpty(criteria.MemberType))
                {
                    query = query.Where(m => m.MemberType == criteria.MemberType);
                }

                if (!string.IsNullOrEmpty(criteria.MembershipStatus))
                {
                    query = query.Where(m => m.MembershipStatus == criteria.MembershipStatus);
                }

                if (criteria.JoinDateFrom.HasValue)
                {
                    query = query.Where(m => m.JoinDate >= criteria.JoinDateFrom.Value);
                }

                if (criteria.JoinDateTo.HasValue)
                {
                    query = query.Where(m => m.JoinDate <= criteria.JoinDateTo.Value);
                }

                if (criteria.ExpiryDateFrom.HasValue)
                {
                    query = query.Where(m => m.MembershipEndDate >= criteria.ExpiryDateFrom.Value);
                }

                if (criteria.ExpiryDateTo.HasValue)
                {
                    query = query.Where(m => m.MembershipEndDate <= criteria.ExpiryDateTo.Value);
                }

                if (criteria.IsActive.HasValue)
                {
                    query = query.Where(m => m.IsActive == criteria.IsActive.Value);
                }

                // 總數
                var totalCount = await query.CountAsync();

                // 排序
                switch (criteria.SortField?.ToLower())
                {
                    case "membernumber":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(m => m.MemberNumber) : 
                            query.OrderBy(m => m.MemberNumber);
                        break;
                    case "membertype":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(m => m.MemberType) : 
                            query.OrderBy(m => m.MemberType);
                        break;
                    case "joindate":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(m => m.JoinDate) : 
                            query.OrderBy(m => m.JoinDate);
                        break;
                    case "membershipenddate":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(m => m.MembershipEndDate) : 
                            query.OrderBy(m => m.MembershipEndDate);
                        break;
                    default:
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(m => m.FullName) : 
                            query.OrderBy(m => m.FullName);
                        break;
                }

                // 分頁
                var members = await query
                    .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                    .Take(criteria.PageSize)
                    .ToListAsync();

                // 記錄搜尋審計日誌
                await _auditService.LogAsync("MEMBER_ADVANCED_SEARCH", "MemberManagement", searchUserId,
                    $"進階搜尋 | 關鍵字: {criteria.Keyword} | 結果: {totalCount}筆", null, "SUCCESS");

                return (members, totalCount);
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("MEMBER_ADVANCED_SEARCH", "MemberManagement", searchUserId,
                    $"進階搜尋失敗: {ex.Message}", null, "ERROR");
                
                _auditService.LogError("SearchMembersAdvanced", "MemberManagement", ex, 
                    $"Criteria: {Newtonsoft.Json.JsonConvert.SerializeObject(criteria)}");
                throw;
            }
        }

        /// <summary>
        /// 根據 ID 取得會員資料
        /// </summary>
        /// <param name="memberId">會員 ID</param>
        /// <returns>會員資料</returns>
        public async Task<Member> GetMemberByIdAsync(int memberId)
        {
            if (memberId <= 0)
                throw new ArgumentException("會員 ID 必須大於 0", nameof(memberId));

            try
            {
                return await _context.Set<Member>()
                    .Include(m => m.MemberActivities)
                    .FirstOrDefaultAsync(m => m.Id == memberId);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMemberById", "MemberManagement", ex, 
                    $"memberId: {memberId}");
                throw;
            }
        }

        /// <summary>
        /// 根據會員號碼取得會員資料
        /// </summary>
        /// <param name="memberNumber">會員號碼</param>
        /// <returns>會員資料</returns>
        public async Task<Member> GetMemberByNumberAsync(string memberNumber)
        {
            if (string.IsNullOrEmpty(memberNumber))
                throw new ArgumentException("會員號碼不能為空", nameof(memberNumber));

            try
            {
                return await _context.Set<Member>()
                    .FirstOrDefaultAsync(m => m.MemberNumber == memberNumber);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMemberByNumber", "MemberManagement", ex, 
                    $"memberNumber: {memberNumber}");
                throw;
            }
        }

        #endregion

        #region 會員狀態管理

        /// <summary>
        /// 更新所有會員的過期狀態
        /// </summary>
        /// <param name="updatedBy">更新者 ID</param>
        /// <returns>更新的會員數量</returns>
        public async Task<int> UpdateAllMembershipStatusesAsync(string updatedBy)
        {
            try
            {
                var expiredMembers = await _context.Set<Member>()
                    .Where(m => m.IsActive && 
                               m.MembershipStatus == "Active" && 
                               m.MembershipEndDate < DateTime.Today)
                    .ToListAsync();

                foreach (var member in expiredMembers)
                {
                    member.UpdateMembershipStatus();
                    member.ModifiedBy = updatedBy;
                    member.ModifiedDate = DateTime.UtcNow;
                }

                if (expiredMembers.Any())
                {
                    await _context.SaveChangesAsync();

                    // 記錄審計日誌
                    await _auditService.LogAsync("UPDATE_MEMBERSHIP_STATUS", "MemberManagement", updatedBy,
                        $"批量更新會籍狀態，共 {expiredMembers.Count} 人", null, "SUCCESS");
                }

                return expiredMembers.Count;
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("UPDATE_MEMBERSHIP_STATUS", "MemberManagement", updatedBy,
                    $"批量更新會籍狀態失敗: {ex.Message}", null, "ERROR");
                
                _auditService.LogError("UpdateAllMembershipStatuses", "MemberManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 取得即將到期的會員列表
        /// </summary>
        /// <param name="daysAhead">提前天數（預設30天）</param>
        /// <returns>即將到期的會員列表</returns>
        public async Task<List<Member>> GetMembersExpiringSoonAsync(int daysAhead = 30)
        {
            try
            {
                var cutoffDate = DateTime.Today.AddDays(daysAhead);

                return await _context.Set<Member>()
                    .Where(m => m.IsActive && 
                               m.MembershipStatus == "Active" && 
                               m.MembershipEndDate <= cutoffDate && 
                               m.MembershipEndDate >= DateTime.Today)
                    .OrderBy(m => m.MembershipEndDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMembersExpiringSoon", "MemberManagement", ex);
                throw;
            }
        }

        #endregion

        #region 會員續會

        /// <summary>
        /// 續會
        /// </summary>
        /// <param name="memberId">會員 ID</param>
        /// <param name="months">續會月數</param>
        /// <param name="fee">續會費用</param>
        /// <param name="renewedBy">續會處理者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<MemberOperationResult> RenewMembershipAsync(int memberId, int months, 
            decimal fee, string renewedBy)
        {
            try
            {
                var member = await GetMemberByIdAsync(memberId);
                if (member == null)
                {
                    return new MemberOperationResult
                    {
                        Success = false,
                        Message = "找不到指定的會員資料"
                    };
                }

                if (months <= 0)
                {
                    return new MemberOperationResult
                    {
                        Success = false,
                        Message = "續會月數必須大於0"
                    };
                }

                var oldEndDate = member.MembershipEndDate.ToString("yyyy-MM-dd");

                member.RenewMembership(months, fee);
                member.ModifiedBy = renewedBy;
                member.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var newEndDate = member.MembershipEndDate.ToString("yyyy-MM-dd");

                // 記錄審計日誌
                await _auditService.LogAsync("RENEW_MEMBERSHIP", "MemberManagement", renewedBy,
                    $"會員續會: {member.DisplayName} | 續會{months}個月 | " +
                    $"舊到期日: {oldEndDate} | 新到期日: {newEndDate} | 費用: ${fee}", 
                    memberId.ToString(), "SUCCESS");

                return new MemberOperationResult
                {
                    Success = true,
                    Message = $"會員續會成功，新到期日：{newEndDate}",
                    MemberId = memberId
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("RENEW_MEMBERSHIP", "MemberManagement", renewedBy,
                    $"續會失敗: {ex.Message}", memberId.ToString(), "ERROR");
                throw;
            }
        }

        #endregion

        #region 統計和報表

        /// <summary>
        /// 取得會員統計資訊
        /// </summary>
        /// <returns>統計資訊</returns>
        public async Task<MemberStatistics> GetMemberStatisticsAsync()
        {
            try
            {
                var allMembers = await _context.Set<Member>().ToListAsync();
                var activeMembers = allMembers.Where(m => m.IsActive).ToList();

                var currentMonth = DateTime.Today.Month;
                var currentYear = DateTime.Today.Year;

                var monthlyRevenue = await _context.Set<Member>()
                    .Where(m => m.ModifiedDate.HasValue && 
                               m.ModifiedDate.Value.Month == currentMonth && 
                               m.ModifiedDate.Value.Year == currentYear && 
                               m.MembershipFee.HasValue)
                    .SumAsync(m => m.MembershipFee ?? 0);

                return new MemberStatistics
                {
                    TotalMembers = allMembers.Count,
                    ActiveMembers = activeMembers.Count(m => m.MembershipStatus == "Active" && !m.IsMembershipExpired),
                    ExpiredMembers = activeMembers.Count(m => m.IsMembershipExpired),
                    ExpiringSoonMembers = activeMembers.Count(m => m.IsMembershipExpiringSoon),
                    RegularMembers = activeMembers.Count(m => m.MemberType == MemberConstants.MemberTypes.Regular),
                    StudentMembers = activeMembers.Count(m => m.MemberType == MemberConstants.MemberTypes.Student),
                    SeniorMembers = activeMembers.Count(m => m.MemberType == MemberConstants.MemberTypes.Senior),
                    FamilyMembers = activeMembers.Count(m => m.MemberType == MemberConstants.MemberTypes.Family),
                    TotalMembershipRevenue = monthlyRevenue
                };
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMemberStatistics", "MemberManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 檢查會員號碼是否可用
        /// </summary>
        /// <param name="memberNumber">會員號碼</param>
        /// <param name="excludeMemberId">排除的會員 ID（更新時使用）</param>
        /// <returns>是否可用</returns>
        public async Task<bool> IsMemberNumberAvailableAsync(string memberNumber, int? excludeMemberId = null)
        {
            if (string.IsNullOrEmpty(memberNumber))
                return false;

            try
            {
                var query = _context.Set<Member>().Where(m => m.MemberNumber == memberNumber);

                if (excludeMemberId.HasValue)
                {
                    query = query.Where(m => m.Id != excludeMemberId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("IsMemberNumberAvailable", "MemberManagement", ex);
                return false;
            }
        }

        /// <summary>
        /// 生成下一個可用的會員號碼（統一格式：CWD年度6位序號）
        /// </summary>
        /// <returns>會員號碼</returns>
        public async Task<string> GenerateNextMemberNumberAsync()
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var yearPrefix = $"CWD{currentYear}";

                // 取得當年度最大序號
                var maxNumber = await GetMaxMemberNumberByYearAsync(currentYear);
                var nextSequence = maxNumber + 1;

                return $"{yearPrefix}{nextSequence:D6}";
            }
            catch (Exception ex)
            {
                _auditService.LogError("GenerateNextMemberNumber", "MemberManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 取得指定年度的最大會員序號
        /// </summary>
        /// <param name="year">年度</param>
        /// <returns>最大序號</returns>
        public async Task<int> GetMaxMemberNumberByYearAsync(int year)
        {
            try
            {
                var yearPrefix = $"CWD{year}";
                
                var lastNumber = await _context.Set<Member>()
                    .Where(m => m.MemberNumber.StartsWith(yearPrefix))
                    .OrderByDescending(m => m.MemberNumber)
                    .Select(m => m.MemberNumber)
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(lastNumber) || lastNumber.Length < yearPrefix.Length + 6)
                {
                    return 0; // 當年度第一個會員
                }

                var sequencePart = lastNumber.Substring(yearPrefix.Length);
                if (int.TryParse(sequencePart, out int currentSequence))
                {
                    return currentSequence;
                }

                return 0;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMaxMemberNumberByYear", "MemberManagement", ex);
                return 0;
            }
        }

        /// <summary>
        /// 新增會員
        /// </summary>
        /// <param name="member">會員資料</param>
        /// <returns>新增的會員物件</returns>
        public async Task<Member> CreateMemberAsync(Member member)
        {
            try
            {
                if (member == null)
                    throw new ArgumentNullException(nameof(member));

                // 驗證必要欄位
                ValidateMemberData(member);

                // 檢查會員號碼是否已存在
                var existingMember = await GetMemberByNumberAsync(member.MemberNumber);
                if (existingMember != null)
                {
                    throw new InvalidOperationException($"會員號碼 {member.MemberNumber} 已存在");
                }

                // 設定建立時間
                member.CreatedDate = DateTime.Now;
                member.UpdatedDate = DateTime.Now;

                _context.Set<Member>().Add(member);
                await _context.SaveChangesAsync();

                return member;
            }
            catch (Exception ex)
            {
                _auditService.LogError("CreateMember", "MemberManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 更新會員資料
        /// </summary>
        /// <param name="member">會員資料</param>
        /// <returns>更新後的會員物件</returns>
        public async Task<Member> UpdateMemberAsync(Member member)
        {
            try
            {
                if (member == null)
                    throw new ArgumentNullException(nameof(member));

                // 驗證必要欄位
                ValidateMemberData(member);

                var existingMember = await GetMemberByIdAsync(member.Id);
                if (existingMember == null)
                {
                    throw new InvalidOperationException($"找不到 ID 為 {member.Id} 的會員");
                }

                // 更新欄位
                existingMember.FullName = member.FullName;
                existingMember.Gender = member.Gender;
                existingMember.DateOfBirth = member.DateOfBirth;
                existingMember.HKID = member.HKID;
                existingMember.Phone = member.Phone;
                existingMember.Email = member.Email;
                existingMember.Address = member.Address;
                existingMember.MemberType = member.MemberType;
                existingMember.MembershipFee = member.MembershipFee;
                existingMember.PaymentMethod = member.PaymentMethod;
                existingMember.EmergencyContactName = member.EmergencyContactName;
                existingMember.EmergencyContactPhone = member.EmergencyContactPhone;
                existingMember.PhotoPath = member.PhotoPath;
                existingMember.BarcodePath = member.BarcodePath;
                existingMember.Remarks = member.Remarks;
                existingMember.UpdatedBy = member.UpdatedBy;
                existingMember.UpdatedDate = DateTime.Now;

                await _context.SaveChangesAsync();

                return existingMember;
            }
            catch (Exception ex)
            {
                _auditService.LogError("UpdateMember", "MemberManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 驗證會員資料
        /// </summary>
        /// <param name="member">會員資料</param>
        private void ValidateMemberData(Member member)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(member.FullName))
                errors.Add("會員姓名不能為空");

            if (string.IsNullOrWhiteSpace(member.Gender))
                errors.Add("性別不能為空");

            if (member.DateOfBirth == default(DateTime))
                errors.Add("出生日期不能為空");

            if (string.IsNullOrWhiteSpace(member.Phone))
                errors.Add("電話號碼不能為空");

            if (string.IsNullOrWhiteSpace(member.Address))
                errors.Add("地址不能為空");

            if (string.IsNullOrWhiteSpace(member.MemberType))
                errors.Add("會員類型不能為空");

            if (member.JoinDate == default(DateTime))
                errors.Add("入會日期不能為空");

            if (string.IsNullOrWhiteSpace(member.PaymentMethod))
                errors.Add("付款方式不能為空");

            // 檢查年齡
            var age = DateTime.Now.Year - member.DateOfBirth.Year;
            if (member.DateOfBirth > DateTime.Now.AddYears(-age)) age--;
            if (age < 16)
                errors.Add("會員年齡必須滿16歲");

            if (errors.Any())
            {
                throw new ArgumentException($"會員資料驗證失敗：{string.Join(", ", errors)}");
            }
        }

        /// <summary>
        /// 生成下一個可用的會員號碼（相容舊版）
        /// </summary>
        /// <param name="memberType">會員類型</param>
        /// <returns>會員號碼</returns>
        public async Task<string> GenerateNextMemberNumberAsync(string memberType)
        {
            // 統一使用新格式，忽略會員類型參數
            return await GenerateNextMemberNumberAsync();
        }

        #endregion

        #region IDisposable

        private bool _disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                _auditService?.Dispose();
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }

    #region 輔助類別

    /// <summary>
    /// 會員操作結果
    /// </summary>
    public class MemberOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public int? MemberId { get; set; }
    }

    #endregion
}