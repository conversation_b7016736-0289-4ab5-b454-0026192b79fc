using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 職員管理服務 - 處理職員與導師資料的增刪修查、合約管理、工時統計等功能
    /// </summary>
    public class StaffService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        public StaffService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
        }

        public StaffService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _auditService = new AuditService();
        }

        #region 職員基本管理

        /// <summary>
        /// 取得所有職員列表
        /// </summary>
        /// <param name="includeInactive">是否包含非活躍職員</param>
        /// <returns>職員列表</returns>
        public async Task<List<Staff>> GetAllStaffAsync(bool includeInactive = false)
        {
            try
            {
                var query = _context.Set<Staff>().AsQueryable();

                if (!includeInactive)
                {
                    query = query.Where(s => s.IsActive);
                }

                return await query
                    .OrderBy(s => s.StaffType)
                    .ThenBy(s => s.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetAllStaff", "StaffManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 根據搜尋條件取得職員列表
        /// </summary>
        /// <param name="criteria">搜尋條件</param>
        /// <returns>職員列表和總數</returns>
        public async Task<(List<Staff> Staff, int TotalCount)> SearchStaffAsync(StaffSearchCriteria criteria)
        {
            try
            {
                var query = _context.Set<Staff>().AsQueryable();

                // 應用搜尋條件
                if (!string.IsNullOrEmpty(criteria.Keyword))
                {
                    var keyword = criteria.Keyword.Trim().ToLower();
                    query = query.Where(s => 
                        s.FullName.ToLower().Contains(keyword) ||
                        s.EmployeeCode.ToLower().Contains(keyword) ||
                        (s.Department != null && s.Department.ToLower().Contains(keyword)) ||
                        (s.Position != null && s.Position.ToLower().Contains(keyword))
                    );
                }

                if (!string.IsNullOrEmpty(criteria.StaffType))
                {
                    query = query.Where(s => s.StaffType == criteria.StaffType);
                }

                if (!string.IsNullOrEmpty(criteria.EmploymentType))
                {
                    query = query.Where(s => s.EmploymentType == criteria.EmploymentType);
                }

                if (!string.IsNullOrEmpty(criteria.EmploymentStatus))
                {
                    query = query.Where(s => s.EmploymentStatus == criteria.EmploymentStatus);
                }

                if (!string.IsNullOrEmpty(criteria.Department))
                {
                    query = query.Where(s => s.Department == criteria.Department);
                }

                if (criteria.HireDateFrom.HasValue)
                {
                    query = query.Where(s => s.HireDate >= criteria.HireDateFrom.Value);
                }

                if (criteria.HireDateTo.HasValue)
                {
                    query = query.Where(s => s.HireDate <= criteria.HireDateTo.Value);
                }

                if (criteria.IsActive.HasValue)
                {
                    query = query.Where(s => s.IsActive == criteria.IsActive.Value);
                }

                // 總數
                var totalCount = await query.CountAsync();

                // 排序
                switch (criteria.SortField?.ToLower())
                {
                    case "employeecode":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(s => s.EmployeeCode) : 
                            query.OrderBy(s => s.EmployeeCode);
                        break;
                    case "stafftype":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(s => s.StaffType) : 
                            query.OrderBy(s => s.StaffType);
                        break;
                    case "hiredate":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(s => s.HireDate) : 
                            query.OrderBy(s => s.HireDate);
                        break;
                    case "employmentstatus":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(s => s.EmploymentStatus) : 
                            query.OrderBy(s => s.EmploymentStatus);
                        break;
                    default:
                        query = criteria.SortDirection?.ToUpper() == "DESC" ? 
                            query.OrderByDescending(s => s.FullName) : 
                            query.OrderBy(s => s.FullName);
                        break;
                }

                // 分頁
                var staff = await query
                    .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                    .Take(criteria.PageSize)
                    .ToListAsync();

                return (staff, totalCount);
            }
            catch (Exception ex)
            {
                _auditService.LogError("SearchStaff", "StaffManagement", ex, 
                    $"Criteria: {Newtonsoft.Json.JsonConvert.SerializeObject(criteria)}");
                throw;
            }
        }

        /// <summary>
        /// 根據 ID 取得職員資料
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        /// <returns>職員資料</returns>
        public async Task<Staff> GetStaffByIdAsync(int staffId)
        {
            if (staffId <= 0)
                throw new ArgumentException("職員 ID 必須大於 0", nameof(staffId));

            try
            {
                return await _context.Set<Staff>()
                    .Include(s => s.StaffActivities)
                    .Include(s => s.StaffTimesheets)
                    .FirstOrDefaultAsync(s => s.Id == staffId);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetStaffById", "StaffManagement", ex, 
                    $"staffId: {staffId}");
                throw;
            }
        }

        /// <summary>
        /// 根據員工編號取得職員資料
        /// </summary>
        /// <param name="employeeCode">員工編號</param>
        /// <returns>職員資料</returns>
        public async Task<Staff> GetStaffByEmployeeCodeAsync(string employeeCode)
        {
            if (string.IsNullOrEmpty(employeeCode))
                throw new ArgumentException("員工編號不能為空", nameof(employeeCode));

            try
            {
                return await _context.Set<Staff>()
                    .FirstOrDefaultAsync(s => s.EmployeeCode == employeeCode);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetStaffByEmployeeCode", "StaffManagement", ex, 
                    $"employeeCode: {employeeCode}");
                throw;
            }
        }

        /// <summary>
        /// 建立新職員
        /// </summary>
        /// <param name="staff">職員資料</param>
        /// <param name="createdBy">建立者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<StaffOperationResult> CreateStaffAsync(Staff staff, string createdBy)
        {
            if (staff == null)
                throw new ArgumentNullException(nameof(staff));

            if (string.IsNullOrEmpty(createdBy))
                throw new ArgumentException("建立者不能為空", nameof(createdBy));

            try
            {
                // 驗證資料
                var validationErrors = staff.Validate();
                if (validationErrors.Any())
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "資料驗證失敗",
                        Errors = validationErrors
                    };
                }

                // 檢查員工編號是否重複
                if (await _context.Set<Staff>().AnyAsync(s => s.EmployeeCode == staff.EmployeeCode))
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "員工編號已存在"
                    };
                }

                // 檢查身份證號碼是否重複
                if (await _context.Set<Staff>().AnyAsync(s => s.HKID == staff.HKID))
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "身份證號碼已存在"
                    };
                }

                // 設定建立資訊
                staff.CreatedBy = createdBy;
                staff.CreatedDate = DateTime.UtcNow;
                staff.IsActive = true;

                // 更新合約狀態
                staff.UpdateEmploymentStatus();

                _context.Set<Staff>().Add(staff);
                await _context.SaveChangesAsync();

                // 記錄審計日誌
                await _auditService.LogAsync("CREATE_STAFF", "StaffManagement", createdBy,
                    $"建立職員: {staff.DisplayName}", staff.Id.ToString(), "SUCCESS");

                return new StaffOperationResult
                {
                    Success = true,
                    Message = "職員建立成功",
                    StaffId = staff.Id
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("CREATE_STAFF", "StaffManagement", createdBy,
                    $"建立職員失敗: {ex.Message}", null, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 更新職員資料
        /// </summary>
        /// <param name="staff">職員資料</param>
        /// <param name="modifiedBy">修改者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<StaffOperationResult> UpdateStaffAsync(Staff staff, string modifiedBy)
        {
            if (staff == null)
                throw new ArgumentNullException(nameof(staff));

            if (string.IsNullOrEmpty(modifiedBy))
                throw new ArgumentException("修改者不能為空", nameof(modifiedBy));

            try
            {
                var existingStaff = await GetStaffByIdAsync(staff.Id);
                if (existingStaff == null)
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "找不到指定的職員資料"
                    };
                }

                // 驗證資料
                var validationErrors = staff.Validate();
                if (validationErrors.Any())
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "資料驗證失敗",
                        Errors = validationErrors
                    };
                }

                // 檢查員工編號是否與其他職員重複
                if (await _context.Set<Staff>().AnyAsync(s => s.EmployeeCode == staff.EmployeeCode && s.Id != staff.Id))
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "員工編號已被其他職員使用"
                    };
                }

                // 檢查身份證號碼是否與其他職員重複
                if (await _context.Set<Staff>().AnyAsync(s => s.HKID == staff.HKID && s.Id != staff.Id))
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "身份證號碼已被其他職員使用"
                    };
                }

                // 記錄變更前的資料
                var oldData = $"Name: {existingStaff.FullName}, Code: {existingStaff.EmployeeCode}, Status: {existingStaff.EmploymentStatus}";

                // 更新資料
                _context.Entry(existingStaff).CurrentValues.SetValues(staff);
                existingStaff.ModifiedBy = modifiedBy;
                existingStaff.ModifiedDate = DateTime.UtcNow;

                // 更新合約狀態
                existingStaff.UpdateEmploymentStatus();

                await _context.SaveChangesAsync();

                var newData = $"Name: {existingStaff.FullName}, Code: {existingStaff.EmployeeCode}, Status: {existingStaff.EmploymentStatus}";

                // 記錄審計日誌
                await _auditService.LogAsync("UPDATE_STAFF", "StaffManagement", modifiedBy,
                    $"更新職員: {existingStaff.DisplayName} | 舊值: {oldData} | 新值: {newData}", 
                    staff.Id.ToString(), "SUCCESS");

                return new StaffOperationResult
                {
                    Success = true,
                    Message = "職員資料更新成功",
                    StaffId = staff.Id
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("UPDATE_STAFF", "StaffManagement", modifiedBy,
                    $"更新職員失敗: {ex.Message}", staff.Id.ToString(), "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 刪除職員（軟刪除）
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        /// <param name="deletedBy">刪除者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<StaffOperationResult> DeleteStaffAsync(int staffId, string deletedBy)
        {
            if (staffId <= 0)
                throw new ArgumentException("職員 ID 必須大於 0", nameof(staffId));

            if (string.IsNullOrEmpty(deletedBy))
                throw new ArgumentException("刪除者不能為空", nameof(deletedBy));

            try
            {
                var staff = await GetStaffByIdAsync(staffId);
                if (staff == null)
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "找不到指定的職員資料"
                    };
                }

                // 檢查是否有相關活動記錄
                var hasActivities = await _context.Set<StaffActivity>().AnyAsync(sa => sa.StaffId == staffId);
                if (hasActivities)
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "此職員有相關活動記錄，無法刪除。請先處理相關記錄或使用停用功能。"
                    };
                }

                // 軟刪除
                staff.IsActive = false;
                staff.EmploymentStatus = "Terminated";
                staff.ModifiedBy = deletedBy;
                staff.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // 記錄審計日誌
                await _auditService.LogAsync("DELETE_STAFF", "StaffManagement", deletedBy,
                    $"刪除職員: {staff.DisplayName}", staffId.ToString(), "SUCCESS");

                return new StaffOperationResult
                {
                    Success = true,
                    Message = "職員資料已停用",
                    StaffId = staffId
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("DELETE_STAFF", "StaffManagement", deletedBy,
                    $"刪除職員失敗: {ex.Message}", staffId.ToString(), "ERROR");
                throw;
            }
        }

        #endregion

        #region 合約管理

        /// <summary>
        /// 更新所有職員的合約狀態
        /// </summary>
        /// <param name="updatedBy">更新者 ID</param>
        /// <returns>更新的職員數量</returns>
        public async Task<int> UpdateAllContractStatusesAsync(string updatedBy)
        {
            try
            {
                var expiredStaff = await _context.Set<Staff>()
                    .Where(s => s.IsActive && 
                               s.EmploymentStatus == "Active" && 
                               s.ContractEndDate.HasValue && 
                               s.ContractEndDate.Value < DateTime.Today)
                    .ToListAsync();

                foreach (var staff in expiredStaff)
                {
                    staff.UpdateEmploymentStatus();
                    staff.ModifiedBy = updatedBy;
                    staff.ModifiedDate = DateTime.UtcNow;
                }

                if (expiredStaff.Any())
                {
                    await _context.SaveChangesAsync();

                    // 記錄審計日誌
                    await _auditService.LogAsync("UPDATE_CONTRACT_STATUS", "StaffManagement", updatedBy,
                        $"批量更新合約狀態，共 {expiredStaff.Count} 人", null, "SUCCESS");
                }

                return expiredStaff.Count;
            }
            catch (Exception ex)
            {
                _auditService.LogError("UpdateAllContractStatuses", "StaffManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 取得即將到期的合約列表
        /// </summary>
        /// <param name="daysAhead">提前天數（預設30天）</param>
        /// <returns>即將到期的職員列表</returns>
        public async Task<List<Staff>> GetContractsExpiringSoonAsync(int daysAhead = 30)
        {
            try
            {
                var cutoffDate = DateTime.Today.AddDays(daysAhead);

                return await _context.Set<Staff>()
                    .Where(s => s.IsActive && 
                               s.EmploymentStatus == "Active" && 
                               s.ContractEndDate.HasValue && 
                               s.ContractEndDate.Value <= cutoffDate && 
                               s.ContractEndDate.Value >= DateTime.Today)
                    .OrderBy(s => s.ContractEndDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetContractsExpiringSoon", "StaffManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 續約
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        /// <param name="newStartDate">新合約開始日期</param>
        /// <param name="newEndDate">新合約結束日期</param>
        /// <param name="updatedBy">更新者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<StaffOperationResult> RenewContractAsync(int staffId, DateTime newStartDate, 
            DateTime newEndDate, string updatedBy)
        {
            try
            {
                var staff = await GetStaffByIdAsync(staffId);
                if (staff == null)
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "找不到指定的職員資料"
                    };
                }

                if (newEndDate <= newStartDate)
                {
                    return new StaffOperationResult
                    {
                        Success = false,
                        Message = "合約結束日期必須晚於開始日期"
                    };
                }

                var oldContract = $"開始: {staff.ContractStartDate?.ToString("yyyy-MM-dd")}, 結束: {staff.ContractEndDate?.ToString("yyyy-MM-dd")}";

                staff.ContractStartDate = newStartDate;
                staff.ContractEndDate = newEndDate;
                staff.EmploymentStatus = "Active";
                staff.ModifiedBy = updatedBy;
                staff.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var newContract = $"開始: {newStartDate:yyyy-MM-dd}, 結束: {newEndDate:yyyy-MM-dd}";

                // 記錄審計日誌
                await _auditService.LogAsync("RENEW_CONTRACT", "StaffManagement", updatedBy,
                    $"續約職員: {staff.DisplayName} | 舊合約: {oldContract} | 新合約: {newContract}", 
                    staffId.ToString(), "SUCCESS");

                return new StaffOperationResult
                {
                    Success = true,
                    Message = "合約續約成功",
                    StaffId = staffId
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("RENEW_CONTRACT", "StaffManagement", updatedBy,
                    $"續約失敗: {ex.Message}", staffId.ToString(), "ERROR");
                throw;
            }
        }

        #endregion

        #region 活動與工時管理

        /// <summary>
        /// 新增職員活動參與記錄
        /// </summary>
        /// <param name="staffActivity">活動記錄</param>
        /// <param name="recordedBy">記錄者 ID</param>
        /// <returns>操作結果</returns>
        public async Task<StaffOperationResult> AddStaffActivityAsync(StaffActivity staffActivity, string recordedBy)
        {
            if (staffActivity == null)
                throw new ArgumentNullException(nameof(staffActivity));

            try
            {
                staffActivity.RecordedBy = recordedBy;
                staffActivity.RecordDate = DateTime.UtcNow;

                _context.Set<StaffActivity>().Add(staffActivity);
                await _context.SaveChangesAsync();

                // 記錄審計日誌
                await _auditService.LogAsync("ADD_STAFF_ACTIVITY", "StaffManagement", recordedBy,
                    $"新增活動記錄: 職員 {staffActivity.StaffId}, 活動 {staffActivity.ActivityId}, 時數 {staffActivity.TeachingHours}", 
                    staffActivity.Id.ToString(), "SUCCESS");

                return new StaffOperationResult
                {
                    Success = true,
                    Message = "活動記錄新增成功",
                    StaffId = staffActivity.StaffId
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("ADD_STAFF_ACTIVITY", "StaffManagement", recordedBy,
                    $"新增活動記錄失敗: {ex.Message}", null, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 取得職員的活動參與記錄
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        /// <param name="fromDate">開始日期</param>
        /// <param name="toDate">結束日期</param>
        /// <returns>活動記錄列表</returns>
        public async Task<List<StaffActivity>> GetStaffActivitiesAsync(int staffId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Set<StaffActivity>()
                    .Include(sa => sa.Activity)
                    .Where(sa => sa.StaffId == staffId);

                if (fromDate.HasValue)
                {
                    query = query.Where(sa => sa.Activity.StartDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(sa => sa.Activity.StartDate <= toDate.Value);
                }

                return await query.OrderByDescending(sa => sa.Activity.StartDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetStaffActivities", "StaffManagement", ex, 
                    $"staffId: {staffId}");
                throw;
            }
        }

        /// <summary>
        /// 計算職員的總授課時數
        /// </summary>
        /// <param name="staffId">職員 ID</param>
        /// <param name="fromDate">開始日期</param>
        /// <param name="toDate">結束日期</param>
        /// <returns>授課時數統計</returns>
        public async Task<StaffHoursStatistics> GetStaffHoursStatisticsAsync(int staffId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Set<StaffActivity>()
                    .Include(sa => sa.Activity)
                    .Where(sa => sa.StaffId == staffId);

                if (fromDate.HasValue)
                {
                    query = query.Where(sa => sa.Activity.StartDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(sa => sa.Activity.StartDate <= toDate.Value);
                }

                var activities = await query.ToListAsync();

                return new StaffHoursStatistics
                {
                    StaffId = staffId,
                    TotalTeachingHours = activities.Sum(a => a.TeachingHours),
                    TotalPreparationHours = activities.Sum(a => a.PreparationHours),
                    TotalActivities = activities.Count,
                    TotalFees = activities.Sum(a => a.Fee ?? 0),
                    FromDate = fromDate,
                    ToDate = toDate
                };
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetStaffHoursStatistics", "StaffManagement", ex, 
                    $"staffId: {staffId}");
                throw;
            }
        }

        #endregion

        #region 統計和報表

        /// <summary>
        /// 取得職員統計資訊
        /// </summary>
        /// <returns>統計資訊</returns>
        public async Task<StaffStatistics> GetStaffStatisticsAsync()
        {
            try
            {
                var allStaff = await _context.Set<Staff>().ToListAsync();
                var activeStaff = allStaff.Where(s => s.IsActive).ToList();

                var currentMonth = DateTime.Today.Month;
                var currentYear = DateTime.Today.Year;

                var monthlyHours = await _context.Set<StaffTimesheet>()
                    .Where(ts => ts.WorkDate.Month == currentMonth && ts.WorkDate.Year == currentYear && ts.Status == "Approved")
                    .SumAsync(ts => ts.WorkHours + ts.OvertimeHours);

                return new StaffStatistics
                {
                    TotalStaff = allStaff.Count,
                    ActiveStaff = activeStaff.Count,
                    InactiveStaff = allStaff.Count(s => !s.IsActive),
                    Teachers = activeStaff.Count(s => s.StaffType == StaffConstants.StaffTypes.Teacher),
                    GeneralStaff = activeStaff.Count(s => s.StaffType == StaffConstants.StaffTypes.Staff),
                    Administrators = activeStaff.Count(s => s.StaffType == StaffConstants.StaffTypes.Administrator),
                    ContractsExpiringSoon = activeStaff.Count(s => s.IsContractExpiringSoon),
                    ExpiredContracts = activeStaff.Count(s => s.IsContractExpired),
                    TotalMonthlyHours = monthlyHours,
                    AverageServiceYears = activeStaff.Any() ? activeStaff.Average(s => s.ServiceYears) : 0
                };
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetStaffStatistics", "StaffManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 取得部門列表
        /// </summary>
        /// <returns>部門列表</returns>
        public async Task<List<string>> GetDepartmentsAsync()
        {
            try
            {
                return await _context.Set<Staff>()
                    .Where(s => s.IsActive && !string.IsNullOrEmpty(s.Department))
                    .Select(s => s.Department)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetDepartments", "StaffManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 檢查員工編號是否可用
        /// </summary>
        /// <param name="employeeCode">員工編號</param>
        /// <param name="excludeStaffId">排除的職員 ID（更新時使用）</param>
        /// <returns>是否可用</returns>
        public async Task<bool> IsEmployeeCodeAvailableAsync(string employeeCode, int? excludeStaffId = null)
        {
            if (string.IsNullOrEmpty(employeeCode))
                return false;

            try
            {
                var query = _context.Set<Staff>().Where(s => s.EmployeeCode == employeeCode);

                if (excludeStaffId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeStaffId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("IsEmployeeCodeAvailable", "StaffManagement", ex);
                return false;
            }
        }

        /// <summary>
        /// 生成下一個可用的員工編號
        /// </summary>
        /// <param name="staffType">職員類型</param>
        /// <returns>員工編號</returns>
        public async Task<string> GenerateNextEmployeeCodeAsync(string staffType)
        {
            try
            {
                string prefix;
                switch (staffType)
                {
                    case StaffConstants.StaffTypes.Teacher:
                        prefix = "T";
                        break;
                    case StaffConstants.StaffTypes.Staff:
                        prefix = "S";
                        break;
                    case StaffConstants.StaffTypes.Administrator:
                        prefix = "A";
                        break;
                    default:
                        prefix = "E";
                        break;
                }

                var year = DateTime.Now.Year;
                var yearPrefix = $"{prefix}{year}";

                // 取得今年此類型的最後一個序號
                var lastCode = await _context.Set<Staff>()
                    .Where(s => s.EmployeeCode.StartsWith(yearPrefix))
                    .OrderByDescending(s => s.EmployeeCode)
                    .Select(s => s.EmployeeCode)
                    .FirstOrDefaultAsync();

                int nextSequence = 1;
                if (!string.IsNullOrEmpty(lastCode) && lastCode.Length >= yearPrefix.Length + 4)
                {
                    var sequencePart = lastCode.Substring(yearPrefix.Length);
                    if (int.TryParse(sequencePart, out int currentSequence))
                    {
                        nextSequence = currentSequence + 1;
                    }
                }

                return $"{yearPrefix}{nextSequence:D4}";
            }
            catch (Exception ex)
            {
                _auditService.LogError("GenerateNextEmployeeCode", "StaffManagement", ex);
                throw;
            }
        }

        #endregion

        #region IDisposable

        private bool _disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                _auditService?.Dispose();
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }

    #region 輔助類別

    /// <summary>
    /// 職員操作結果
    /// </summary>
    public class StaffOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public int? StaffId { get; set; }
    }

    /// <summary>
    /// 職員工時統計
    /// </summary>
    public class StaffHoursStatistics
    {
        public int StaffId { get; set; }
        public decimal TotalTeachingHours { get; set; }
        public decimal TotalPreparationHours { get; set; }
        public int TotalActivities { get; set; }
        public decimal TotalFees { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    #endregion
}