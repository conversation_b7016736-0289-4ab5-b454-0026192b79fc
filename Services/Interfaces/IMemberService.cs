using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services.Interfaces
{
    /// <summary>
    /// 會員服務接口
    /// </summary>
    public interface IMemberService
    {
        /// <summary>
        /// 取得所有會員
        /// </summary>
        /// <returns>會員列表</returns>
        Task<IEnumerable<Member>> GetAllMembersAsync();

        /// <summary>
        /// 根據ID取得會員
        /// </summary>
        /// <param name="id">會員ID</param>
        /// <returns>會員資料</returns>
        Task<Member> GetMemberByIdAsync(string id);

        /// <summary>
        /// 根據電子郵件取得會員
        /// </summary>
        /// <param name="email">電子郵件</param>
        /// <returns>會員資料</returns>
        Task<Member> GetMemberByEmailAsync(string email);

        /// <summary>
        /// 新增會員
        /// </summary>
        /// <param name="member">會員資料</param>
        /// <returns>操作結果</returns>
        Task<bool> CreateMemberAsync(Member member);

        /// <summary>
        /// 更新會員資料
        /// </summary>
        /// <param name="member">會員資料</param>
        /// <returns>操作結果</returns>
        Task<bool> UpdateMemberAsync(Member member);

        /// <summary>
        /// 刪除會員
        /// </summary>
        /// <param name="id">會員ID</param>
        /// <returns>操作結果</returns>
        Task<bool> DeleteMemberAsync(string id);

        /// <summary>
        /// 搜尋會員
        /// </summary>
        /// <param name="searchTerm">搜尋關鍵字</param>
        /// <returns>符合條件的會員列表</returns>
        Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm);

        /// <summary>
        /// 驗證會員資料
        /// </summary>
        /// <param name="member">會員資料</param>
        /// <returns>驗證結果</returns>
        bool ValidateMember(Member member);

        /// <summary>
        /// 檢查電子郵件是否已存在
        /// </summary>
        /// <param name="email">電子郵件</param>
        /// <param name="excludeId">排除的會員ID（用於更新時檢查）</param>
        /// <returns>是否已存在</returns>
        Task<bool> IsEmailExistsAsync(string email, string excludeId = null);

        /// <summary>
        /// 取得會員統計資料
        /// </summary>
        /// <returns>統計資料</returns>
        Task<Dictionary<string, object>> GetMemberStatisticsAsync();

        /// <summary>
        /// 啟用會員
        /// </summary>
        /// <param name="id">會員ID</param>
        /// <returns>操作結果</returns>
        Task<bool> ActivateMemberAsync(string id);

        /// <summary>
        /// 停用會員
        /// </summary>
        /// <param name="id">會員ID</param>
        /// <returns>操作結果</returns>
        Task<bool> DeactivateMemberAsync(string id);
    }
}