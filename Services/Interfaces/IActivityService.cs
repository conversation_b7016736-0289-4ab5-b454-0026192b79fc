using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services.Interfaces
{
    /// <summary>
    /// 活動服務接口
    /// </summary>
    public interface IActivityService
    {
        /// <summary>
        /// 取得所有活動
        /// </summary>
        /// <returns>活動列表</returns>
        Task<IEnumerable<Activity>> GetAllActivitiesAsync();

        /// <summary>
        /// 根據ID取得活動
        /// </summary>
        /// <param name="id">活動ID</param>
        /// <returns>活動資料</returns>
        Task<Activity> GetActivityByIdAsync(string id);

        /// <summary>
        /// 新增活動
        /// </summary>
        /// <param name="activity">活動資料</param>
        /// <returns>操作結果</returns>
        Task<bool> CreateActivityAsync(Activity activity);

        /// <summary>
        /// 更新活動資料
        /// </summary>
        /// <param name="activity">活動資料</param>
        /// <returns>操作結果</returns>
        Task<bool> UpdateActivityAsync(Activity activity);

        /// <summary>
        /// 刪除活動
        /// </summary>
        /// <param name="id">活動ID</param>
        /// <returns>操作結果</returns>
        Task<bool> DeleteActivityAsync(string id);

        /// <summary>
        /// 搜尋活動
        /// </summary>
        /// <param name="searchTerm">搜尋關鍵字</param>
        /// <returns>符合條件的活動列表</returns>
        Task<IEnumerable<Activity>> SearchActivitiesAsync(string searchTerm);

        /// <summary>
        /// 取得即將舉行的活動
        /// </summary>
        /// <param name="days">天數範圍</param>
        /// <returns>活動列表</returns>
        Task<IEnumerable<Activity>> GetUpcomingActivitiesAsync(int days = 30);

        /// <summary>
        /// 取得已結束的活動
        /// </summary>
        /// <returns>活動列表</returns>
        Task<IEnumerable<Activity>> GetPastActivitiesAsync();

        /// <summary>
        /// 會員報名活動
        /// </summary>
        /// <param name="activityId">活動ID</param>
        /// <param name="memberId">會員ID</param>
        /// <returns>操作結果</returns>
        Task<bool> RegisterMemberForActivityAsync(string activityId, string memberId);

        /// <summary>
        /// 會員取消報名
        /// </summary>
        /// <param name="activityId">活動ID</param>
        /// <param name="memberId">會員ID</param>
        /// <returns>操作結果</returns>
        Task<bool> UnregisterMemberFromActivityAsync(string activityId, string memberId);

        /// <summary>
        /// 取得活動參與者列表
        /// </summary>
        /// <param name="activityId">活動ID</param>
        /// <returns>參與者列表</returns>
        Task<IEnumerable<Member>> GetActivityParticipantsAsync(string activityId);

        /// <summary>
        /// 取得會員參與的活動列表
        /// </summary>
        /// <param name="memberId">會員ID</param>
        /// <returns>活動列表</returns>
        Task<IEnumerable<Activity>> GetMemberActivitiesAsync(string memberId);

        /// <summary>
        /// 驗證活動資料
        /// </summary>
        /// <param name="activity">活動資料</param>
        /// <returns>驗證結果</returns>
        bool ValidateActivity(Activity activity);

        /// <summary>
        /// 取得活動統計資料
        /// </summary>
        /// <returns>統計資料</returns>
        Task<Dictionary<string, object>> GetActivityStatisticsAsync();

        /// <summary>
        /// 根據類型取得活動
        /// </summary>
        /// <param name="activityType">活動類型</param>
        /// <returns>活動列表</returns>
        Task<IEnumerable<Activity>> GetActivitiesByTypeAsync(string activityType);

        /// <summary>
        /// 根據狀態取得活動
        /// </summary>
        /// <param name="status">活動狀態</param>
        /// <returns>活動列表</returns>
        Task<IEnumerable<Activity>> GetActivitiesByStatusAsync(string status);

        /// <summary>
        /// 開放活動報名
        /// </summary>
        /// <param name="id">活動ID</param>
        /// <returns>操作結果</returns>
        Task<bool> OpenActivityRegistrationAsync(string id);

        /// <summary>
        /// 關閉活動報名
        /// </summary>
        /// <param name="id">活動ID</param>
        /// <returns>操作結果</returns>
        Task<bool> CloseActivityRegistrationAsync(string id);

        /// <summary>
        /// 取消活動
        /// </summary>
        /// <param name="id">活動ID</param>
        /// <returns>操作結果</returns>
        Task<bool> CancelActivityAsync(string id);
    }
}