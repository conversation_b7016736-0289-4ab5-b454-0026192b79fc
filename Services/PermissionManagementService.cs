using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 權限管理服務 - 處理角色權限的配置和查詢
    /// </summary>
    public class PermissionManagementService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;
        private readonly RoleManagementService _roleService;

        public PermissionManagementService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
            _roleService = new RoleManagementService(_context);
        }

        public PermissionManagementService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _auditService = new AuditService();
            _roleService = new RoleManagementService(_context);
        }

        #region 權限矩陣管理

        /// <summary>
        /// 取得完整的權限矩陣
        /// </summary>
        /// <param name="includeInactiveRoles">是否包含停用的角色</param>
        /// <param name="includeInactiveModules">是否包含停用的模組</param>
        /// <returns>權限矩陣</returns>
        public async Task<PermissionMatrix> GetPermissionMatrixAsync(bool includeInactiveRoles = false, 
            bool includeInactiveModules = false)
        {
            try
            {
                var matrix = new PermissionMatrix();

                // 取得角色清單
                var rolesQuery = _context.ApplicationRoles.AsQueryable();
                if (!includeInactiveRoles)
                {
                    rolesQuery = rolesQuery.Where(r => r.IsActive);
                }
                matrix.Roles = await rolesQuery.OrderBy(r => r.Name).ToListAsync();

                // 取得模組清單
                var modulesQuery = _context.SystemModules.AsQueryable();
                if (!includeInactiveModules)
                {
                    modulesQuery = modulesQuery.Where(m => m.IsActive);
                }
                matrix.Modules = await modulesQuery.OrderBy(m => m.Category).ThenBy(m => m.SortOrder).ToListAsync();

                // 取得所有權限記錄
                matrix.Permissions = await _context.RolePermissions
                    .Where(rp => rp.IsActive)
                    .Include(rp => rp.Role)
                    .Include(rp => rp.Module)
                    .ToListAsync();

                // 建立權限索引，以便快速查找
                matrix.PermissionIndex = matrix.Permissions
                    .ToDictionary(p => $"{p.RoleId}_{p.ModuleName}", p => p);

                return matrix;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetPermissionMatrix", "PermissionManagement", ex);
                throw;
            }
        }

        /// <summary>
        /// 取得角色的所有權限
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <returns>權限清單</returns>
        public async Task<List<RolePermission>> GetRolePermissionsAsync(string roleId)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            try
            {
                return await _context.RolePermissions
                    .Where(rp => rp.RoleId == roleId && rp.IsActive)
                    .Include(rp => rp.Module)
                    .OrderBy(rp => rp.Module.Category)
                    .ThenBy(rp => rp.Module.SortOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetRolePermissions", "PermissionManagement", ex, 
                    $"roleId: {roleId}");
                throw;
            }
        }

        /// <summary>
        /// 取得模組的所有權限配置
        /// </summary>
        /// <param name="moduleName">模組名稱</param>
        /// <returns>權限清單</returns>
        public async Task<List<RolePermission>> GetModulePermissionsAsync(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
                throw new ArgumentException("模組名稱不能為空", nameof(moduleName));

            try
            {
                return await _context.RolePermissions
                    .Where(rp => rp.ModuleName == moduleName && rp.IsActive)
                    .Include(rp => rp.Role)
                    .OrderBy(rp => rp.Role.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetModulePermissions", "PermissionManagement", ex, 
                    $"moduleName: {moduleName}");
                throw;
            }
        }

        /// <summary>
        /// 取得特定角色和模組的權限
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="moduleName">模組名稱</param>
        /// <returns>權限物件，如果不存在則返回 null</returns>
        public async Task<RolePermission> GetRoleModulePermissionAsync(string roleId, string moduleName)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(moduleName))
                throw new ArgumentException("模組名稱不能為空", nameof(moduleName));

            try
            {
                return await _context.RolePermissions
                    .Include(rp => rp.Role)
                    .Include(rp => rp.Module)
                    .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.ModuleName == moduleName);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetRoleModulePermission", "PermissionManagement", ex, 
                    $"roleId: {roleId}, moduleName: {moduleName}");
                throw;
            }
        }

        #endregion

        #region 權限設定

        /// <summary>
        /// 設定角色權限
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="moduleName">模組名稱</param>
        /// <param name="canRead">可讀取</param>
        /// <param name="canCreate">可建立</param>
        /// <param name="canUpdate">可更新</param>
        /// <param name="canDelete">可刪除</param>
        /// <param name="modifiedBy">修改者用戶 ID</param>
        /// <returns>操作結果</returns>
        public async Task<PermissionResult> SetRolePermissionAsync(string roleId, string moduleName,
            bool canRead, bool canCreate, bool canUpdate, bool canDelete, string modifiedBy)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(moduleName))
                throw new ArgumentException("模組名稱不能為空", nameof(moduleName));

            if (string.IsNullOrEmpty(modifiedBy))
                throw new ArgumentException("修改者不能為空", nameof(modifiedBy));

            try
            {
                // 驗證角色是否存在且啟用
                var role = await _context.ApplicationRoles.FirstOrDefaultAsync(r => r.Id == roleId);
                if (role == null)
                {
                    return new PermissionResult { Success = false, Message = "找不到指定的角色" };
                }

                if (!role.IsActive)
                {
                    return new PermissionResult { Success = false, Message = "無法為停用的角色設定權限" };
                }

                // 驗證模組是否存在且啟用
                var module = await _context.SystemModules.FirstOrDefaultAsync(m => m.ModuleName == moduleName);
                if (module == null)
                {
                    return new PermissionResult { Success = false, Message = "找不到指定的模組" };
                }

                if (!module.IsActive)
                {
                    return new PermissionResult { Success = false, Message = "無法為停用的模組設定權限" };
                }

                // 查找現有權限記錄
                var existingPermission = await GetRoleModulePermissionAsync(roleId, moduleName);

                if (existingPermission != null)
                {
                    // 更新現有權限
                    var oldPermissions = $"Read: {existingPermission.CanRead}, Create: {existingPermission.CanCreate}, Update: {existingPermission.CanUpdate}, Delete: {existingPermission.CanDelete}";

                    existingPermission.CanRead = canRead;
                    existingPermission.CanCreate = canCreate;
                    existingPermission.CanUpdate = canUpdate;
                    existingPermission.CanDelete = canDelete;
                    existingPermission.ModifiedBy = modifiedBy;
                    existingPermission.ModifiedDate = DateTime.UtcNow;
                    existingPermission.IsActive = canRead || canCreate || canUpdate || canDelete; // 如果所有權限都是 false，則標記為停用

                    var newPermissions = $"Read: {canRead}, Create: {canCreate}, Update: {canUpdate}, Delete: {canDelete}";

                    await _context.SaveChangesAsync();

                    await _auditService.LogAsync("UPDATE_PERMISSION", "PermissionManagement", modifiedBy,
                        $"更新權限: 角色 {role.Name} -> 模組 {module.DisplayName} | 舊值: {oldPermissions} | 新值: {newPermissions}", 
                        existingPermission.Id.ToString(), "SUCCESS");
                }
                else
                {
                    // 建立新的權限記錄
                    var newPermission = new RolePermission
                    {
                        RoleId = roleId,
                        ModuleName = moduleName,
                        CanRead = canRead,
                        CanCreate = canCreate,
                        CanUpdate = canUpdate,
                        CanDelete = canDelete,
                        CreatedBy = modifiedBy,
                        CreatedDate = DateTime.UtcNow,
                        IsActive = canRead || canCreate || canUpdate || canDelete
                    };

                    _context.RolePermissions.Add(newPermission);
                    await _context.SaveChangesAsync();

                    var permissions = $"Read: {canRead}, Create: {canCreate}, Update: {canUpdate}, Delete: {canDelete}";

                    await _auditService.LogAsync("CREATE_PERMISSION", "PermissionManagement", modifiedBy,
                        $"建立權限: 角色 {role.Name} -> 模組 {module.DisplayName} | 權限: {permissions}", 
                        newPermission.Id.ToString(), "SUCCESS");
                }

                return new PermissionResult { Success = true, Message = "權限設定成功" };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("SET_PERMISSION", "PermissionManagement", modifiedBy,
                    $"設定權限異常: roleId {roleId}, moduleName {moduleName} - {ex.Message}", 
                    null, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 批量設定角色權限
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="permissions">權限設定清單</param>
        /// <param name="modifiedBy">修改者用戶 ID</param>
        /// <returns>操作結果</returns>
        public async Task<PermissionResult> SetRolePermissionsBatchAsync(string roleId, 
            List<ModulePermissionSetting> permissions, string modifiedBy)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (permissions == null || !permissions.Any())
                throw new ArgumentException("權限設定不能為空", nameof(permissions));

            if (string.IsNullOrEmpty(modifiedBy))
                throw new ArgumentException("修改者不能為空", nameof(modifiedBy));

            try
            {
                using (var transaction = _context.Database.BeginTransaction())
                {
                    try
                    {
                        var results = new List<string>();

                        foreach (var permission in permissions)
                        {
                            var result = await SetRolePermissionAsync(roleId, permission.ModuleName,
                                permission.CanRead, permission.CanCreate, permission.CanUpdate, permission.CanDelete,
                                modifiedBy);

                            if (!result.Success)
                            {
                                results.Add($"{permission.ModuleName}: {result.Message}");
                            }
                        }

                        if (results.Any())
                        {
                            transaction.Rollback();
                            return new PermissionResult 
                            { 
                                Success = false, 
                                Message = $"部分權限設定失敗: {string.Join("; ", results)}" 
                            };
                        }

                        transaction.Commit();

                        var role = await _context.ApplicationRoles.FirstOrDefaultAsync(r => r.Id == roleId);
                        await _auditService.LogAsync("BATCH_SET_PERMISSIONS", "PermissionManagement", modifiedBy,
                            $"批量設定權限: 角色 {role?.Name} | 模組數量: {permissions.Count}", 
                            roleId, "SUCCESS");

                        return new PermissionResult { Success = true, Message = "批量權限設定成功" };
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("BATCH_SET_PERMISSIONS", "PermissionManagement", modifiedBy,
                    $"批量設定權限異常: roleId {roleId} - {ex.Message}", roleId, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 複製角色權限
        /// </summary>
        /// <param name="sourceRoleId">來源角色 ID</param>
        /// <param name="targetRoleId">目標角色 ID</param>
        /// <param name="overwriteExisting">是否覆蓋現有權限</param>
        /// <param name="copiedBy">複製者用戶 ID</param>
        /// <returns>操作結果</returns>
        public async Task<PermissionResult> CopyRolePermissionsAsync(string sourceRoleId, string targetRoleId, 
            bool overwriteExisting, string copiedBy)
        {
            if (string.IsNullOrEmpty(sourceRoleId))
                throw new ArgumentException("來源角色 ID 不能為空", nameof(sourceRoleId));

            if (string.IsNullOrEmpty(targetRoleId))
                throw new ArgumentException("目標角色 ID 不能為空", nameof(targetRoleId));

            if (string.IsNullOrEmpty(copiedBy))
                throw new ArgumentException("複製者不能為空", nameof(copiedBy));

            try
            {
                using (var transaction = _context.Database.BeginTransaction())
                {
                    try
                    {
                        // 驗證來源和目標角色
                        var sourceRole = await _context.ApplicationRoles.FirstOrDefaultAsync(r => r.Id == sourceRoleId);
                        var targetRole = await _context.ApplicationRoles.FirstOrDefaultAsync(r => r.Id == targetRoleId);

                        if (sourceRole == null)
                            return new PermissionResult { Success = false, Message = "找不到來源角色" };

                        if (targetRole == null)
                            return new PermissionResult { Success = false, Message = "找不到目標角色" };

                        if (!targetRole.IsActive)
                            return new PermissionResult { Success = false, Message = "無法為停用的角色複製權限" };

                        // 取得來源角色的所有權限
                        var sourcePermissions = await GetRolePermissionsAsync(sourceRoleId);

                        if (!sourcePermissions.Any())
                            return new PermissionResult { Success = false, Message = "來源角色沒有任何權限可供複製" };

                        int copiedCount = 0;
                        int skippedCount = 0;

                        foreach (var sourcePermission in sourcePermissions)
                        {
                            var existingPermission = await GetRoleModulePermissionAsync(targetRoleId, sourcePermission.ModuleName);

                            if (existingPermission != null && !overwriteExisting)
                            {
                                skippedCount++;
                                continue;
                            }

                            var result = await SetRolePermissionAsync(targetRoleId, sourcePermission.ModuleName,
                                sourcePermission.CanRead, sourcePermission.CanCreate, 
                                sourcePermission.CanUpdate, sourcePermission.CanDelete, copiedBy);

                            if (result.Success)
                            {
                                copiedCount++;
                            }
                        }

                        transaction.Commit();

                        await _auditService.LogAsync("COPY_PERMISSIONS", "PermissionManagement", copiedBy,
                            $"複製權限: 從角色 {sourceRole.Name} 到 {targetRole.Name} | 複製: {copiedCount}, 跳過: {skippedCount}", 
                            targetRoleId, "SUCCESS");

                        return new PermissionResult 
                        { 
                            Success = true, 
                            Message = $"權限複製完成。複製了 {copiedCount} 個權限，跳過 {skippedCount} 個權限" 
                        };
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("COPY_PERMISSIONS", "PermissionManagement", copiedBy,
                    $"複製權限異常: 從 {sourceRoleId} 到 {targetRoleId} - {ex.Message}", 
                    targetRoleId, "ERROR");
                throw;
            }
        }

        /// <summary>
        /// 清除角色的所有權限
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="clearedBy">清除者用戶 ID</param>
        /// <returns>操作結果</returns>
        public async Task<PermissionResult> ClearRolePermissionsAsync(string roleId, string clearedBy)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentException("角色 ID 不能為空", nameof(roleId));

            if (string.IsNullOrEmpty(clearedBy))
                throw new ArgumentException("清除者不能為空", nameof(clearedBy));

            try
            {
                var role = await _context.ApplicationRoles.FirstOrDefaultAsync(r => r.Id == roleId);
                if (role == null)
                    return new PermissionResult { Success = false, Message = "找不到指定的角色" };

                // 檢查是否為系統管理員角色
                if (role.Name == "Administrator")
                    return new PermissionResult { Success = false, Message = "無法清除系統管理員角色的權限" };

                var permissions = await GetRolePermissionsAsync(roleId);
                var clearedCount = permissions.Count;

                // 標記權限為停用而不是刪除
                foreach (var permission in permissions)
                {
                    permission.IsActive = false;
                    permission.ModifiedBy = clearedBy;
                    permission.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                await _auditService.LogAsync("CLEAR_PERMISSIONS", "PermissionManagement", clearedBy,
                    $"清除權限: 角色 {role.Name} | 清除數量: {clearedCount}", roleId, "SUCCESS");

                return new PermissionResult 
                { 
                    Success = true, 
                    Message = $"已清除角色 '{role.Name}' 的 {clearedCount} 個權限" 
                };
            }
            catch (Exception ex)
            {
                await _auditService.LogAsync("CLEAR_PERMISSIONS", "PermissionManagement", clearedBy,
                    $"清除權限異常: roleId {roleId} - {ex.Message}", roleId, "ERROR");
                throw;
            }
        }

        #endregion

        #region 權限查詢和驗證

        /// <summary>
        /// 檢查用戶是否有指定模組的權限
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="moduleName">模組名稱</param>
        /// <param name="operation">操作類型 (Read, Create, Update, Delete)</param>
        /// <returns>是否有權限</returns>
        public async Task<bool> CheckUserPermissionAsync(string userId, string moduleName, string operation)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(moduleName) || string.IsNullOrEmpty(operation))
                return false;

            try
            {
                // 取得用戶的所有角色
                var userRoles = await _roleService.GetUserRolesAsync(userId);
                if (!userRoles.Any())
                    return false;

                // 檢查任一角色是否有該權限
                foreach (var role in userRoles)
                {
                    var permission = await GetRoleModulePermissionAsync(role.Id, moduleName);
                    if (permission != null && permission.IsActive && permission.HasPermission(operation))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _auditService.LogError("CheckUserPermission", "PermissionManagement", ex, 
                    $"userId: {userId}, moduleName: {moduleName}, operation: {operation}");
                return false; // 發生錯誤時拒絕存取
            }
        }

        /// <summary>
        /// 取得用戶的有效權限清單
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <returns>權限清單</returns>
        public async Task<List<UserEffectivePermission>> GetUserEffectivePermissionsAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentException("用戶 ID 不能為空", nameof(userId));

            try
            {
                var effectivePermissions = new List<UserEffectivePermission>();

                // 取得用戶的所有角色
                var userRoles = await _roleService.GetUserRolesAsync(userId);
                if (!userRoles.Any())
                    return effectivePermissions;

                // 取得所有啟用的模組
                var modules = await _context.SystemModules.Where(m => m.IsActive).ToListAsync();

                foreach (var module in modules)
                {
                    var modulePermissions = new UserEffectivePermission
                    {
                        ModuleName = module.ModuleName,
                        DisplayName = module.DisplayName,
                        Category = module.Category,
                        CanRead = false,
                        CanCreate = false,
                        CanUpdate = false,
                        CanDelete = false,
                        GrantedByRoles = new List<string>()
                    };

                    // 檢查所有角色對此模組的權限
                    foreach (var role in userRoles)
                    {
                        var rolePermission = await GetRoleModulePermissionAsync(role.Id, module.ModuleName);
                        if (rolePermission != null && rolePermission.IsActive)
                        {
                            if (rolePermission.CanRead) 
                            {
                                modulePermissions.CanRead = true;
                                modulePermissions.GrantedByRoles.Add(role.Name);
                            }
                            if (rolePermission.CanCreate) 
                            {
                                modulePermissions.CanCreate = true;
                                if (!modulePermissions.GrantedByRoles.Contains(role.Name))
                                    modulePermissions.GrantedByRoles.Add(role.Name);
                            }
                            if (rolePermission.CanUpdate) 
                            {
                                modulePermissions.CanUpdate = true;
                                if (!modulePermissions.GrantedByRoles.Contains(role.Name))
                                    modulePermissions.GrantedByRoles.Add(role.Name);
                            }
                            if (rolePermission.CanDelete) 
                            {
                                modulePermissions.CanDelete = true;
                                if (!modulePermissions.GrantedByRoles.Contains(role.Name))
                                    modulePermissions.GrantedByRoles.Add(role.Name);
                            }
                        }
                    }

                    // 只加入有權限的模組
                    if (modulePermissions.CanRead || modulePermissions.CanCreate || 
                        modulePermissions.CanUpdate || modulePermissions.CanDelete)
                    {
                        effectivePermissions.Add(modulePermissions);
                    }
                }

                return effectivePermissions.OrderBy(p => p.Category).ThenBy(p => p.DisplayName).ToList();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetUserEffectivePermissions", "PermissionManagement", ex, 
                    $"userId: {userId}");
                throw;
            }
        }

        /// <summary>
        /// 取得權限統計資訊
        /// </summary>
        /// <returns>權限統計</returns>
        public async Task<PermissionStatistics> GetPermissionStatisticsAsync()
        {
            try
            {
                var statistics = new PermissionStatistics
                {
                    TotalPermissions = await _context.RolePermissions.CountAsync(),
                    ActivePermissions = await _context.RolePermissions.CountAsync(rp => rp.IsActive),
                    InactivePermissions = await _context.RolePermissions.CountAsync(rp => !rp.IsActive),
                    TotalModules = await _context.SystemModules.CountAsync(m => m.IsActive),
                    ConfiguredModules = await _context.RolePermissions
                        .Where(rp => rp.IsActive)
                        .Select(rp => rp.ModuleName)
                        .Distinct()
                        .CountAsync()
                };

                // 按分類統計模組權限配置
                statistics.ModulesByCategory = await _context.SystemModules
                    .Where(m => m.IsActive)
                    .GroupBy(m => m.Category)
                    .Select(g => new ModuleCategoryStatistics
                    {
                        Category = g.Key,
                        ModuleCount = g.Count(),
                        ConfiguredCount = _context.RolePermissions
                            .Where(rp => rp.IsActive && g.Any(m => m.ModuleName == rp.ModuleName))
                            .Select(rp => rp.ModuleName)
                            .Distinct()
                            .Count()
                    })
                    .ToListAsync();

                // 按角色統計權限
                statistics.PermissionsByRole = await _context.ApplicationRoles
                    .Where(r => r.IsActive)
                    .Select(r => new RolePermissionStatistics
                    {
                        RoleId = r.Id,
                        RoleName = r.Name,
                        PermissionCount = _context.RolePermissions.Count(rp => rp.RoleId == r.Id && rp.IsActive),
                        ReadPermissions = _context.RolePermissions.Count(rp => rp.RoleId == r.Id && rp.IsActive && rp.CanRead),
                        CreatePermissions = _context.RolePermissions.Count(rp => rp.RoleId == r.Id && rp.IsActive && rp.CanCreate),
                        UpdatePermissions = _context.RolePermissions.Count(rp => rp.RoleId == r.Id && rp.IsActive && rp.CanUpdate),
                        DeletePermissions = _context.RolePermissions.Count(rp => rp.RoleId == r.Id && rp.IsActive && rp.CanDelete)
                    })
                    .ToListAsync();

                return statistics;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetPermissionStatistics", "PermissionManagement", ex);
                throw;
            }
        }

        #endregion

        #region IDisposable

        private bool _disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                _auditService?.Dispose();
                _roleService?.Dispose();
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }

    #region 輔助類別

    /// <summary>
    /// 權限操作結果
    /// </summary>
    public class PermissionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// 模組權限設定
    /// </summary>
    public class ModulePermissionSetting
    {
        public string ModuleName { get; set; }
        public bool CanRead { get; set; }
        public bool CanCreate { get; set; }
        public bool CanUpdate { get; set; }
        public bool CanDelete { get; set; }
    }

    /// <summary>
    /// 權限矩陣
    /// </summary>
    public class PermissionMatrix
    {
        public List<ApplicationRole> Roles { get; set; } = new List<ApplicationRole>();
        public List<SystemModule> Modules { get; set; } = new List<SystemModule>();
        public List<RolePermission> Permissions { get; set; } = new List<RolePermission>();
        public Dictionary<string, RolePermission> PermissionIndex { get; set; } = new Dictionary<string, RolePermission>();

        /// <summary>
        /// 快速查找特定角色和模組的權限
        /// </summary>
        /// <param name="roleId">角色 ID</param>
        /// <param name="moduleName">模組名稱</param>
        /// <returns>權限物件，如果不存在則返回 null</returns>
        public RolePermission GetPermission(string roleId, string moduleName)
        {
            var key = $"{roleId}_{moduleName}";
            return PermissionIndex.ContainsKey(key) ? PermissionIndex[key] : null;
        }
    }

    /// <summary>
    /// 用戶有效權限
    /// </summary>
    public class UserEffectivePermission
    {
        public string ModuleName { get; set; }
        public string DisplayName { get; set; }
        public string Category { get; set; }
        public bool CanRead { get; set; }
        public bool CanCreate { get; set; }
        public bool CanUpdate { get; set; }
        public bool CanDelete { get; set; }
        public List<string> GrantedByRoles { get; set; } = new List<string>();
    }

    /// <summary>
    /// 權限統計資訊
    /// </summary>
    public class PermissionStatistics
    {
        public int TotalPermissions { get; set; }
        public int ActivePermissions { get; set; }
        public int InactivePermissions { get; set; }
        public int TotalModules { get; set; }
        public int ConfiguredModules { get; set; }
        public List<ModuleCategoryStatistics> ModulesByCategory { get; set; } = new List<ModuleCategoryStatistics>();
        public List<RolePermissionStatistics> PermissionsByRole { get; set; } = new List<RolePermissionStatistics>();
    }

    /// <summary>
    /// 模組分類統計
    /// </summary>
    public class ModuleCategoryStatistics
    {
        public string Category { get; set; }
        public int ModuleCount { get; set; }
        public int ConfiguredCount { get; set; }
    }

    /// <summary>
    /// 角色權限統計
    /// </summary>
    public class RolePermissionStatistics
    {
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public int PermissionCount { get; set; }
        public int ReadPermissions { get; set; }
        public int CreatePermissions { get; set; }
        public int UpdatePermissions { get; set; }
        public int DeletePermissions { get; set; }
    }

    #endregion
}