using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 膳食服務 - 處理膳食訂購、管理與統計功能
    /// </summary>
    public class MealService : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditService _auditService;

        public MealService()
        {
            _context = new ApplicationDbContext();
            _auditService = new AuditService();
        }

        public MealService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _auditService = new AuditService();
        }

        #region 會員搜尋

        /// <summary>
        /// 搜尋會員（依會員號或姓名）
        /// </summary>
        /// <param name="keyword">搜尋關鍵字</param>
        /// <returns>會員列表</returns>
        public async Task<List<Member>> SearchMembersAsync(string keyword)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(keyword))
                    return new List<Member>();

                keyword = keyword.Trim().ToLower();

                return await _context.Set<Member>()
                    .Where(m => m.IsActive &&
                               (m.MemberNumber.ToLower().Contains(keyword) ||
                                m.FullName.ToLower().Contains(keyword)))
                    .OrderBy(m => m.MemberNumber)
                    .Take(50)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("SearchMembers", "MealService", ex, $"keyword: {keyword}");
                throw;
            }
        }

        /// <summary>
        /// 根據會員號取得會員資料
        /// </summary>
        /// <param name="memberNumber">會員號</param>
        /// <returns>會員資料</returns>
        public async Task<Member> GetMemberByNumberAsync(string memberNumber)
        {
            try
            {
                return await _context.Set<Member>()
                    .FirstOrDefaultAsync(m => m.MemberNumber == memberNumber && m.IsActive);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMemberByNumber", "MealService", ex, $"memberNumber: {memberNumber}");
                throw;
            }
        }

        #endregion

        #region 膳食項目管理

        /// <summary>
        /// 取得所有可用的膳食項目
        /// </summary>
        /// <param name="category">餐點類別篩選</param>
        /// <returns>膳食項目列表</returns>
        public async Task<List<MealItem>> GetAvailableMealItemsAsync(string category = null)
        {
            try
            {
                var query = _context.Set<MealItem>()
                    .Where(mi => mi.IsActive && mi.IsAvailable);

                if (!string.IsNullOrEmpty(category))
                {
                    query = query.Where(mi => mi.Category == category);
                }

                return await query
                    .OrderBy(mi => mi.Category)
                    .ThenBy(mi => mi.SortOrder)
                    .ThenBy(mi => mi.ItemName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetAvailableMealItems", "MealService", ex, $"category: {category}");
                throw;
            }
        }

        /// <summary>
        /// 根據 ID 取得膳食項目
        /// </summary>
        /// <param name="mealItemId">膳食項目 ID</param>
        /// <returns>膳食項目</returns>
        public async Task<MealItem> GetMealItemByIdAsync(int mealItemId)
        {
            try
            {
                return await _context.Set<MealItem>()
                    .Include(mi => mi.MealDailyStocks)
                    .FirstOrDefaultAsync(mi => mi.Id == mealItemId);
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetMealItemById", "MealService", ex, $"mealItemId: {mealItemId}");
                throw;
            }
        }

        #endregion

        #region 膳食訂購

        /// <summary>
        /// 建立單筆膳食訂單
        /// </summary>
        /// <param name="memberId">會員 ID</param>
        /// <param name="mealItemId">餐點 ID</param>
        /// <param name="mealDate">用餐日期</param>
        /// <param name="quantity">數量</param>
        /// <param name="orderedBy">訂購者</param>
        /// <returns>操作結果</returns>
        public async Task<MealOperationResult> CreateMealOrderAsync(int memberId, int mealItemId, 
            DateTime mealDate, int quantity, string orderedBy)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    // 取得會員和餐點資料
                    var member = await _context.Set<Member>().FindAsync(memberId);
                    var mealItem = await GetMealItemByIdAsync(mealItemId);

                    if (member == null)
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = $"找不到 ID 為 {memberId} 的會員"
                        };
                    }

                    if (mealItem == null)
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = $"找不到 ID 為 {mealItemId} 的餐點"
                        };
                    }

                    // 檢查用餐日期
                    if (mealDate < DateTime.Today)
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = "用餐日期不能早於今天"
                        };
                    }

                    // 檢查庫存
                    if (!await CheckAndReserveStockAsync(mealItemId, mealDate, quantity, orderedBy))
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = $"餐點 {mealItem.ItemName} 在 {mealDate:yyyy-MM-dd} 庫存不足"
                        };
                    }

                    // 生成訂單號碼
                    var orderNumber = await GenerateOrderNumberAsync();

                    // 建立訂單
                    var order = new MealOrder
                    {
                        OrderNumber = orderNumber,
                        MemberId = memberId,
                        MealItemId = mealItemId,
                        MealDate = mealDate,
                        Quantity = quantity,
                        UnitPrice = mealItem.Price,
                        PaymentStatus = MealConstants.PaymentStatus.Pending,
                        OrderStatus = MealConstants.OrderStatus.Ordered,
                        OrderedBy = orderedBy,
                        OrderDate = DateTime.Now
                    };

                    order.CalculateTotalAmount();

                    // 驗證訂單資料
                    var validationErrors = order.Validate();
                    if (validationErrors.Any())
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = "訂單資料驗證失敗",
                            Errors = validationErrors
                        };
                    }

                    _context.Set<MealOrder>().Add(order);
                    await _context.SaveChangesAsync();

                    transaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("MEAL_ORDER_CREATE", "MealOrdering", orderedBy,
                        $"建立膳食訂單：{orderNumber} - {member.FullName} - {mealItem.ItemName} ({mealDate:yyyy-MM-dd})",
                        order.Id.ToString(), "SUCCESS");

                    return new MealOperationResult
                    {
                        Success = true,
                        Message = $"膳食訂單建立成功：{orderNumber}",
                        OrderId = order.Id,
                        Data = order
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();

                    await _auditService.LogAsync("MEAL_ORDER_CREATE", "MealOrdering", orderedBy,
                        $"建立膳食訂單失敗：{ex.Message}", null, "ERROR");

                    _auditService.LogError("CreateMealOrder", "MealService", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 批量建立多日膳食訂單
        /// </summary>
        /// <param name="memberId">會員 ID</param>
        /// <param name="mealItemId">餐點 ID</param>
        /// <param name="mealDates">用餐日期列表</param>
        /// <param name="quantity">每日數量</param>
        /// <param name="orderedBy">訂購者</param>
        /// <returns>操作結果</returns>
        public async Task<MealOperationResult> CreateBulkMealOrdersAsync(int memberId, int mealItemId,
            List<DateTime> mealDates, int quantity, string orderedBy)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var successOrders = new List<int>();
                    var failedDates = new List<string>();

                    foreach (var mealDate in mealDates)
                    {
                        var result = await CreateMealOrderAsync(memberId, mealItemId, mealDate, quantity, orderedBy);
                        
                        if (result.Success)
                        {
                            successOrders.Add(result.OrderId.Value);
                        }
                        else
                        {
                            failedDates.Add($"{mealDate:yyyy-MM-dd}: {result.Message}");
                        }
                    }

                    if (successOrders.Any())
                    {
                        await _context.SaveChangesAsync();
                        transaction.Commit();

                        var message = $"成功建立 {successOrders.Count} 筆訂單";
                        if (failedDates.Any())
                        {
                            message += $"，{failedDates.Count} 筆失敗";
                        }

                        // 記錄審計日誌
                        await _auditService.LogAsync("MEAL_ORDER_BULK_CREATE", "MealOrdering", orderedBy,
                            $"批量建立膳食訂單：成功 {successOrders.Count} 筆，失敗 {failedDates.Count} 筆",
                            string.Join(",", successOrders), "SUCCESS");

                        return new MealOperationResult
                        {
                            Success = true,
                            Message = message,
                            OrderIds = successOrders,
                            Errors = failedDates
                        };
                    }
                    else
                    {
                        transaction.Rollback();
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = "所有訂單建立失敗",
                            Errors = failedDates
                        };
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();

                    await _auditService.LogAsync("MEAL_ORDER_BULK_CREATE", "MealOrdering", orderedBy,
                        $"批量建立膳食訂單失敗：{ex.Message}", null, "ERROR");

                    _auditService.LogError("CreateBulkMealOrders", "MealService", ex);
                    throw;
                }
            }
        }

        #endregion

        #region 訂單管理

        /// <summary>
        /// 搜尋膳食訂單
        /// </summary>
        /// <param name="criteria">搜尋條件</param>
        /// <returns>搜尋結果</returns>
        public async Task<(List<MealOrder> Orders, int TotalCount)> SearchMealOrdersAsync(
            MealOrderSearchCriteria criteria)
        {
            try
            {
                var query = _context.Set<MealOrder>()
                    .Include(mo => mo.Member)
                    .Include(mo => mo.MealItem)
                    .AsQueryable();

                // 關鍵字搜尋
                if (!string.IsNullOrEmpty(criteria.Keyword))
                {
                    var keyword = criteria.Keyword.Trim().ToLower();
                    query = query.Where(mo =>
                        mo.OrderNumber.ToLower().Contains(keyword) ||
                        mo.Member.FullName.ToLower().Contains(keyword) ||
                        mo.Member.MemberNumber.ToLower().Contains(keyword) ||
                        mo.MealItem.ItemName.ToLower().Contains(keyword)
                    );
                }

                // 會員篩選
                if (criteria.MemberId.HasValue)
                {
                    query = query.Where(mo => mo.MemberId == criteria.MemberId.Value);
                }

                if (!string.IsNullOrEmpty(criteria.MemberNumber))
                {
                    query = query.Where(mo => mo.Member.MemberNumber == criteria.MemberNumber);
                }

                // 狀態篩選
                if (!string.IsNullOrEmpty(criteria.OrderStatus))
                {
                    query = query.Where(mo => mo.OrderStatus == criteria.OrderStatus);
                }

                if (!string.IsNullOrEmpty(criteria.PaymentStatus))
                {
                    query = query.Where(mo => mo.PaymentStatus == criteria.PaymentStatus);
                }

                // 餐點類別篩選
                if (!string.IsNullOrEmpty(criteria.Category))
                {
                    query = query.Where(mo => mo.MealItem.Category == criteria.Category);
                }

                // 訂單日期篩選
                if (criteria.OrderDateFrom.HasValue)
                {
                    query = query.Where(mo => mo.OrderDate >= criteria.OrderDateFrom.Value);
                }

                if (criteria.OrderDateTo.HasValue)
                {
                    query = query.Where(mo => mo.OrderDate <= criteria.OrderDateTo.Value);
                }

                // 用餐日期篩選
                if (criteria.MealDateFrom.HasValue)
                {
                    query = query.Where(mo => mo.MealDate >= criteria.MealDateFrom.Value);
                }

                if (criteria.MealDateTo.HasValue)
                {
                    query = query.Where(mo => mo.MealDate <= criteria.MealDateTo.Value);
                }

                // 總數
                var totalCount = await query.CountAsync();

                // 排序
                switch (criteria.SortField?.ToLower())
                {
                    case "ordernumber":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ?
                            query.OrderByDescending(mo => mo.OrderNumber) :
                            query.OrderBy(mo => mo.OrderNumber);
                        break;
                    case "membername":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ?
                            query.OrderByDescending(mo => mo.Member.FullName) :
                            query.OrderBy(mo => mo.Member.FullName);
                        break;
                    case "mealdate":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ?
                            query.OrderByDescending(mo => mo.MealDate) :
                            query.OrderBy(mo => mo.MealDate);
                        break;
                    case "orderstatus":
                        query = criteria.SortDirection?.ToUpper() == "DESC" ?
                            query.OrderByDescending(mo => mo.OrderStatus) :
                            query.OrderBy(mo => mo.OrderStatus);
                        break;
                    default:
                        query = criteria.SortDirection?.ToUpper() == "DESC" ?
                            query.OrderByDescending(mo => mo.OrderDate) :
                            query.OrderBy(mo => mo.OrderDate);
                        break;
                }

                // 分頁
                var orders = await query
                    .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                    .Take(criteria.PageSize)
                    .ToListAsync();

                return (orders, totalCount);
            }
            catch (Exception ex)
            {
                _auditService.LogError("SearchMealOrders", "MealService", ex);
                throw;
            }
        }

        /// <summary>
        /// 取消膳食訂單
        /// </summary>
        /// <param name="orderId">訂單 ID</param>
        /// <param name="reason">取消原因</param>
        /// <param name="cancelledBy">取消者</param>
        /// <returns>操作結果</returns>
        public async Task<MealOperationResult> CancelMealOrderAsync(int orderId, string reason, string cancelledBy)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var order = await _context.Set<MealOrder>()
                        .Include(mo => mo.Member)
                        .Include(mo => mo.MealItem)
                        .FirstOrDefaultAsync(mo => mo.Id == orderId);

                    if (order == null)
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = $"找不到 ID 為 {orderId} 的訂單"
                        };
                    }

                    if (!order.CanCancel)
                    {
                        return new MealOperationResult
                        {
                            Success = false,
                            Message = "此訂單無法取消"
                        };
                    }

                    // 釋放庫存
                    await ReleaseStockAsync(order.MealItemId, order.MealDate, order.Quantity, cancelledBy);

                    // 更新訂單狀態
                    order.CancelOrder(cancelledBy, reason);

                    await _context.SaveChangesAsync();
                    transaction.Commit();

                    // 記錄審計日誌
                    await _auditService.LogAsync("MEAL_ORDER_CANCEL", "MealManagement", cancelledBy,
                        $"取消膳食訂單：{order.OrderNumber} - {order.Member.FullName} - {order.MealItem.ItemName}，原因：{reason}",
                        orderId.ToString(), "SUCCESS");

                    return new MealOperationResult
                    {
                        Success = true,
                        Message = $"訂單 {order.OrderNumber} 取消成功",
                        OrderId = orderId
                    };
                }
                catch (Exception ex)
                {
                    transaction.Rollback();

                    await _auditService.LogAsync("MEAL_ORDER_CANCEL", "MealManagement", cancelledBy,
                        $"取消膳食訂單失敗：{ex.Message}", orderId.ToString(), "ERROR");

                    _auditService.LogError("CancelMealOrder", "MealService", ex);
                    throw;
                }
            }
        }

        #endregion

        #region 每日統計

        /// <summary>
        /// 取得每日膳食統計
        /// </summary>
        /// <param name="date">統計日期</param>
        /// <returns>每日統計資料</returns>
        public async Task<DailyMealStatistics> GetDailyMealStatisticsAsync(DateTime date)
        {
            try
            {
                var orders = await _context.Set<MealOrder>()
                    .Include(mo => mo.Member)
                    .Include(mo => mo.MealItem)
                    .Where(mo => mo.MealDate.Date == date.Date &&
                                mo.OrderStatus != MealConstants.OrderStatus.Cancelled)
                    .ToListAsync();

                var statistics = new DailyMealStatistics
                {
                    Date = date.Date,
                    TotalOrders = orders.Count,
                    MainMealOrders = orders.Count(o => o.MealItem.Category == MealConstants.Category.MainMeal),
                    SideMealOrders = orders.Count(o => o.MealItem.Category == MealConstants.Category.SideMeal),
                    SpecialMealOrders = orders.Count(o => o.MealItem.Category == MealConstants.Category.SpecialMeal),
                    TotalRevenue = orders.Sum(o => o.TotalAmount)
                };

                // 統計各餐點資料
                var itemGroups = orders.GroupBy(o => new { o.MealItemId, o.MealItem.ItemName, o.MealItem.Category });
                
                foreach (var group in itemGroups)
                {
                    var itemStat = new MealItemStatistic
                    {
                        MealItemId = group.Key.MealItemId,
                        ItemName = group.Key.ItemName,
                        Category = group.Key.Category,
                        OrderedQuantity = group.Sum(o => o.Quantity),
                        Revenue = group.Sum(o => o.TotalAmount)
                    };

                    // 取得可用庫存
                    var stock = await _context.Set<MealDailyStock>()
                        .FirstOrDefaultAsync(s => s.MealItemId == group.Key.MealItemId && s.StockDate.Date == date.Date);
                    
                    itemStat.AvailableQuantity = stock?.AvailableQuantity ?? 0;

                    statistics.ItemStatistics.Add(itemStat);
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetDailyMealStatistics", "MealService", ex, $"date: {date:yyyy-MM-dd}");
                throw;
            }
        }

        /// <summary>
        /// 取得指定日期的所有訂單（匯出用）
        /// </summary>
        /// <param name="date">查詢日期</param>
        /// <returns>訂單列表</returns>
        public async Task<List<MealOrder>> GetDailyOrdersForExportAsync(DateTime date)
        {
            try
            {
                return await _context.Set<MealOrder>()
                    .Include(mo => mo.Member)
                    .Include(mo => mo.MealItem)
                    .Where(mo => mo.MealDate.Date == date.Date &&
                                mo.OrderStatus != MealConstants.OrderStatus.Cancelled)
                    .OrderBy(mo => mo.MealItem.Category)
                    .ThenBy(mo => mo.MealItem.ItemName)
                    .ThenBy(mo => mo.Member.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _auditService.LogError("GetDailyOrdersForExport", "MealService", ex, $"date: {date:yyyy-MM-dd}");
                throw;
            }
        }

        #endregion

        #region 庫存管理

        /// <summary>
        /// 檢查並預訂庫存
        /// </summary>
        /// <param name="mealItemId">餐點 ID</param>
        /// <param name="date">日期</param>
        /// <param name="quantity">數量</param>
        /// <param name="updatedBy">更新者</param>
        /// <returns>是否成功</returns>
        private async Task<bool> CheckAndReserveStockAsync(int mealItemId, DateTime date, int quantity, string updatedBy)
        {
            try
            {
                var mealItem = await GetMealItemByIdAsync(mealItemId);
                if (mealItem?.DailyLimit == null) return true; // 無限制

                // 取得或建立每日庫存記錄
                var stock = await _context.Set<MealDailyStock>()
                    .FirstOrDefaultAsync(s => s.MealItemId == mealItemId && s.StockDate.Date == date.Date);

                if (stock == null)
                {
                    // 建立新的庫存記錄
                    stock = new MealDailyStock
                    {
                        MealItemId = mealItemId,
                        StockDate = date.Date,
                        OriginalQuantity = mealItem.DailyLimit.Value,
                        AvailableQuantity = mealItem.DailyLimit.Value,
                        UpdatedBy = updatedBy
                    };
                    _context.Set<MealDailyStock>().Add(stock);
                }

                return stock.ReserveStock(quantity, updatedBy);
            }
            catch (Exception ex)
            {
                _auditService.LogError("CheckAndReserveStock", "MealService", ex);
                return false;
            }
        }

        /// <summary>
        /// 釋放庫存
        /// </summary>
        /// <param name="mealItemId">餐點 ID</param>
        /// <param name="date">日期</param>
        /// <param name="quantity">數量</param>
        /// <param name="updatedBy">更新者</param>
        private async Task ReleaseStockAsync(int mealItemId, DateTime date, int quantity, string updatedBy)
        {
            try
            {
                var stock = await _context.Set<MealDailyStock>()
                    .FirstOrDefaultAsync(s => s.MealItemId == mealItemId && s.StockDate.Date == date.Date);

                stock?.ReleaseStock(quantity, updatedBy);
            }
            catch (Exception ex)
            {
                _auditService.LogError("ReleaseStock", "MealService", ex);
            }
        }

        /// <summary>
        /// 生成訂單號碼
        /// </summary>
        /// <returns>訂單號碼</returns>
        private async Task<string> GenerateOrderNumberAsync()
        {
            try
            {
                var today = DateTime.Today;
                var prefix = $"MO{today:yyyyMMdd}";

                var maxOrderNumber = await _context.Set<MealOrder>()
                    .Where(mo => mo.OrderNumber.StartsWith(prefix))
                    .Select(mo => mo.OrderNumber)
                    .OrderByDescending(on => on)
                    .FirstOrDefaultAsync();

                if (maxOrderNumber != null && maxOrderNumber.Length >= 14)
                {
                    var sequenceStr = maxOrderNumber.Substring(10);
                    if (int.TryParse(sequenceStr, out int sequence))
                    {
                        return $"{prefix}{(sequence + 1):D4}";
                    }
                }

                return $"{prefix}0001";
            }
            catch (Exception ex)
            {
                _auditService.LogError("GenerateOrderNumber", "MealService", ex);
                return $"MO{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        #endregion

        #region IDisposable

        private bool _disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                _auditService?.Dispose();
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}