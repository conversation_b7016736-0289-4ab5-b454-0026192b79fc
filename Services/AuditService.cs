using System;
using System.Threading.Tasks;
using System.Web;
using System.Data.Entity;
using CWDECC_3S.Models;
using CWDECC_3S.Data;

namespace CWDECC_3S.Services
{
    /// <summary>
    /// 審計日誌服務 - 記錄所有重要操作
    /// </summary>
    public class AuditService : IDisposable
    {
        private readonly ApplicationDbContext _context;

        public AuditService()
        {
            _context = new ApplicationDbContext();
        }

        #region 審計日誌記錄

        /// <summary>
        /// 記錄審計日誌（非同步）
        /// </summary>
        /// <param name="action">操作類型</param>
        /// <param name="userId">用戶 ID</param>
        /// <param name="userName">用戶名</param>
        /// <param name="ipAddress">IP 地址</param>
        /// <param name="details">詳細信息</param>
        /// <param name="userAgent">用戶代理</param>
        /// <param name="entity">實體類型</param>
        /// <param name="entityId">實體 ID</param>
        /// <param name="result">操作結果</param>
        public async Task LogAsync(string action, string userId = null, string userName = null, 
            string ipAddress = null, string details = null, string userAgent = null, 
            string entity = null, string entityId = null, string result = "SUCCESS")
        {
            try
            {
                var context = HttpContext.Current;
                
                var auditLog = new AuditLog
                {
                    Action = action,
                    UserId = userId,
                    UserName = userName ?? GetCurrentUserName(),
                    IPAddress = ipAddress ?? GetClientIPAddress(),
                    UserAgent = userAgent ?? context?.Request?.UserAgent,
                    Details = details,
                    Entity = entity,
                    EntityId = entityId,
                    Result = result,
                    Timestamp = DateTime.UtcNow,
                    SessionId = context?.Session?.SessionID
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();

                // 同時寫入系統日誌檔案
                await WriteToLogFileAsync(auditLog);
            }
            catch (Exception ex)
            {
                // 審計失敗不應影響主要功能，但需要記錄錯誤
                await WriteErrorLogAsync($"審計日誌記錄失敗: {ex.Message}", action, userId, userName);
            }
        }

        /// <summary>
        /// 記錄審計日誌（同步）
        /// </summary>
        public void Log(string action, string userId = null, string userName = null, 
            string ipAddress = null, string details = null, string userAgent = null, 
            string entity = null, string entityId = null, string result = "SUCCESS")
        {
            Task.Run(async () => await LogAsync(action, userId, userName, ipAddress, details, userAgent, entity, entityId, result));
        }

        #endregion

        #region 特定操作日誌

        /// <summary>
        /// 記錄登入成功
        /// </summary>
        public async Task LogLoginSuccessAsync(string userId, string userName, string ipAddress, string userAgent, string details = null)
        {
            await LogAsync("LOGIN_SUCCESS", userId, userName, ipAddress, details ?? "用戶成功登入", userAgent);
        }

        /// <summary>
        /// 記錄登入失敗
        /// </summary>
        public async Task LogLoginFailureAsync(string userId, string userName, string ipAddress, string userAgent, string reason)
        {
            await LogAsync("LOGIN_FAILED", userId, userName, ipAddress, reason, userAgent, result: "FAILED");
        }

        /// <summary>
        /// 記錄登出
        /// </summary>
        public async Task LogLogoutAsync(string userId, string userName, string ipAddress, string userAgent, string reason = "用戶主動登出")
        {
            await LogAsync("LOGOUT", userId, userName, ipAddress, reason, userAgent);
        }

        /// <summary>
        /// 記錄密碼修改
        /// </summary>
        public async Task LogPasswordChangeAsync(string userId, string userName, string ipAddress, string userAgent, bool success, string details = null)
        {
            var action = success ? "PASSWORD_CHANGED" : "PASSWORD_CHANGE_FAILED";
            var result = success ? "SUCCESS" : "FAILED";
            await LogAsync(action, userId, userName, ipAddress, details ?? (success ? "密碼修改成功" : "密碼修改失敗"), userAgent, result: result);
        }

        /// <summary>
        /// 記錄密碼重設
        /// </summary>
        public async Task LogPasswordResetAsync(string userId, string userName, string ipAddress, string userAgent, string adminId, bool success, string details = null)
        {
            var action = success ? "PASSWORD_RESET" : "PASSWORD_RESET_FAILED";
            var result = success ? "SUCCESS" : "FAILED";
            var message = details ?? (success ? $"管理員 {adminId} 重設密碼成功" : $"管理員 {adminId} 重設密碼失敗");
            await LogAsync(action, userId, userName, ipAddress, message, userAgent, result: result);
        }

        /// <summary>
        /// 記錄用戶鎖定
        /// </summary>
        public async Task LogUserLockoutAsync(string userId, string userName, string ipAddress, string userAgent, string reason)
        {
            await LogAsync("USER_LOCKOUT", userId, userName, ipAddress, reason, userAgent);
        }

        /// <summary>
        /// 記錄用戶解鎖
        /// </summary>
        public async Task LogUserUnlockAsync(string userId, string userName, string ipAddress, string userAgent, string adminId)
        {
            await LogAsync("USER_UNLOCK", userId, userName, ipAddress, $"管理員 {adminId} 解鎖用戶", userAgent);
        }

        /// <summary>
        /// 記錄權限變更
        /// </summary>
        public async Task LogPermissionChangeAsync(string userId, string userName, string ipAddress, string userAgent, string adminId, string oldRole, string newRole)
        {
            await LogAsync("PERMISSION_CHANGED", userId, userName, ipAddress, $"管理員 {adminId} 將用戶角色從 {oldRole} 變更為 {newRole}", userAgent);
        }

        /// <summary>
        /// 記錄資料存取
        /// </summary>
        public async Task LogDataAccessAsync(string userId, string userName, string ipAddress, string userAgent, string entity, string entityId, string operation)
        {
            await LogAsync($"DATA_{operation.ToUpper()}", userId, userName, ipAddress, $"{operation} {entity}", userAgent, entity, entityId);
        }

        /// <summary>
        /// 記錄系統操作
        /// </summary>
        public async Task LogSystemOperationAsync(string userId, string userName, string ipAddress, string userAgent, string operation, string details)
        {
            await LogAsync("SYSTEM_OPERATION", userId, userName, ipAddress, $"{operation}: {details}", userAgent);
        }

        /// <summary>
        /// 記錄安全事件
        /// </summary>
        public async Task LogSecurityEventAsync(string userId, string userName, string ipAddress, string userAgent, string eventType, string details)
        {
            await LogAsync($"SECURITY_{eventType.ToUpper()}", userId, userName, ipAddress, details, userAgent, result: "ALERT");
        }

        /// <summary>
        /// 記錄錯誤日誌
        /// </summary>
        public async Task LogErrorAsync(string action, string entity, Exception exception, string additionalDetails = null)
        {
            var details = $"Error: {exception?.Message}";
            if (!string.IsNullOrEmpty(additionalDetails))
                details += $" | Additional: {additionalDetails}";
            
            if (exception?.StackTrace != null)
                details += $" | StackTrace: {exception.StackTrace}";

            await LogAsync($"ERROR_{action.ToUpper()}", null, null, null, details, null, entity, null, "ERROR");
        }

        /// <summary>
        /// 記錄錯誤日誌（同步）
        /// </summary>
        public void LogError(string action, string entity, Exception exception, string additionalDetails = null)
        {
            Task.Run(async () => await LogErrorAsync(action, entity, exception, additionalDetails));
        }

        /// <summary>
        /// 記錄膳食操作日誌
        /// </summary>
        public async Task LogMealOperationAsync(string action, string userId, string userName, string details, string entity = "MealOrder", string entityId = null)
        {
            await LogAsync($"MEAL_{action.ToUpper()}", userId, userName, null, details, null, entity, entityId);
        }

        /// <summary>
        /// 記錄膳食操作日誌（同步）
        /// </summary>
        public void LogMealOperation(string action, string userId, string userName, string details, string entity = "MealOrder", string entityId = null)
        {
            Task.Run(async () => await LogMealOperationAsync(action, userId, userName, details, entity, entityId));
        }

        #endregion

        #region 日誌檔案寫入

        /// <summary>
        /// 寫入日誌檔案
        /// </summary>
        private async Task WriteToLogFileAsync(AuditLog auditLog)
        {
            try
            {
                var logDirectory = HttpContext.Current?.Server?.MapPath("~/App_Data/Logs/Audit/");
                if (string.IsNullOrEmpty(logDirectory))
                    return;

                if (!System.IO.Directory.Exists(logDirectory))
                {
                    System.IO.Directory.CreateDirectory(logDirectory);
                }

                var logFileName = $"audit_{DateTime.UtcNow:yyyyMMdd}.log";
                var logFilePath = System.IO.Path.Combine(logDirectory, logFileName);

                var logEntry = FormatLogEntry(auditLog);
                
                using (var writer = new System.IO.StreamWriter(logFilePath, true, System.Text.Encoding.UTF8))
                {
                    await writer.WriteLineAsync(logEntry);
                }
            }
            catch (Exception ex)
            {
                // 日誌檔案寫入失敗，記錄到事件日誌
                System.Diagnostics.EventLog.WriteEntry("CWDECC_3S", $"審計日誌檔案寫入失敗: {ex.Message}", System.Diagnostics.EventLogEntryType.Warning);
            }
        }

        /// <summary>
        /// 寫入錯誤日誌
        /// </summary>
        private async Task WriteErrorLogAsync(string errorMessage, string action, string userId, string userName)
        {
            try
            {
                var logDirectory = HttpContext.Current?.Server?.MapPath("~/App_Data/Logs/Errors/");
                if (string.IsNullOrEmpty(logDirectory))
                    return;

                if (!System.IO.Directory.Exists(logDirectory))
                {
                    System.IO.Directory.CreateDirectory(logDirectory);
                }

                var logFileName = $"audit_errors_{DateTime.UtcNow:yyyyMMdd}.log";
                var logFilePath = System.IO.Path.Combine(logDirectory, logFileName);

                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [ERROR] Action: {action}, User: {userName} ({userId}), Error: {errorMessage}";
                
                using (var writer = new System.IO.StreamWriter(logFilePath, true, System.Text.Encoding.UTF8))
                {
                    await writer.WriteLineAsync(logEntry);
                }
            }
            catch
            {
                // 忽略錯誤日誌寫入失敗
            }
        }

        /// <summary>
        /// 格式化日誌條目
        /// </summary>
        private string FormatLogEntry(AuditLog auditLog)
        {
            return $"{auditLog.Timestamp:yyyy-MM-dd HH:mm:ss} UTC | " +
                   $"{auditLog.Action} | " +
                   $"User: {auditLog.UserName} ({auditLog.UserId}) | " +
                   $"IP: {auditLog.IPAddress} | " +
                   $"Session: {auditLog.SessionId} | " +
                   $"Result: {auditLog.Result} | " +
                   $"Details: {auditLog.Details} | " +
                   $"Entity: {auditLog.Entity} ({auditLog.EntityId}) | " +
                   $"UserAgent: {auditLog.UserAgent}";
        }

        #endregion

        #region 日誌查詢

        /// <summary>
        /// 查詢用戶審計日誌
        /// </summary>
        public async Task<System.Collections.Generic.List<AuditLog>> GetUserAuditLogsAsync(string userId, DateTime? startDate = null, DateTime? endDate = null, int pageSize = 50, int pageNumber = 1)
        {
            var query = _context.AuditLogs.Where(a => a.UserId == userId);

            if (startDate.HasValue)
                query = query.Where(a => a.Timestamp >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.Timestamp <= endDate.Value);

            return await query
                .OrderByDescending(a => a.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        /// <summary>
        /// 查詢操作審計日誌
        /// </summary>
        public async Task<System.Collections.Generic.List<AuditLog>> GetActionAuditLogsAsync(string action, DateTime? startDate = null, DateTime? endDate = null, int pageSize = 50, int pageNumber = 1)
        {
            var query = _context.AuditLogs.Where(a => a.Action == action);

            if (startDate.HasValue)
                query = query.Where(a => a.Timestamp >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.Timestamp <= endDate.Value);

            return await query
                .OrderByDescending(a => a.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        /// <summary>
        /// 查詢安全事件日誌
        /// </summary>
        public async Task<System.Collections.Generic.List<AuditLog>> GetSecurityEventsAsync(DateTime? startDate = null, DateTime? endDate = null, int pageSize = 50, int pageNumber = 1)
        {
            var query = _context.AuditLogs.Where(a => a.Action.StartsWith("SECURITY_") || a.Action.Contains("FAILED") || a.Result == "ALERT");

            if (startDate.HasValue)
                query = query.Where(a => a.Timestamp >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.Timestamp <= endDate.Value);

            return await query
                .OrderByDescending(a => a.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        /// <summary>
        /// 統計登入失敗次數
        /// </summary>
        public async Task<int> GetFailedLoginCountAsync(string ipAddress, DateTime since)
        {
            return await _context.AuditLogs
                .Where(a => a.Action == "LOGIN_FAILED" && a.IPAddress == ipAddress && a.Timestamp >= since)
                .CountAsync();
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 取得當前用戶名
        /// </summary>
        private string GetCurrentUserName()
        {
            var context = HttpContext.Current;
            return context?.Session?["UserName"]?.ToString() ?? 
                   context?.User?.Identity?.Name ?? 
                   "anonymous";
        }

        /// <summary>
        /// 取得客戶端 IP 地址
        /// </summary>
        private string GetClientIPAddress()
        {
            return AuthenticationService.GetClientIPAddress();
        }

        /// <summary>
        /// 清理舊日誌（保留指定天數）
        /// </summary>
        public async Task CleanupOldLogsAsync(int retentionDays = 365)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
                var oldLogs = _context.AuditLogs.Where(a => a.Timestamp < cutoffDate);
                
                _context.AuditLogs.RemoveRange(oldLogs);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                await WriteErrorLogAsync($"清理舊日誌失敗: {ex.Message}", "CLEANUP_LOGS", null, "system");
            }
        }

        #endregion

        #region IDisposable 實作

        public void Dispose()
        {
            _context?.Dispose();
        }

        #endregion
    }
}