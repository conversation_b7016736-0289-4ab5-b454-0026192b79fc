/* ========================================
   CWDECC 3S 系統 - 主樣式表
   響應式設計與角色權限導航優化
   ======================================== */

/* CSS 變數定義 - 整合 HTTrack 企業藍色主題 */
:root {
    /* 主要色彩系統 - 基於 HTTrack 的企業藍色風格 */
    --primary-color: #0850B2;
    --primary-light: #1f5080;
    --primary-dark: #284E98;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* HTTrack 整合色彩 */
    --enterprise-blue: #0850B2;
    --enterprise-blue-light: #7f9db9;
    --enterprise-blue-dark: #1f5080;
    --table-light: #f4f8fb;
    --table-dark: #ecf2f9;
    --border-light: #dae6f3;
    --border-dark: #d0e0f0;
    --system-message-bg: #FFCCFF;
    --system-message-border: #FF0000;
    
    --navbar-height: 56px;
    --sidebar-width: 250px;
    --footer-height: 100px;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --transition: all 0.15s ease-in-out;
}

/* 基礎樣式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: "Microsoft JhengHei", "PingFang TC", "Apple LiGothic Medium", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f5f5;
    padding-top: var(--navbar-height);
    padding-bottom: 20px;
    min-height: 100vh;
}

/* ========================================
   導航欄樣式
   ======================================== */

.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: var(--navbar-height);
    box-shadow: var(--box-shadow-lg);
    transition: var(--transition);
    z-index: 1030;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
    transition: var(--transition);
}

.navbar-brand:hover {
    color: #e6f3ff !important;
    transform: scale(1.05);
}

.navbar-brand i {
    margin-right: 8px;
    font-size: 1.3rem;
}

/* 導航選單項目 */
.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin: 0 2px;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

.navbar-nav .nav-link i {
    margin-right: 6px;
    width: 16px;
    text-align: center;
}

/* 下拉選單樣式 */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 200px;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    color: var(--dark-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--light-color);
    color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    opacity: 0.7;
}

.dropdown-header {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 0.875rem;
    padding: 0.5rem 1.5rem;
    margin-bottom: 0.25rem;
}

/* 用戶資訊下拉選單 */
.dropdown-item-text {
    padding: 0.5rem 1.5rem;
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.dropdown-item.text-danger:hover {
    background-color: var(--danger-color);
    color: white;
}

/* 移動端導航按鈕 */
.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    transition: var(--transition);
}

.navbar-toggler:hover,
.navbar-toggler:focus {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* ========================================
   主要內容區域
   ======================================== */

.main-content {
    min-height: calc(100vh - var(--navbar-height) - var(--footer-height));
    background-color: white;
    margin-top: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.container-fluid {
    padding: 1.5rem;
}

/* 相容性支援 */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

@media screen and (min-width: 768px) {
    .body-content {
        padding: 0;
    }
}

/* 麵包屑導航 */
.breadcrumb {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
}

.breadcrumb-item {
    font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--secondary-color);
    font-weight: 700;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--info-color);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--secondary-color);
    font-weight: 600;
}

/* ========================================
   頁尾樣式
   ======================================== */

.footer {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    margin-top: auto;
    min-height: var(--footer-height);
}

.footer h6 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.footer .text-muted {
    font-size: 0.875rem;
    line-height: 1.4;
}

/* ========================================
   表單樣式
   ======================================== */

/* Set widths on the form inputs since otherwise they're 100% wide */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="select"] {
    max-width: 280px;
}

.form-group {
    margin-bottom: 15px;
}

.form-control {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* ========================================
   Alert 警告樣式
   ======================================== */

.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-dismissible .close {
    padding: 0.75rem 1rem;
    opacity: 0.7;
    transition: var(--transition);
}

.alert-dismissible .close:hover {
    opacity: 1;
}

/* ========================================
   按鈕樣式
   ======================================== */

.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn:active {
    transform: translateY(0);
}

/* ========================================
   表格樣式
   ======================================== */

.table {
    border-collapse: collapse;
}

.table th {
    background-color: var(--light-color);
    font-weight: bold;
    border-top: none;
}

/* ========================================
   卡片和區塊樣式
   ======================================== */

.system-status {
    background-color: var(--light-color);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
}

.test-section {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.card {
    transition: var(--transition);
    border: none;
    box-shadow: var(--box-shadow);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* ========================================
   標籤樣式 (Bootstrap 4 compatible)
   ======================================== */

.label,
.badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 3px;
    font-weight: 600;
}

.label-success,
.badge-success {
    background-color: var(--success-color);
    color: white;
}

.label-danger,
.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.label-info,
.badge-info {
    background-color: var(--info-color);
    color: white;
}

.label-warning,
.badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

/* ========================================
   響應式設計 - 移動端優化
   ======================================== */

/* 平板設備 (768px - 991px) */
@media (max-width: 991.98px) {
    .navbar-nav {
        padding: 1rem 0;
    }
    
    .navbar-nav .nav-link {
        margin: 2px 0;
        border-radius: var(--border-radius);
    }
    
    .dropdown-menu {
        position: static !important;
        transform: none !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background-color: rgba(255, 255, 255, 0.95);
        margin: 0.5rem 0;
        box-shadow: none;
    }
    
    .dropdown-item {
        color: var(--dark-color);
        padding: 0.5rem 1rem;
    }
    
    .container-fluid {
        padding: 1rem;
    }
}

/* 手機設備 (576px 以下) */
@media (max-width: 575.98px) {
    body {
        font-size: 13px;
    }
    
    .navbar-brand {
        font-size: 1.3rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }
    
    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .container-fluid {
        padding: 0.75rem;
    }
    
    .footer {
        padding: 1.5rem 0;
        text-align: center;
    }
    
    .footer .col-md-6 {
        margin-bottom: 1rem;
    }
    
    .footer .text-md-right {
        text-align: center !important;
    }
    
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="tel"],
    input[type="select"] {
        max-width: 100%;
    }
}

/* 極小設備 (480px 以下) */
@media (max-width: 480px) {
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .navbar-brand i {
        display: none;
    }
    
    .nav-link i {
        margin-right: 4px;
    }
    
    .dropdown-item i {
        margin-right: 6px;
    }
}

/* ========================================
   角色權限樣式
   ======================================== */

/* 角色徽章樣式 */
.role-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-administrator {
    background-color: var(--danger-color);
    color: white;
}

.role-staffmember {
    background-color: var(--primary-color);
    color: white;
}

.role-teacher {
    background-color: var(--success-color);
    color: white;
}

.role-volunteer {
    background-color: var(--info-color);
    color: white;
}

.role-member {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.role-guest {
    background-color: var(--secondary-color);
    color: white;
}

/* 權限提示樣式 */
.permission-required {
    opacity: 0.6;
    pointer-events: none;
}

.permission-denied {
    display: none;
}

/* ========================================
   工具類別
   ======================================== */

/* 間距調整 */
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }
.ms-1 { margin-left: 0.25rem !important; }
.ms-2 { margin-left: 0.5rem !important; }
.ms-3 { margin-left: 1rem !important; }

/* 載入動畫 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 淡入動畫 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ========================================
   無障礙支援
   ======================================== */

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* 焦點樣式 */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 跳過連結 */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--dark-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 100;
    border-radius: var(--border-radius);
}

.skip-link:focus {
    top: 6px;
}

/* 螢幕閱讀器專用 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ========================================
   HTTrack 整合樣式 - 企業級組件
   ======================================== */

/* 企業標題樣式 */
.enterprise-header {
    font-size: 16pt;
    font-weight: bold;
    color: var(--enterprise-blue);
    background-color: #FFFFFF;
    border-top: 7px solid var(--enterprise-blue);
    border-bottom: 3px solid #CCCCCC;
    height: auto;
    padding: 5px;
    margin-bottom: 15px;
}

/* 企業頁尾樣式 */
.enterprise-footer {
    padding: 5px;
    font-size: 12pt;
    font-weight: bold;
    color: #ffffff;
    background-color: var(--primary-dark);
    border-bottom: 3px solid var(--enterprise-blue);
    border-top: 7px solid var(--enterprise-blue);
    width: 100%;
    font-family: Verdana, Geneva, Tahoma, sans-serif;
}

/* 系統訊息樣式 */
.system-message {
    padding: 5px;
    background-color: var(--system-message-bg);
    border-bottom: dashed thin var(--system-message-border);
    border-left: thick solid var(--system-message-border);
    margin-bottom: 10px;
    border-radius: 4px;
}

.system-message.working-hour {
    font-family: Verdana, Geneva, Tahoma, sans-serif;
}

/* 重要提醒樣式 */
.important-note {
    padding: 5px;
    font-size: 13pt;
    font-weight: bold;
    color: #ffffff;
    background-color: #6599cc;
    border: 3px solid #ffffff;
    text-align: center;
    font-family: 新細明體;
    border-radius: 4px;
    margin-bottom: 15px;
}

/* 表單控件樣式 - HTTrack 風格 */
.form-input-enterprise {
    font-family: Verdana, Arial, Tahoma;
    font-size: 10pt;
    color: var(--primary-light);
    background-color: white;
    border: 1px solid var(--enterprise-blue-light);
    padding: 2px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.form-input-enterprise:focus {
    border-color: var(--enterprise-blue);
    box-shadow: 0 0 5px rgba(31, 80, 128, 0.3);
}

.form-dropdown-enterprise {
    font-family: Verdana, Arial, Tahoma;
    font-size: 10pt;
    color: var(--primary-light);
    background-color: white;
    border: 1px solid var(--enterprise-blue-light);
    margin-bottom: 0px;
    border-radius: 3px;
}

/* 按鈕樣式 - HTTrack 風格 */
.btn-enterprise {
    font-family: Verdana, Arial, Tahoma;
    font-size: 10pt;
    color: #2c4b7e;
    background-color: #ffffff;
    padding: 3px 8px;
    border: 1px solid var(--enterprise-blue-light);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-enterprise:hover {
    background-color: var(--table-light);
    border-color: var(--enterprise-blue);
    transform: translateY(-1px);
}

.btn-enterprise.important {
    font-family: 新細明體;
    color: #FF0066;
    border-color: #FF0066;
    font-weight: bold;
}

.btn-enterprise.important:hover {
    background-color: #FF0066;
    color: white;
}

/* 企業級表格樣式 */
.table-enterprise {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
}

.table-enterprise th,
.table-enterprise td {
    font-size: 11pt;
    font-weight: normal;
    padding: 8px;
    border: 1px solid var(--border-light);
    text-align: left;
}

.table-enterprise th {
    background-color: var(--table-light);
    color: var(--primary-light);
    font-weight: bold;
}

.table-enterprise .cell-light {
    background-color: var(--table-light);
    color: var(--primary-light);
}

.table-enterprise .cell-dark {
    background-color: var(--table-dark);
    color: var(--primary-light);
    border-color: var(--border-dark);
}

.table-enterprise .cell-dark.highlight {
    border-left: 3px solid var(--primary-dark);
    text-align: right;
}

.table-enterprise .cell-request {
    border-left: 3px solid #D90000;
    background-color: var(--table-dark);
    color: var(--primary-light);
    text-align: right;
}

.table-enterprise .cell-request-display {
    color: #D90000;
    background-color: var(--table-dark);
    border-left: 3px solid #D90000;
    text-align: center;
    width: 60px;
    font-weight: normal;
}

/* 導航樣式 - HTTrack 風格 */
.navigation-enterprise {
    background-color: var(--enterprise-blue-light);
    border-radius: 4px;
    margin-bottom: 15px;
}

.navigation-enterprise .nav-header {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #777777;
    border: 1px solid #ffffff;
    background-color: var(--table-light);
    font-weight: bold;
    padding: 8px;
}

.navigation-enterprise .nav-footer {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    font-weight: normal;
    color: var(--primary-light);
    border: 1px solid #ffffff;
    background-color: #ffffff;
    padding: 8px;
}

.navigation-enterprise a {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #8a8a8a;
    text-decoration: none;
    font-weight: bold;
}

.navigation-enterprise a:hover {
    color: #0482fe;
}

/* 分頁控制樣式 */
.paging-control {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: var(--primary-light);
    margin-left: 2px;
    text-align: center;
    text-decoration: none;
    background-color: #ffffff;
    border: 1px solid var(--enterprise-blue-light);
    padding: 4px 8px;
    border-radius: 3px;
    display: inline-block;
}

.paging-control:hover {
    color: #0482fe;
    background-color: var(--table-light);
}

.paging-control.disabled {
    font-weight: bold;
    background-color: #dbe8fa;
    color: var(--primary-light);
    cursor: not-allowed;
}

/* 標籤頁樣式 */
.tab-strip {
    margin-bottom: 15px;
}

.tab-strip .tab-selected {
    padding: 6px 12px;
    border-top: 1px solid #7898b5;
    background-color: #ffffff;
    height: 30px;
    border-radius: 4px 4px 0 0;
    display: inline-block;
}

.tab-strip .tab {
    padding: 6px 12px;
    border-top: 1px solid #aaaaaa;
    background-color: #becbd2;
    height: 30px;
    border-radius: 4px 4px 0 0;
    display: inline-block;
    margin-right: 2px;
}

.tab-strip a {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #0066cc;
    text-decoration: none;
}

.tab-strip a:hover {
    color: #ff0000;
}

/* 工具提示樣式 */
.tooltip-enterprise {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #003366;
    border: 1px dashed #679acd;
    visibility: hidden;
    position: absolute;
    background-color: #ffffd9;
    padding: 8px;
    border-radius: 4px;
    z-index: 1000;
}

/* 錯誤訊息樣式 */
.error-message {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #800505;
    font-weight: bold;
}

.error-message:hover {
    color: #d46060;
}

/* 驗證文字樣式 */
.validation-text {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #ff0000;
    font-weight: bold;
    margin-top: 4px;
    display: block;
}

/* 小型文字和連結樣式 */
.small-text {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    font-weight: normal;
    color: var(--primary-light);
}

.small-gray-text {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: #8a8a8a;
}

.small-bold-gray-text {
    font-family: Verdana, Tahoma, Arial;
    font-size: 8pt;
    color: #8a8a8a;
    font-weight: bold;
}

.small-links {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    color: var(--primary-light);
    text-decoration: none;
}

.small-links:hover {
    color: #0482fe;
}

.small-bold-links {
    font-family: Verdana, Arial, Tahoma;
    font-size: 8pt;
    font-weight: bold;
    color: #5f779c;
    text-decoration: none;
}

.small-bold-links:hover {
    color: #0482fe;
}

/* 引用區塊樣式 */
.quote-block {
    border-right: 1px dotted var(--enterprise-blue-light);
    padding: 6px;
    border-top: 1px dotted var(--enterprise-blue-light);
    font-size: 10pt;
    background: #ffffff;
    border-left: 4px solid #b2cce5;
    color: #5f779c;
    border-bottom: 1px dotted var(--enterprise-blue-light);
    font-family: Verdana, Arial, Tahoma;
    font-style: normal;
    font-weight: bolder;
    text-align: center;
    border-radius: 4px;
    margin: 15px 0;
}

/* 注意色彩樣式 */
.attention-color {
    background: #FFE0C0;
    border-radius: 4px;
    padding: 8px;
}

/* 邊框樣式 */
.table-border-enterprise {
    border: 5px double #b5c7de;
    width: 100%;
    border-radius: 4px;
}

/* Modal 背景樣式 */
.modal-background-enterprise {
    filter: alpha(opacity=70);
    background-color: gray;
    opacity: 0.7;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
}

/* 滑鼠懸停效果 */
.mouse-over-enterprise {
    background-color: #f0f5fa;
    transition: all 0.3s ease;
}

.mouse-over-enterprise:hover {
    background-color: #e6f3ff;
    transform: scale(1.02);
}

/* ========================================
   印刷樣式
   ======================================== */

@media print {
    body {
        background: white;
        color: black;
    }
    
    .navbar,
    .footer,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
        box-shadow: none;
    }
    
    a {
        text-decoration: underline;
    }
    
    .breadcrumb {
        background: none;
        border: 1px solid #ccc;
    }
}