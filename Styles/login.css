/* 登入頁面專用樣式 - 整合 HTTrack 企業風格 */

/* 全域設定 - 企業藍色主題 */
.login-page {
    background: linear-gradient(135deg, #0850B2 0%, #284E98 100%);
    min-height: 100vh;
    font-family: 'Microsoft JhengHei', 'PingFang TC', Verdana, Arial, Tahoma, sans-serif;
    overflow-x: hidden;
    padding: 0;
    margin: 0;
}

/* 登入容器 */
.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

/* 登入卡片 - HTTrack 企業風格 */
.login-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(8, 80, 178, 0.15);
    padding: 40px;
    width: 100%;
    max-width: 480px;
    border: 2px solid #7f9db9;
    border-top: 7px solid #0850B2;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 登入頁首 - HTTrack 企業標題風格 */
.login-header {
    margin-bottom: 30px;
    background-color: #FFFFFF;
    border-bottom: 3px solid #CCCCCC;
    padding-bottom: 15px;
}

.logo-container {
    margin-bottom: 20px;
    text-align: center;
}

.login-header h3 {
    color: #0850B2;
    font-weight: bold;
    font-size: 16pt;
    margin-bottom: 10px;
    font-family: Verdana, Arial, Tahoma, sans-serif;
}

.login-header h5 {
    color: #1f5080;
    font-weight: normal;
    font-size: 12pt;
    margin-bottom: 20px;
    font-family: Verdana, Arial, Tahoma, sans-serif;
}

.login-header p {
    color: #7f9db9;
    font-size: 10pt;
    font-family: Verdana, Arial, Tahoma, sans-serif;
}

/* 表單樣式 - HTTrack 企業風格 */
.login-form {
    margin-bottom: 20px;
}

.form-label {
    font-weight: bold;
    color: #1f5080;
    margin-bottom: 8px;
    display: block;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    font-size: 10pt;
}

.form-group {
    margin-bottom: 20px;
}

.form-control {
    border: 1px solid #7f9db9;
    border-radius: 3px;
    padding: 6px 10px;
    font-size: 10pt;
    transition: all 0.3s ease;
    background-color: white;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    color: #1f5080;
}

.form-control:focus {
    border-color: #0850B2;
    box-shadow: 0 0 5px rgba(31, 80, 128, 0.3);
    background-color: #fff;
    outline: none;
}

.form-control.is-invalid {
    border-color: #FF0000;
    background-color: rgba(255, 0, 0, 0.05);
}

/* Input Group 樣式 */
.input-group-text {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 10px 0 0 10px;
    color: #6c757d;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 10px 10px 0;
}

.input-group .form-control:focus {
    border-left: none;
}

.input-group-append .btn {
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 10px 10px 0;
    background-color: #f8f9fa;
    color: #6c757d;
}

.input-group-append .btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

/* 按鈕樣式 - HTTrack 企業風格 */
.btn-primary {
    background-color: #ffffff;
    color: #2c4b7e;
    border: 1px solid #7f9db9;
    border-radius: 3px;
    padding: 8px 16px;
    font-size: 10pt;
    font-weight: normal;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    text-transform: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #f4f8fb;
    border-color: #0850B2;
    color: #0850B2;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(8, 80, 178, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
    background-color: #ecf2f9;
    box-shadow: inset 0 2px 4px rgba(8, 80, 178, 0.2);
}

.btn-primary.important {
    color: #FF0066;
    border-color: #FF0066;
    font-weight: bold;
    font-family: 新細明體, Verdana, Arial, sans-serif;
}

.btn-primary.important:hover {
    background-color: #FF0066;
    color: white;
    border-color: #FF0066;
}

.btn-block {
    width: 100%;
}

/* 連結樣式 - HTTrack 企業風格 */
a {
    color: #1f5080;
    text-decoration: none;
    transition: color 0.3s ease;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    font-size: 8pt;
}

a:hover {
    color: #0482fe;
    text-decoration: none;
}

a.small-link {
    font-size: 8pt;
    color: #5f779c;
    font-weight: bold;
}

a.small-link:hover {
    color: #0482fe;
}

/* 警示訊息樣式 - HTTrack 企業風格 */
.alert {
    border-radius: 4px;
    border: 1px solid;
    padding: 10px 15px;
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease-out;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    font-size: 10pt;
}

/* 系統訊息樣式 */
.alert.system-message {
    background-color: #FFCCFF;
    border-color: #FF0000;
    border-left: 4px solid #FF0000;
    color: #333;
}

/* 重要提醒樣式 */
.alert.important-note {
    background-color: #6599cc;
    color: #ffffff;
    border: 3px solid #ffffff;
    text-align: center;
    font-weight: bold;
    font-family: 新細明體, sans-serif;
    font-size: 13pt;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background-color: #f8fff9;
    color: #0f5132;
    border-color: #198754;
    border-left: 4px solid #198754;
}

.alert-danger {
    background-color: #fff5f5;
    color: #721c24;
    border-color: #dc3545;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background-color: #fffbf0;
    color: #664d03;
    border-color: #ffc107;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background-color: #f0f9ff;
    color: #055160;
    border-color: #0dcaf0;
    border-left: 4px solid #0dcaf0;
}

/* 錯誤訊息樣式 */
.error-message {
    color: #800505;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    font-size: 8pt;
    font-weight: bold;
}

.error-message:hover {
    color: #d46060;
}

/* 驗證文字樣式 */
.validation-text {
    color: #ff0000;
    font-family: Verdana, Arial, Tahoma, sans-serif;
    font-size: 8pt;
    font-weight: bold;
    margin-top: 4px;
    display: block;
}

/* 安全資訊樣式 */
.security-info .alert {
    background-color: rgba(13, 202, 240, 0.1);
    border: 1px solid rgba(13, 202, 240, 0.2);
    color: #055160;
}

.security-info ul {
    padding-left: 20px;
    margin: 0;
}

.security-info li {
    margin-bottom: 5px;
}

/* 支援資訊樣式 */
.support-info {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 載入指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background-color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-content p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

/* 模態視窗樣式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: 30px;
}

.contact-info h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

.contact-info .list-unstyled li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-info .list-unstyled li:last-child {
    border-bottom: none;
}

.required-info h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
}

/* 驗證錯誤樣式 */
.text-danger {
    color: #dc3545 !important;
    font-size: 13px;
    margin-top: 5px;
}

.text-warning {
    color: #ffc107 !important;
    font-size: 13px;
    margin-top: 5px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .login-container {
        padding: 15px;
    }
    
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .login-header h3 {
        font-size: 1.5rem;
    }
    
    .login-header h5 {
        font-size: 1.1rem;
    }
    
    .btn-lg {
        padding: 15px 20px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 25px 15px;
        border-radius: 15px;
    }
    
    .login-header h3 {
        font-size: 1.3rem;
    }
    
    .login-header h5 {
        font-size: 1rem;
    }
    
    .form-control {
        font-size: 16px; /* 防止 iOS 縮放 */
    }
    
    .modal-body {
        padding: 20px;
    }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
    .login-card {
        background-color: white;
        border: 2px solid #000;
    }
    
    .form-control {
        border: 2px solid #000;
    }
    
    .btn-primary {
        background: #000;
        border: 2px solid #000;
    }
}

/* 暗色主題支援 */
@media (prefers-color-scheme: dark) {
    .login-page {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .login-card {
        background: rgba(52, 73, 94, 0.95);
        color: #ecf0f1;
    }
    
    .login-header h3 {
        color: #ecf0f1;
    }
    
    .login-header h5 {
        color: #bdc3c7;
    }
    
    .form-label {
        color: #ecf0f1;
    }
    
    .form-control {
        background-color: rgba(44, 62, 80, 0.5);
        border-color: #7f8c8d;
        color: #ecf0f1;
    }
    
    .form-control::placeholder {
        color: #95a5a6;
    }
}

/* 動畫和過渡效果 */
.login-card,
.form-control,
.btn,
.alert {
    transition: all 0.3s ease;
}

/* 焦點可見性增強 */
.form-control:focus,
.btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* 無障礙樣式 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}