using System;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using CWDECC_3S.Services;
using CWDECC_3S.Models;

namespace CWDECC_3S
{
    /// <summary>
    /// 登入頁面 - 處理用戶驗證和 Session 管理
    /// </summary>
    public partial class Login : Page
    {
        private AuthenticationService _authService;
        private AuditService _auditService;

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化服務
                _authService = new AuthenticationService();
                _auditService = new AuditService();

                if (!IsPostBack)
                {
                    InitializePage();
                }
            }
            catch (Exception ex)
            {
                LogError("頁面載入錯誤", ex);
                ShowMessage("系統暫時無法使用，請稍後再試", "danger");
            }
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            // 註冊客戶端腳本事件
            btnLogin.Attributes["onclick"] = "return onLoginSubmit();";
            lnkForgotPassword.Attributes["onclick"] = "showForgotPasswordModal(); return false;";
        }

        #region 頁面初始化

        private void InitializePage()
        {
            try
            {
                // 檢查是否已經登入
                if (IsUserAuthenticated())
                {
                    RedirectToDefaultPage();
                    return;
                }

                // 檢查是否有返回 URL
                HandleReturnUrl();

                // 設定頁面標題
                Page.Title = "登入 - 香港基督教培道聯愛會陳維周夫人紀念長者學苑";

                // 清除任何現有的錯誤訊息
                HideMessage();

                // 記錄頁面存取
                LogPageAccess();
            }
            catch (Exception ex)
            {
                LogError("頁面初始化錯誤", ex);
            }
        }

        private bool IsUserAuthenticated()
        {
            return _authService?.IsSessionValid() == true && 
                   Session["IsAuthenticated"] as bool? == true;
        }

        private void HandleReturnUrl()
        {
            var returnUrl = Request.QueryString["ReturnUrl"];
            if (!string.IsNullOrEmpty(returnUrl) && IsLocalUrl(returnUrl))
            {
                ViewState["ReturnUrl"] = returnUrl;
            }
        }

        private void LogPageAccess()
        {
            var ipAddress = AuthenticationService.GetClientIPAddress();
            var userAgent = Request.UserAgent;
            
            _auditService?.LogAsync("LOGIN_PAGE_ACCESS", null, null, ipAddress, 
                "用戶存取登入頁面", userAgent);
        }

        #endregion

        #region 登入處理

        protected async void btnLogin_Click(object sender, EventArgs e)
        {
            if (!IsPostBack || !Page.IsValid)
                return;

            try
            {
                await ProcessLoginAsync();
            }
            catch (Exception ex)
            {
                LogError("登入處理錯誤", ex);
                ShowMessage("登入過程中發生錯誤，請稍後再試", "danger");
            }
            finally
            {
                // 確保清除敏感資訊
                txtPassword.Text = string.Empty;
            }
        }

        private async Task ProcessLoginAsync()
        {
            var userName = txtUserName.Text.Trim();
            var password = txtPassword.Text;
            var ipAddress = AuthenticationService.GetClientIPAddress();
            var userAgent = Request.UserAgent;

            // 基本驗證
            if (string.IsNullOrEmpty(userName) || string.IsNullOrEmpty(password))
            {
                ShowMessage("請輸入用戶名和密碼", "warning");
                return;
            }

            // 檢查暴力破解防護
            if (await IsIPBlocked(ipAddress))
            {
                ShowMessage("由於多次登入失敗，您的 IP 已被暫時鎖定，請稍後再試", "danger");
                return;
            }

            // 執行登入驗證
            var loginResult = await _authService.AuthenticateAsync(userName, password, ipAddress, userAgent);

            if (loginResult.Success)
            {
                await HandleSuccessfulLogin(loginResult);
            }
            else
            {
                await HandleFailedLogin(userName, loginResult.Message, ipAddress, userAgent);
            }
        }

        private async Task<bool> IsIPBlocked(string ipAddress)
        {
            try
            {
                // 檢查過去 30 分鐘內的失敗登入次數
                var failedCount = await _auditService.GetFailedLoginCountAsync(ipAddress, DateTime.UtcNow.AddMinutes(-30));
                
                if (failedCount >= 10) // IP 層面的防護：30 分鐘內 10 次失敗
                {
                    await _auditService.LogSecurityEventAsync(null, null, ipAddress, Request.UserAgent, 
                        "IP_BLOCKED", $"IP 被鎖定 - 30分鐘內失敗登入 {failedCount} 次");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogError("檢查 IP 鎖定狀態錯誤", ex);
                return false; // 錯誤時不阻擋登入
            }
        }

        private async Task HandleSuccessfulLogin(LoginResult loginResult)
        {
            try
            {
                // 清除登入嘗試計數
                if (Session["LoginAttempts"] != null)
                {
                    Session.Remove("LoginAttempts");
                }

                // 設定成功訊息
                ShowMessage($"歡迎回來，{loginResult.User.DisplayName ?? loginResult.User.UserName}！", "success");

                // 延遲重定向，讓用戶看到成功訊息
                var redirectUrl = GetRedirectUrl(loginResult);
                var script = $@"
                    setTimeout(function() {{
                        window.location.href = '{redirectUrl}';
                    }}, 1500);
                ";
                
                ClientScript.RegisterStartupScript(this.GetType(), "RedirectScript", script, true);
            }
            catch (Exception ex)
            {
                LogError("處理成功登入錯誤", ex);
                // 即使有錯誤，也要嘗試重定向
                Response.Redirect(GetRedirectUrl(loginResult), false);
            }
        }

        private async Task HandleFailedLogin(string userName, string errorMessage, string ipAddress, string userAgent)
        {
            try
            {
                // 增加登入嘗試計數
                var attempts = (Session["LoginAttempts"] as int?) ?? 0;
                attempts++;
                Session["LoginAttempts"] = attempts;
                Session["LastLoginAttempt"] = DateTime.UtcNow;

                // 顯示錯誤訊息
                ShowMessage(errorMessage, "danger");

                // 如果嘗試次數過多，顯示額外警告
                if (attempts >= 3)
                {
                    var warningMessage = $"連續登入失敗 {attempts} 次。請檢查您的用戶名和密碼，或聯絡管理員協助。";
                    ShowMessage(warningMessage, "warning");
                    
                    // 記錄安全事件
                    await _auditService.LogSecurityEventAsync(null, userName, ipAddress, userAgent,
                        "MULTIPLE_FAILED_LOGINS", $"用戶連續登入失敗 {attempts} 次");
                }

                // 清除密碼欄位
                txtPassword.Text = string.Empty;
                
                // 設定焦點到密碼欄位（如果用戶名看起來正確）或用戶名欄位
                if (IsValidEmailOrUsername(userName))
                {
                    txtPassword.Focus();
                }
                else
                {
                    txtUserName.Focus();
                }
            }
            catch (Exception ex)
            {
                LogError("處理失敗登入錯誤", ex);
            }
        }

        private string GetRedirectUrl(LoginResult loginResult)
        {
            // 優先使用 ReturnUrl
            var returnUrl = ViewState["ReturnUrl"] as string;
            if (!string.IsNullOrEmpty(returnUrl) && IsLocalUrl(returnUrl))
            {
                return returnUrl;
            }

            // 使用登入結果中的重定向 URL
            if (!string.IsNullOrEmpty(loginResult.RedirectUrl))
            {
                return ResolveUrl(loginResult.RedirectUrl);
            }

            // 預設重定向到主頁
            return ResolveUrl("~/Default.aspx");
        }

        private bool IsValidEmailOrUsername(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            // 檢查電子郵件格式
            if (input.Contains("@"))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(input);
                    return addr.Address == input;
                }
                catch
                {
                    return false;
                }
            }

            // 檢查用戶名格式（字母、數字、底線、點）
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z0-9._]+$") && input.Length >= 3;
        }

        #endregion

        #region 忘記密碼處理

        protected void lnkForgotPassword_Click(object sender, EventArgs e)
        {
            try
            {
                // 記錄忘記密碼請求
                var ipAddress = AuthenticationService.GetClientIPAddress();
                var userAgent = Request.UserAgent;
                
                _auditService?.LogAsync("FORGOT_PASSWORD_REQUEST", null, null, ipAddress, 
                    "用戶點擊忘記密碼連結", userAgent);

                // 客戶端會處理模態視窗顯示
            }
            catch (Exception ex)
            {
                LogError("忘記密碼處理錯誤", ex);
            }
        }

        #endregion

        #region 輔助方法

        private void ShowMessage(string message, string type)
        {
            ltlMessage.Text = message;
            
            // 設定 CSS 類別
            pnlMessage.CssClass = $"alert alert-{type} alert-dismissible fade show";
            
            pnlMessage.Visible = true;
        }

        private void HideMessage()
        {
            pnlMessage.Visible = false;
            ltlMessage.Text = string.Empty;
        }

        private void RedirectToDefaultPage()
        {
            var userRole = Session["Role"] as string;
            string redirectUrl;

            switch (userRole?.ToLower())
            {
                case "administrator":
                    redirectUrl = "~/Admin/Dashboard.aspx";
                    break;
                case "staffmember":
                    redirectUrl = "~/Staff/Dashboard.aspx";
                    break;
                case "teacher":
                    redirectUrl = "~/Teacher/Dashboard.aspx";
                    break;
                default:
                    redirectUrl = "~/Default.aspx";
                    break;
            }

            Response.Redirect(redirectUrl, false);
        }

        private bool IsLocalUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            // 檢查是否為相對 URL
            if (url.StartsWith("/"))
                return true;

            // 檢查是否為相對 URL（~/ 開頭）
            if (url.StartsWith("~/"))
                return true;

            // 拒絕絕對 URL 和 JavaScript
            if (url.StartsWith("http://", StringComparison.OrdinalIgnoreCase) ||
                url.StartsWith("https://", StringComparison.OrdinalIgnoreCase) ||
                url.StartsWith("javascript:", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        private void LogError(string message, Exception ex)
        {
            try
            {
                var errorMessage = $"{message}: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $" Inner: {ex.InnerException.Message}";
                }

                var ipAddress = AuthenticationService.GetClientIPAddress();
                _auditService?.LogAsync("LOGIN_ERROR", null, null, ipAddress, errorMessage, Request.UserAgent);

                // 同時記錄到系統事件日誌
                System.Diagnostics.EventLog.WriteEntry("CWDECC_3S", 
                    $"Login.aspx: {errorMessage}\nStackTrace: {ex.StackTrace}", 
                    System.Diagnostics.EventLogEntryType.Error);
            }
            catch
            {
                // 記錄失敗時不應影響主要功能
            }
        }

        #endregion

        #region 頁面清理

        protected override void OnUnload(EventArgs e)
        {
            try
            {
                _authService?.Dispose();
                _auditService?.Dispose();
            }
            catch
            {
                // 清理時忽略錯誤
            }
            
            base.OnUnload(e);
        }

        #endregion
    }
}