<%@ Page Title="產品管理" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ProductManagement.aspx.cs" Inherits="CWDECC_3S.Sales.ProductManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-fluid mt-4">
        <!-- 頁面標題 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i class="fas fa-boxes text-primary me-2"></i>產品管理
                </h2>
                <p class="text-muted">管理商店產品、庫存及圖片</p>
            </div>
            <div>
                <asp:Button ID="btnAddProduct" runat="server" Text="新增產品" CssClass="btn btn-success"
                    OnClick="btnAddProduct_Click" />
                <asp:Button ID="btnRefresh" runat="server" Text="重新載入" CssClass="btn btn-outline-primary"
                    OnClick="btnRefresh_Click" CausesValidation="false" />
            </div>
        </div>

        <!-- 系統訊息 -->
        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert" role="alert" Visible="false">
            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </asp:Panel>

        <div class="row">
            <!-- 左側：搜尋與篩選 -->
            <div class="col-lg-3">
                <!-- 搜尋功能 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i>搜尋篩選
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="<%= txtSearch.ClientID %>">搜尋關鍵字</label>
                            <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" 
                                placeholder="產品名稱、編號或描述"></asp:TextBox>
                        </div>
                        <div class="form-group mb-3">
                            <label for="<%= ddlCategoryFilter.ClientID %>">產品類別</label>
                            <asp:DropDownList ID="ddlCategoryFilter" runat="server" CssClass="form-select">
                                <asp:ListItem Text="全部類別" Value=""></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <div class="form-group mb-3">
                            <label for="<%= ddlStatusFilter.ClientID %>">產品狀態</label>
                            <asp:DropDownList ID="ddlStatusFilter" runat="server" CssClass="form-select">
                                <asp:ListItem Text="全部狀態" Value=""></asp:ListItem>
                                <asp:ListItem Text="啟用" Value="Active"></asp:ListItem>
                                <asp:ListItem Text="停用" Value="Inactive"></asp:ListItem>
                                <asp:ListItem Text="缺貨" Value="OutOfStock"></asp:ListItem>
                                <asp:ListItem Text="庫存不足" Value="LowStock"></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                        <asp:Button ID="btnSearch" runat="server" Text="搜尋" CssClass="btn btn-primary w-100"
                            OnClick="btnSearch_Click" />
                    </div>
                </div>

                <!-- 快速統計 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>快速統計
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary">
                                        <asp:Label ID="lblTotalProducts" runat="server" Text="0"></asp:Label>
                                    </h4>
                                    <small class="text-muted">總產品</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">
                                    <asp:Label ID="lblActiveProducts" runat="server" Text="0"></asp:Label>
                                </h4>
                                <small class="text-muted">啟用中</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-warning">
                                        <asp:Label ID="lblLowStockProducts" runat="server" Text="0"></asp:Label>
                                    </h4>
                                    <small class="text-muted">庫存不足</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-danger">
                                    <asp:Label ID="lblOutOfStockProducts" runat="server" Text="0"></asp:Label>
                                </h4>
                                <small class="text-muted">缺貨</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側：產品列表 -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>產品列表
                        </h5>
                        <asp:Label ID="lblRecordCount" runat="server" CssClass="badge bg-secondary"></asp:Label>
                    </div>
                    <div class="card-body">
                        <asp:Panel ID="pnlProducts" runat="server" Visible="false">
                            <div class="table-responsive">
                                <asp:GridView ID="gvProducts" runat="server" CssClass="table table-hover table-striped"
                                    AutoGenerateColumns="false" DataKeyNames="Id" AllowPaging="true" PageSize="15"
                                    OnPageIndexChanging="gvProducts_PageIndexChanging" OnRowCommand="gvProducts_RowCommand"
                                    OnRowDataBound="gvProducts_RowDataBound" EmptyDataText="沒有找到符合條件的產品">
                                    <Columns>
                                        <asp:TemplateField HeaderText="圖片" ItemStyle-Width="80px">
                                            <ItemTemplate>
                                                <div class="text-center">
                                                    <asp:Image ID="imgProduct" runat="server" CssClass="img-thumbnail product-thumb"
                                                        Width="50" Height="50" />
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:BoundField DataField="ProductCode" HeaderText="產品編號" SortExpression="ProductCode" />
                                        <asp:BoundField DataField="ProductName" HeaderText="產品名稱" SortExpression="ProductName" />
                                        <asp:BoundField DataField="Category" HeaderText="類別" SortExpression="Category" />
                                        <asp:BoundField DataField="UnitPrice" HeaderText="單價" SortExpression="UnitPrice" DataFormatString="HK${0:F2}" />
                                        <asp:BoundField DataField="StockQuantity" HeaderText="庫存" SortExpression="StockQuantity" />
                                        <asp:TemplateField HeaderText="狀態" SortExpression="IsActive">
                                            <ItemTemplate>
                                                <asp:Label ID="lblStatus" runat="server" CssClass='<%# Eval("StatusCssClass") %>'
                                                    Text='<%# Eval("StatusDisplayText") %>'></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:BoundField DataField="CreatedDate" HeaderText="建立時間" SortExpression="CreatedDate" DataFormatString="{0:MM/dd HH:mm}" />
                                        <asp:TemplateField HeaderText="操作" ItemStyle-Width="200px">
                                            <ItemTemplate>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <asp:Button ID="btnEdit" runat="server" Text="編輯" CssClass="btn btn-outline-primary btn-sm"
                                                        CommandName="EditProduct" CommandArgument='<%# Eval("Id") %>' />
                                                    <asp:Button ID="btnUploadImage" runat="server" Text="上傳圖片" CssClass="btn btn-outline-info btn-sm"
                                                        CommandName="UploadImage" CommandArgument='<%# Eval("Id") %>' />
                                                    <asp:Button ID="btnDelete" runat="server" Text="刪除" CssClass="btn btn-outline-danger btn-sm"
                                                        CommandName="DeleteProduct" CommandArgument='<%# Eval("Id") %>'
                                                        OnClientClick="return confirm('確定要刪除此產品嗎？');" />
                                                </div>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                    <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" 
                                        NextPageText="下一頁" PreviousPageText="上一頁" />
                                    <PagerStyle CssClass="pagination justify-content-center" />
                                </asp:GridView>
                            </div>
                        </asp:Panel>

                        <asp:Panel ID="pnlNoProducts" runat="server" Visible="true" CssClass="text-center py-5">
                            <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">沒有找到產品</h5>
                            <p class="text-muted">請新增產品或調整搜尋條件</p>
                            <asp:Button ID="btnAddFirstProduct" runat="server" Text="新增第一個產品" CssClass="btn btn-primary"
                                OnClick="btnAddProduct_Click" />
                        </asp:Panel>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 產品編輯模態視窗 -->
    <div class="modal fade" id="productEditModal" tabindex="-1" aria-labelledby="productEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productEditModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        <asp:Label ID="lblModalTitle" runat="server" Text="新增產品"></asp:Label>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hfProductId" runat="server" />
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="<%= txtProductName.ClientID %>">產品名稱 <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtProductName" runat="server" CssClass="form-control" MaxLength="100" required></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvProductName" runat="server" ControlToValidate="txtProductName"
                                    ErrorMessage="產品名稱為必填" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="<%= txtProductCode.ClientID %>">產品編號</label>
                                <asp:TextBox ID="txtProductCode" runat="server" CssClass="form-control" MaxLength="20" placeholder="留空自動生成"></asp:TextBox>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="<%= ddlCategory.ClientID %>">產品類別 <span class="text-danger">*</span></label>
                                <asp:DropDownList ID="ddlCategory" runat="server" CssClass="form-select" required>
                                    <asp:ListItem Text="請選擇類別" Value=""></asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvCategory" runat="server" ControlToValidate="ddlCategory"
                                    ErrorMessage="產品類別為必填" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="<%= txtUnitPrice.ClientID %>">單價 (HK$) <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtUnitPrice" runat="server" CssClass="form-control" TextMode="Number" 
                                    step="0.01" min="0.01" required></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvUnitPrice" runat="server" ControlToValidate="txtUnitPrice"
                                    ErrorMessage="單價為必填" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvUnitPrice" runat="server" ControlToValidate="txtUnitPrice"
                                    Type="Double" MinimumValue="0.01" MaximumValue="99999.99"
                                    ErrorMessage="單價必須介於 0.01 至 99999.99" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="<%= txtStockQuantity.ClientID %>">庫存數量 <span class="text-danger">*</span></label>
                                <asp:TextBox ID="txtStockQuantity" runat="server" CssClass="form-control" TextMode="Number" 
                                    min="0" required></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvStockQuantity" runat="server" ControlToValidate="txtStockQuantity"
                                    ErrorMessage="庫存數量為必填" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvStockQuantity" runat="server" ControlToValidate="txtStockQuantity"
                                    Type="Integer" MinimumValue="0" MaximumValue="999999"
                                    ErrorMessage="庫存數量必須介於 0 至 999999" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RangeValidator>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="<%= txtMinStockLevel.ClientID %>">最低庫存警告</label>
                                <asp:TextBox ID="txtMinStockLevel" runat="server" CssClass="form-control" TextMode="Number" 
                                    min="0" placeholder="選填"></asp:TextBox>
                                <asp:RangeValidator ID="rvMinStockLevel" runat="server" ControlToValidate="txtMinStockLevel"
                                    Type="Integer" MinimumValue="0" MaximumValue="999999"
                                    ErrorMessage="最低庫存必須介於 0 至 999999" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ProductEdit"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="<%= txtDescription.ClientID %>">產品描述</label>
                        <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine"
                            Rows="3" MaxLength="500" placeholder="產品詳細描述"></asp:TextBox>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <asp:CheckBox ID="cbIsActive" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= cbIsActive.ClientID %>">
                                    啟用產品
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <asp:CheckBox ID="cbIsAvailable" runat="server" CssClass="form-check-input" Checked="true" />
                                <label class="form-check-label" for="<%= cbIsAvailable.ClientID %>">
                                    允許銷售
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="<%= txtRemarks.ClientID %>">備註</label>
                        <asp:TextBox ID="txtRemarks" runat="server" CssClass="form-control" TextMode="MultiLine"
                            Rows="2" MaxLength="1000" placeholder="其他備註資訊"></asp:TextBox>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnSaveProduct" runat="server" Text="儲存" CssClass="btn btn-primary"
                        OnClick="btnSaveProduct_Click" ValidationGroup="ProductEdit" />
                </div>
            </div>
        </div>
    </div>

    <!-- 圖片上傳模態視窗 -->
    <div class="modal fade" id="imageUploadModal" tabindex="-1" aria-labelledby="imageUploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageUploadModalLabel">
                        <i class="fas fa-image me-2"></i>上傳產品圖片
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <asp:HiddenField ID="hfImageProductId" runat="server" />
                    
                    <div class="form-group mb-3">
                        <label for="<%= fuProductImage.ClientID %>">選擇圖片檔案</label>
                        <asp:FileUpload ID="fuProductImage" runat="server" CssClass="form-control" 
                            accept="image/*" required />
                        <div class="form-text">
                            支援 JPG、PNG、GIF、BMP 格式，檔案大小不超過 5MB
                        </div>
                        <asp:RequiredFieldValidator ID="rfvProductImage" runat="server" ControlToValidate="fuProductImage"
                            ErrorMessage="請選擇圖片檔案" Display="Dynamic" CssClass="text-danger small" ValidationGroup="ImageUpload"></asp:RequiredFieldValidator>
                    </div>

                    <div id="imagePreview" class="text-center mb-3" style="display: none;">
                        <img id="previewImg" src="#" alt="圖片預覽" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <asp:Button ID="btnUploadImage" runat="server" Text="上傳" CssClass="btn btn-primary"
                        OnClick="btnUploadImage_Click" ValidationGroup="ImageUpload" />
                </div>
            </div>
        </div>
    </div>

    <style>
        .product-thumb {
            object-fit: cover;
            border-radius: 4px;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            white-space: nowrap;
        }
        
        .btn-group-sm .btn {
            margin-right: 2px;
        }
        
        .modal-lg {
            max-width: 800px;
        }
        
        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .text-danger {
            color: #dc3545 !important;
        }
        
        @media print {
            .btn, .modal, .pagination {
                display: none !important;
            }
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function() {
            // 圖片預覽功能
            $('#<%= fuProductImage.ClientID %>').change(function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#previewImg').attr('src', e.target.result);
                        $('#imagePreview').show();
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#imagePreview').hide();
                }
            });

            // 表格行點擊效果
            $('.table tbody tr').hover(
                function() { $(this).addClass('table-active'); },
                function() { $(this).removeClass('table-active'); }
            );
        });

        // 顯示產品編輯模態視窗
        function showProductEditModal() {
            $('#productEditModal').modal('show');
        }

        // 顯示圖片上傳模態視窗
        function showImageUploadModal() {
            $('#imageUploadModal').modal('show');
        }

        // 確認刪除
        function confirmDelete(productName) {
            return confirm('確定要刪除產品 "' + productName + '" 嗎？\n\n注意：如果產品有銷售記錄，將改為停用產品。');
        }
    </script>
</asp:Content>