<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="CWDECC_3S.Login" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入 - 香港基督教培道聯愛會陳維周夫人紀念長者學苑</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Login Styles -->
    <link href="Styles/login.css" rel="stylesheet">
</head>
<body class="login-page">
    <form id="form1" runat="server">
        <!-- 登入容器 -->
        <div class="login-container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7 col-sm-9">
                    <div class="login-card">
                        <!-- 機構標識 - HTTrack 企業風格 -->
                        <div class="login-header text-center enterprise-header">
                            <div class="logo-container">
                                <i class="fas fa-graduation-cap fa-3x mb-3" style="color: #0850B2;"></i>
                            </div>
                            <h3 class="mb-2">香港基督教培道聯愛會</h3>
                            <h5 class="mb-4">陳維周夫人紀念長者學苑</h5>
                            <p>請登入您的帳戶以繼續</p>
                        </div>

                        <!-- 錯誤訊息顯示 - HTTrack 企業風格 -->
                        <asp:Panel ID="pnlMessage" runat="server" CssClass="alert system-message" 
                            role="alert" Visible="false">
                            <asp:Literal ID="ltlMessage" runat="server"></asp:Literal>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </asp:Panel>

                        <!-- 登入表單 -->
                        <div class="login-form">
                            <div class="form-group">
                                <label for="<%= txtUserName.ClientID %>" class="form-label">
                                    <i class="fas fa-user me-2"></i>用戶名或電子郵件
                                </label>
                                <asp:TextBox ID="txtUserName" runat="server" 
                                    CssClass="form-control form-input-enterprise" 
                                    placeholder="請輸入用戶名或電子郵件"
                                    autocomplete="username"
                                    TabIndex="1">
                                </asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvUserName" runat="server" 
                                    ControlToValidate="txtUserName"
                                    ErrorMessage="請輸入用戶名或電子郵件"
                                    CssClass="validation-text"
                                    Display="Dynamic">
                                </asp:RequiredFieldValidator>
                            </div>

                            <div class="form-group">
                                <label for="<%= txtPassword.ClientID %>" class="form-label">
                                    <i class="fas fa-lock me-2"></i>密碼
                                </label>
                                <div class="input-group">
                                    <asp:TextBox ID="txtPassword" runat="server" 
                                        TextMode="Password"
                                        CssClass="form-control form-input-enterprise" 
                                        placeholder="請輸入密碼"
                                        autocomplete="current-password"
                                        TabIndex="2" 
                                        style="width: calc(100% - 40px); display: inline-block;">
                                    </asp:TextBox>
                                    <button type="button" class="btn-enterprise" 
                                        onclick="togglePassword()" tabindex="-1"
                                        style="width: 35px; margin-left: 5px; display: inline-block; vertical-align: top;">
                                        <i id="passwordToggleIcon" class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <asp:RequiredFieldValidator ID="rfvPassword" runat="server" 
                                    ControlToValidate="txtPassword"
                                    ErrorMessage="請輸入密碼"
                                    CssClass="validation-text"
                                    Display="Dynamic">
                                </asp:RequiredFieldValidator>
                            </div>

                            <!-- 記住我選項（暫時隱藏） -->
                            <div class="form-group form-check d-none">
                                <asp:CheckBox ID="chkRememberMe" runat="server" 
                                    CssClass="form-check-input" />
                                <label class="form-check-label" for="<%= chkRememberMe.ClientID %>">
                                    記住我的登入狀態
                                </label>
                            </div>

                            <!-- 登入按鈕 - HTTrack 企業風格 -->
                            <div class="form-group">
                                <asp:Button ID="btnLogin" runat="server" 
                                    Text="登入 CWDECC-3S 系統"
                                    CssClass="btn-enterprise important" 
                                    OnClick="btnLogin_Click"
                                    TabIndex="3"
                                    style="width: 100%; padding: 12px; font-size: 12pt;">
                                </asp:Button>
                            </div>

                            <!-- 忘記密碼連結 - HTTrack 企業風格 -->
                            <div class="text-center">
                                <asp:LinkButton ID="lnkForgotPassword" runat="server" 
                                    CssClass="small-link"
                                    OnClick="lnkForgotPassword_Click"
                                    CausesValidation="false"
                                    TabIndex="4">
                                    <i class="fas fa-question-circle me-1"></i>忘記密碼？
                                </asp:LinkButton>
                            </div>
                        </div>

                        <!-- 安全信息 - HTTrack 企業風格 -->
                        <div class="security-info mt-4">
                            <div class="alert important-note">
                                <i class="fas fa-shield-alt me-2"></i>
                                <strong>系統安全提醒</strong>
                                <div class="mt-2 small-text" style="color: white;">
                                    • 請妥善保管您的帳戶資訊<br/>
                                    • 密碼連續錯誤 5 次將被鎖定 30 分鐘<br/>
                                    • 系統會記錄所有登入活動<br/>
                                    • 使用完畢請務必登出
                                </div>
                            </div>
                        </div>

                        <!-- 技術支援資訊 - HTTrack 企業風格 -->
                        <div class="support-info text-center mt-3">
                            <div class="small-text">
                                遇到問題？請聯絡 IT 支援：
                                <a href="mailto:<EMAIL>" class="small-link">
                                    <i class="fas fa-envelope me-1"></i><EMAIL>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 忘記密碼模態視窗 -->
        <div class="modal fade" id="forgotPasswordModal" tabindex="-1" role="dialog" 
             aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="forgotPasswordModalLabel">
                            <i class="fas fa-key me-2"></i>密碼重設
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>重要提醒：</strong>
                            <p class="mb-0 mt-2">
                                基於安全考量，密碼重設功能需要由系統管理員處理。
                                請聯絡 IT 部門或管理員協助重設密碼。
                            </p>
                        </div>
                        
                        <div class="contact-info">
                            <h6>聯絡方式：</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    電子郵件：<a href="mailto:<EMAIL>"><EMAIL></a>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-phone text-success me-2"></i>
                                    電話：(852) 2345-6789
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    辦公時間：週一至週五 9:00-17:00
                                </li>
                            </ul>
                        </div>

                        <div class="required-info mt-3">
                            <h6>重設密碼時請準備：</h6>
                            <ul class="small text-muted">
                                <li>身份證明文件</li>
                                <li>用戶名或註冊電子郵件</li>
                                <li>聯絡電話（用於身份驗證）</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fas fa-times me-1"></i>關閉
                        </button>
                        <a href="mailto:<EMAIL>?subject=密碼重設申請&body=請協助重設密碼，我的用戶名是：" 
                           class="btn btn-primary">
                            <i class="fas fa-envelope me-1"></i>發送郵件
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 載入指示器 -->
        <div id="loadingIndicator" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">載入中...</span>
                </div>
                <p class="mt-3">正在驗證您的身份...</p>
            </div>
        </div>
    </form>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 登入頁面 JavaScript -->
    <script type="text/javascript">
        $(document).ready(function() {
            // 頁面載入完成初始化
            initializeLoginPage();
            
            // 焦點設定到用戶名欄位
            $('#<%= txtUserName.ClientID %>').focus();
            
            // Enter 鍵提交表單
            setupEnterKeySubmission();
            
            // 表單驗證增強
            setupFormValidation();
        });

        function initializeLoginPage() {
            console.log('登入頁面已初始化');
            
            // 檢查瀏覽器支援
            if (typeof Storage === "undefined") {
                console.warn('瀏覽器不支援 Local Storage');
            }
            
            // 清除任何遺留的登入狀態
            clearLoginState();
        }

        function setupEnterKeySubmission() {
            $('#<%= txtUserName.ClientID %>, #<%= txtPassword.ClientID %>').keypress(function(e) {
                if (e.which === 13) { // Enter 鍵
                    e.preventDefault();
                    $('#<%= btnLogin.ClientID %>').click();
                }
            });
        }

        function setupFormValidation() {
            // 即時驗證用戶名格式
            $('#<%= txtUserName.ClientID %>').on('blur', function() {
                var username = $(this).val().trim();
                var isValid = validateUsername(username);
                
                if (username && !isValid) {
                    showFieldError(this, '請輸入有效的用戶名或電子郵件格式');
                } else {
                    clearFieldError(this);
                }
            });

            // 密碼強度指示（簡化版）
            $('#<%= txtPassword.ClientID %>').on('input', function() {
                var password = $(this).val();
                if (password.length > 0 && password.length < 8) {
                    showFieldHint(this, '密碼長度至少需要 8 個字元', 'warning');
                } else {
                    clearFieldHint(this);
                }
            });
        }

        function validateUsername(username) {
            if (!username) return false;
            
            // 檢查是否為電子郵件格式
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailRegex.test(username)) {
                return true;
            }
            
            // 檢查是否為有效用戶名（字母、數字、底線、點）
            var usernameRegex = /^[a-zA-Z0-9._]+$/;
            return usernameRegex.test(username) && username.length >= 3;
        }

        function showFieldError(field, message) {
            clearFieldError(field);
            var errorDiv = $('<div class="text-danger small mt-1 field-error">' + message + '</div>');
            $(field).closest('.form-group').append(errorDiv);
            $(field).addClass('is-invalid');
        }

        function clearFieldError(field) {
            $(field).closest('.form-group').find('.field-error').remove();
            $(field).removeClass('is-invalid');
        }

        function showFieldHint(field, message, type) {
            clearFieldHint(field);
            var hintClass = type === 'warning' ? 'text-warning' : 'text-info';
            var hintDiv = $('<div class="' + hintClass + ' small mt-1 field-hint">' + message + '</div>');
            $(field).closest('.form-group').append(hintDiv);
        }

        function clearFieldHint(field) {
            $(field).closest('.form-group').find('.field-hint').remove();
        }

        function togglePassword() {
            var passwordField = document.getElementById('<%= txtPassword.ClientID %>');
            var toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        function showForgotPasswordModal() {
            $('#forgotPasswordModal').modal('show');
        }

        function showLoadingIndicator() {
            $('#loadingIndicator').fadeIn();
            
            // 禁用表單元素
            $('#<%= txtUserName.ClientID %>, #<%= txtPassword.ClientID %>, #<%= btnLogin.ClientID %>').prop('disabled', true);
        }

        function hideLoadingIndicator() {
            $('#loadingIndicator').fadeOut();
            
            // 重新啟用表單元素
            $('#<%= txtUserName.ClientID %>, #<%= txtPassword.ClientID %>, #<%= btnLogin.ClientID %>').prop('disabled', false);
        }

        function clearLoginState() {
            // 清除可能的 Local Storage 登入狀態
            if (typeof Storage !== "undefined") {
                localStorage.removeItem('loginAttempts');
                localStorage.removeItem('lastLoginTime');
            }
        }

        // 表單提交前的處理
        function onLoginSubmit() {
            // 顯示載入指示器
            showLoadingIndicator();
            
            // 記錄登入嘗試
            recordLoginAttempt();
            
            return true; // 允許表單提交
        }

        function recordLoginAttempt() {
            if (typeof Storage !== "undefined") {
                var attempts = parseInt(localStorage.getItem('loginAttempts') || '0');
                localStorage.setItem('loginAttempts', (attempts + 1).toString());
                localStorage.setItem('lastLoginTime', new Date().toISOString());
            }
        }

        // 安全相關功能
        function preventBruteForce() {
            if (typeof Storage !== "undefined") {
                var attempts = parseInt(localStorage.getItem('loginAttempts') || '0');
                var lastAttempt = localStorage.getItem('lastLoginTime');
                
                if (attempts >= 3 && lastAttempt) {
                    var timeDiff = new Date() - new Date(lastAttempt);
                    var minutesDiff = timeDiff / (1000 * 60);
                    
                    if (minutesDiff < 5) { // 5 分鐘內嘗試超過 3 次
                        alert('登入嘗試過於頻繁，請稍後再試');
                        return false;
                    }
                }
            }
            return true;
        }

        // 頁面卸載時的清理
        $(window).on('beforeunload', function() {
            hideLoadingIndicator();
        });
    </script>
</body>
</html>