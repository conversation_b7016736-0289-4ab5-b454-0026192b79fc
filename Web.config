<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  
  <appSettings>
    <!-- Firebase Configuration -->
    <add key="FirebaseProjectId" value="your-firebase-project-id" />
    <add key="FirebaseApiKey" value="your-firebase-api-key" />
    <add key="FirebaseAuthDomain" value="your-firebase-project-id.firebaseapp.com" />
    <add key="FirebaseDatabaseURL" value="https://your-firebase-project-id-default-rtdb.firebaseio.com/" />
    <add key="FirebaseStorageBucket" value="your-firebase-project-id.appspot.com" />
    
    <!-- Firebase Service Account Credentials -->
    <add key="FirebaseCredentialsPath" value="App_Data/firebase-service-account.json" />
    
    <!-- Firebase Settings -->
    <add key="FirebaseTimeout" value="30000" />
    <add key="FirebaseRetryAttempts" value="3" />
    <add key="FirebaseEnableLogging" value="true" />
    
    <!-- Application Settings -->
    <add key="ApplicationName" value="CWDECC 3S System" />
    <add key="ApplicationVersion" value="1.0.0" />
    <add key="SupportEmail" value="<EMAIL>" />
    
    <!-- Security Settings -->
    <add key="EncryptionMasterKey" value="PLEASE_GENERATE_AND_SET_MASTER_KEY" />
    <add key="RequireSSL" value="true" />
    <add key="SessionTimeout" value="30" />
    
    <!-- AES-256 Encryption Settings -->
    <add key="EncryptionAlgorithm" value="AES-256-CBC" />
    <add key="EncryptionKeyDerivation" value="PBKDF2-SHA256" />
    <add key="EncryptionIterations" value="100000" />
    <add key="EncryptionKeyVersion" value="1" />
    
    <!-- Security Headers -->
    <add key="EnableSecurityHeaders" value="true" />
    <add key="EnableHSTS" value="true" />
    <add key="HSTSMaxAge" value="31536000" />
    
    <!-- Data Protection -->
    <add key="EnableDataEncryption" value="true" />
    <add key="EncryptSensitiveFields" value="Name,HKID,Phone,Address" />
    <add key="EnableDataMasking" value="true" />
    
    <!-- Validation Settings -->
    <add key="PasswordMinLength" value="8" />
    <add key="PasswordRequireUppercase" value="true" />
    <add key="PasswordRequireLowercase" value="true" />
    <add key="PasswordRequireDigits" value="true" />
    <add key="PasswordRequireSpecialChars" value="true" />
    
    <!-- Email Settings (for future use) -->
    <add key="SMTPServer" value="smtp.gmail.com" />
    <add key="SMTPPort" value="587" />
    <add key="SMTPUsername" value="<EMAIL>" />
    <add key="SMTPPassword" value="your-email-password" />
    <add key="SMTPEnableSSL" value="true" />
    
    <!-- Cultural Settings -->
    <add key="DefaultCulture" value="zh-HK" />
    <add key="SupportedCultures" value="zh-HK,en-US" />
    
    <!-- Logging Settings -->
    <add key="LogLevel" value="Information" />
    <add key="LogToFile" value="true" />
    <add key="LogFilePath" value="~/App_Data/Logs/" />
    
    <!-- Performance Settings -->
    <add key="EnableCaching" value="true" />
    <add key="CacheTimeout" value="600" />
  </appSettings>
  
  <connectionStrings>
    <!-- MariaDB Connection String -->
    <add name="DefaultConnection" connectionString="Server=localhost;Database=CWDECC3S;Uid=root;Pwd=your_password;SslMode=none;CharSet=utf8mb4;" providerName="MySql.Data.MySqlClient" />
    
    <!-- Backup SQL Server Connection String (commented out) -->
    <!--<add name="DefaultConnection" connectionString="Data Source=(LocalDb)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\aspnet-CWDECC3S-20241201.mdf;Initial Catalog=aspnet-CWDECC3S-20241201;Integrated Security=True" providerName="System.Data.SqlClient" />-->
  </connectionStrings>
  
  <system.web>
    <!-- Compilation Settings -->
    <compilation debug="true" targetFramework="4.8" />
    
    <!-- HTTP Runtime Settings -->
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="300" />
    
    <!-- Globalization Settings -->
    <globalization culture="zh-HK" uiCulture="zh-HK" />
    
    <!-- Authentication -->
    <authentication mode="Forms">
      <forms loginUrl="~/Account/Login.aspx" timeout="30" name=".ASPXAUTH" requireSSL="true" cookieless="false" />
    </authentication>
    
    <!-- Authorization -->
    <authorization>
      <allow users="*" />
    </authorization>
    
    <!-- Session State -->
    <sessionState mode="InProc" cookieless="false" timeout="30" />
    
    <!-- Pages Settings -->
    <pages controlRenderingCompatibilityVersion="4.0" clientIDMode="AutoID">
      <namespaces>
        <add namespace="System.Web.Optimization" />
      </namespaces>
      <controls>
        <add assembly="Microsoft.AspNet.Web.Optimization.WebForms" namespace="Microsoft.AspNet.Web.Optimization.WebForms" tagPrefix="webopt" />
      </controls>
    </pages>
    
    <!-- Custom Errors -->
    <customErrors mode="Off" defaultRedirect="~/Error.aspx">
      <error statusCode="404" redirect="~/NotFound.aspx" />
      <error statusCode="500" redirect="~/Error.aspx" />
    </customErrors>
    
    <!-- HTTP Handlers -->
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" validate="false" />
    </httpHandlers>
    
    <!-- HTTP Modules -->
    <httpModules>
      <add name="ErrorHandlingModule" type="CWDECC_3S.Modules.ErrorHandlingModule" />
      <add name="SecurityModule" type="CWDECC_3S.Modules.SecurityModule" />
    </httpModules>
    
    <!-- Trust Level -->
    <trust level="Full" />
    
    <!-- Trace -->
    <trace enabled="false" pageOutput="false" requestLimit="40" localOnly="false" />
  </system.web>
  
  <system.webServer>
    <!-- Default Documents -->
    <defaultDocument>
      <files>
        <clear />
        <add value="Default.aspx" />
        <add value="default.htm" />
        <add value="index.html" />
      </files>
    </defaultDocument>
    
    <!-- Directory Browsing -->
    <directoryBrowse enabled="false" />
    
    <!-- HTTP Handlers -->
    <handlers>
      <add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
    </handlers>
    
    <!-- HTTP Modules -->
    <modules>
      <add name="ErrorHandlingModule" type="CWDECC_3S.Modules.ErrorHandlingModule" />
      <add name="SecurityModule" type="CWDECC_3S.Modules.SecurityModule" />
    </modules>
    
    <!-- Static Content -->
    <staticContent>
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
    </staticContent>
    
    <!-- HTTP Protocol -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
      </customHeaders>
    </httpProtocol>
    
    <!-- URL Rewrite (if module is installed) -->
    <!--
    <rewrite>
      <rules>
        <rule name="Redirect to HTTPS" stopProcessing="true">
          <match url=".*" />
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:0}" redirectType="Permanent" />
        </rule>
      </rules>
    </rewrite>
    -->
    
    <!-- Compression -->
    <httpCompression>
      <dynamicTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </staticTypes>
    </httpCompression>
    
    <!-- Validation -->
    <validation validateIntegratedModeConfiguration="false" />
  </system.webServer>
  
  <!-- Runtime -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" culture="neutral" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <!-- System.CodeDom -->
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
</configuration>