using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using CWDECC_3S.Services;

namespace CWDECC_3S.Admin
{
    public partial class RolePermissionTest : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                InitializePage();
            }
        }

        private void InitializePage()
        {
            UpdateCurrentStatus();
            GeneratePermissionMatrix();
            AnalyzeCurrentNavigation();
        }

        #region 角色模擬

        protected void btnSimulateLogin_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedRole = ddlTestRole.SelectedValue;
                var testUsername = txtTestUsername.Text.Trim();

                if (string.IsNullOrEmpty(testUsername))
                {
                    ShowMessage("請輸入測試用戶名", "warning");
                    return;
                }

                // 設定 Session 來模擬登入
                Session["UserRole"] = selectedRole;
                Session["Username"] = testUsername;
                Session["DisplayName"] = GetDisplayNameForRole(selectedRole);
                Session["IsAuthenticated"] = true;

                // 使用 PermissionService 設定角色
                PermissionService.SetUserRole(testUsername, selectedRole);

                ShowMessage($"模擬登入成功！角色：{GetRoleDisplayName(selectedRole)}", "success");
                
                UpdateCurrentStatus();
                AnalyzeCurrentNavigation();
                
                LogTestEvent($"模擬登入: {testUsername} - {selectedRole}");
            }
            catch (Exception ex)
            {
                ShowMessage($"模擬登入失敗: {ex.Message}", "danger");
                LogTestEvent($"模擬登入失敗: {ex.Message}");
            }
        }

        protected void btnSimulateLogout_Click(object sender, EventArgs e)
        {
            try
            {
                // 清除 Session
                Session.Remove("UserRole");
                Session.Remove("Username");
                Session.Remove("DisplayName");
                Session.Remove("IsAuthenticated");

                // 清除 PermissionService
                PermissionService.ClearUserRole();

                ShowMessage("模擬登出成功！", "info");
                
                UpdateCurrentStatus();
                AnalyzeCurrentNavigation();
                
                LogTestEvent("模擬登出成功");
            }
            catch (Exception ex)
            {
                ShowMessage($"模擬登出失敗: {ex.Message}", "danger");
                LogTestEvent($"模擬登出失敗: {ex.Message}");
            }
        }

        protected void btnRefreshNavigation_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateCurrentStatus();
                AnalyzeCurrentNavigation();
                ShowMessage("導航選單已重新整理", "info");
                LogTestEvent("導航選單重新整理");
            }
            catch (Exception ex)
            {
                ShowMessage($"重新整理失敗: {ex.Message}", "danger");
            }
        }

        #endregion

        #region 狀態顯示

        private void UpdateCurrentStatus()
        {
            try
            {
                var userInfo = PermissionService.GetUserDisplayInfo();
                var result = new StringBuilder();

                result.AppendLine("<h5>👤 當前用戶狀態</h5>");
                
                if (userInfo.IsAuthenticated)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 已登入</strong><br/>");
                    result.AppendLine($"<strong>用戶名:</strong> {userInfo.Username}<br/>");
                    result.AppendLine($"<strong>顯示名:</strong> {userInfo.DisplayName}<br/>");
                    result.AppendLine($"<strong>角色:</strong> <span class='role-badge role-{userInfo.Role.ToLower()}'>{userInfo.RoleDisplayName}</span><br/>");
                    result.AppendLine($"<strong>Session ID:</strong> {Session.SessionID}");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-warning'>");
                    result.AppendLine("<strong>⚠️ 未登入</strong><br/>");
                    result.AppendLine("請選擇角色進行模擬登入測試");
                    result.AppendLine("</div>");
                }

                // 顯示權限數量
                if (userInfo.IsAuthenticated)
                {
                    var userNavigation = PermissionService.GetUserNavigation();
                    result.AppendLine($"<div class='alert alert-info'>");
                    result.AppendLine($"<strong>📊 權限統計:</strong><br/>");
                    result.AppendLine($"可見導航項目: {userNavigation.Count} 個<br/>");
                    
                    int totalChildItems = 0;
                    foreach (var item in userNavigation)
                    {
                        totalChildItems += item.Children.Count;
                    }
                    result.AppendLine($"子選單項目: {totalChildItems} 個");
                    result.AppendLine("</div>");
                }

                ltlCurrentStatus.Text = result.ToString();
            }
            catch (Exception ex)
            {
                ltlCurrentStatus.Text = $"<div class='alert alert-danger'>狀態更新失敗: {ex.Message}</div>";
            }
        }

        #endregion

        #region 權限矩陣

        private void GeneratePermissionMatrix()
        {
            try
            {
                var tbody = new HtmlGenericControl("tbody");
                
                // 定義所有權限
                var permissions = new Dictionary<string, string>
                {
                    { PermissionService.Permissions.ViewDashboard, "檢視儀表板" },
                    { PermissionService.Permissions.ViewMembers, "檢視會員" },
                    { PermissionService.Permissions.ManageMembers, "管理會員" },
                    { PermissionService.Permissions.ViewActivities, "檢視活動" },
                    { PermissionService.Permissions.ManageActivities, "管理活動" },
                    { PermissionService.Permissions.ViewReports, "檢視報告" },
                    { PermissionService.Permissions.ManageSystem, "系統管理" },
                    { PermissionService.Permissions.ManageUsers, "用戶管理" },
                    { PermissionService.Permissions.ViewFirebase, "Firebase 測試" },
                    { PermissionService.Permissions.ViewSecurity, "安全測試" }
                };

                // 定義所有角色
                var roles = new[]
                {
                    PermissionService.Roles.Administrator,
                    PermissionService.Roles.StaffMember,
                    PermissionService.Roles.Teacher,
                    PermissionService.Roles.Volunteer,
                    PermissionService.Roles.Member,
                    PermissionService.Roles.Guest
                };

                // 生成權限矩陣
                foreach (var permission in permissions)
                {
                    var row = new HtmlGenericControl("tr");
                    
                    // 權限名稱
                    var nameCell = new HtmlGenericControl("td");
                    nameCell.InnerText = permission.Value;
                    row.Controls.Add(nameCell);

                    // 各角色的權限狀態
                    foreach (var role in roles)
                    {
                        var cell = new HtmlGenericControl("td");
                        cell.Attributes["class"] = "text-center";
                        
                        var hasPermission = PermissionService.HasRolePermission(role, permission.Key);
                        
                        if (hasPermission)
                        {
                            cell.InnerHtml = "<i class='fas fa-check text-success'></i>";
                            cell.Attributes["title"] = "有權限";
                        }
                        else
                        {
                            cell.InnerHtml = "<i class='fas fa-times text-danger'></i>";
                            cell.Attributes["title"] = "無權限";
                        }
                        
                        row.Controls.Add(cell);
                    }
                    
                    tbody.Controls.Add(row);
                }

                // 清空並添加到表格
                permissionMatrix.Controls.Clear();
                
                // 重新建立表格結構
                var thead = new HtmlGenericControl("thead");
                var headerRow = new HtmlGenericControl("tr");
                headerRow.Attributes["class"] = "active";
                
                var headers = new[] { "權限項目", "管理員", "職員", "導師", "義工", "會員", "訪客" };
                foreach (var header in headers)
                {
                    var th = new HtmlGenericControl("th");
                    if (header != "權限項目")
                    {
                        th.Attributes["class"] = "text-center";
                    }
                    th.InnerText = header;
                    headerRow.Controls.Add(th);
                }
                
                thead.Controls.Add(headerRow);
                permissionMatrix.Controls.Add(thead);
                permissionMatrix.Controls.Add(tbody);
            }
            catch (Exception ex)
            {
                var errorRow = new HtmlGenericControl("tr");
                var errorCell = new HtmlGenericControl("td");
                errorCell.Attributes["colspan"] = "7";
                errorCell.InnerText = $"權限矩陣生成失敗: {ex.Message}";
                errorRow.Controls.Add(errorCell);
                
                var tbody = new HtmlGenericControl("tbody");
                tbody.Controls.Add(errorRow);
                permissionMatrix.Controls.Add(tbody);
            }
        }

        #endregion

        #region 導航分析

        private void AnalyzeCurrentNavigation()
        {
            try
            {
                var userNavigation = PermissionService.GetUserNavigation();
                var allNavigationItems = GetAllPossibleNavigation();

                // 可見項目
                var visibleResult = new StringBuilder();
                visibleResult.AppendLine("<h5>✅ 可見導航項目</h5>");
                
                if (userNavigation.Count > 0)
                {
                    visibleResult.AppendLine("<ul class='list-group'>");
                    foreach (var item in userNavigation)
                    {
                        visibleResult.AppendLine($"<li class='list-group-item'>");
                        visibleResult.AppendLine($"<i class='{item.Icon} me-2'></i><strong>{item.Text}</strong>");
                        
                        if (item.Children.Count > 0)
                        {
                            visibleResult.AppendLine("<ul class='mt-2'>");
                            foreach (var child in item.Children)
                            {
                                visibleResult.AppendLine($"<li><i class='fas fa-chevron-right me-1'></i>{child.Text}</li>");
                            }
                            visibleResult.AppendLine("</ul>");
                        }
                        visibleResult.AppendLine("</li>");
                    }
                    visibleResult.AppendLine("</ul>");
                }
                else
                {
                    visibleResult.AppendLine("<div class='alert alert-warning'>沒有可見的導航項目</div>");
                }

                // 被隱藏的項目
                var hiddenResult = new StringBuilder();
                hiddenResult.AppendLine("<h5>❌ 被隱藏的項目</h5>");
                
                var hiddenItems = GetHiddenNavigationItems(userNavigation, allNavigationItems);
                if (hiddenItems.Count > 0)
                {
                    hiddenResult.AppendLine("<ul class='list-group'>");
                    foreach (var item in hiddenItems)
                    {
                        hiddenResult.AppendLine($"<li class='list-group-item text-muted'>");
                        hiddenResult.AppendLine($"<i class='{item.Icon} me-2'></i><s>{item.Text}</s>");
                        hiddenResult.AppendLine($"<small class='text-muted'> (需要權限: {item.Permission})</small>");
                        hiddenResult.AppendLine("</li>");
                    }
                    hiddenResult.AppendLine("</ul>");
                }
                else
                {
                    hiddenResult.AppendLine("<div class='alert alert-success'>沒有被隱藏的項目</div>");
                }

                ltlVisibleNavigation.Text = visibleResult.ToString();
                ltlHiddenNavigation.Text = hiddenResult.ToString();
            }
            catch (Exception ex)
            {
                ltlVisibleNavigation.Text = $"<div class='alert alert-danger'>導航分析失敗: {ex.Message}</div>";
                ltlHiddenNavigation.Text = $"<div class='alert alert-danger'>導航分析失敗: {ex.Message}</div>";
            }
        }

        private List<PermissionService.NavigationItem> GetAllPossibleNavigation()
        {
            // 返回所有可能的導航項目（不考慮權限）
            return new List<PermissionService.NavigationItem>
            {
                new PermissionService.NavigationItem
                {
                    Text = "儀表板",
                    Icon = "fas fa-tachometer-alt",
                    Permission = PermissionService.Permissions.ViewDashboard
                },
                new PermissionService.NavigationItem
                {
                    Text = "會員管理",
                    Icon = "fas fa-users",
                    Permission = PermissionService.Permissions.ViewMembers
                },
                new PermissionService.NavigationItem
                {
                    Text = "活動管理",
                    Icon = "fas fa-calendar-alt",
                    Permission = PermissionService.Permissions.ViewActivities
                },
                new PermissionService.NavigationItem
                {
                    Text = "系統管理",
                    Icon = "fas fa-cog",
                    Permission = PermissionService.Permissions.ManageSystem
                }
            };
        }

        private List<PermissionService.NavigationItem> GetHiddenNavigationItems(
            List<PermissionService.NavigationItem> visibleItems,
            List<PermissionService.NavigationItem> allItems)
        {
            var hiddenItems = new List<PermissionService.NavigationItem>();
            
            foreach (var allItem in allItems)
            {
                var isVisible = visibleItems.Any(v => v.Permission == allItem.Permission);
                if (!isVisible)
                {
                    hiddenItems.Add(allItem);
                }
            }
            
            return hiddenItems;
        }

        #endregion

        #region 輔助方法

        private string GetDisplayNameForRole(string role)
        {
            var displayNames = new Dictionary<string, string>
            {
                { PermissionService.Roles.Administrator, "系統管理員" },
                { PermissionService.Roles.StaffMember, "職員" },
                { PermissionService.Roles.Teacher, "導師" },
                { PermissionService.Roles.Volunteer, "義工" },
                { PermissionService.Roles.Member, "會員" },
                { PermissionService.Roles.Guest, "訪客" }
            };

            return displayNames.ContainsKey(role) ? displayNames[role] : role;
        }

        private string GetRoleDisplayName(string role)
        {
            return GetDisplayNameForRole(role);
        }

        private void ShowMessage(string message, string type)
        {
            var alertClass = $"alert-{type}";
            var alertHtml = $"<div class='alert {alertClass} alert-dismissible fade show' role='alert'>" +
                           $"{message}" +
                           $"<button type='button' class='close' data-dismiss='alert' aria-label='Close'>" +
                           $"<span aria-hidden='true'>&times;</span></button></div>";

            // 在頁面頂部顯示訊息
            var messagePlaceholder = FindControl("messagePlaceholder") as Literal;
            if (messagePlaceholder == null)
            {
                Response.Write($"<script>$(document).ready(function(){{ $('body').prepend('{alertHtml.Replace("'", "\\'")}'); }});</script>");
            }
        }

        private void LogTestEvent(string message)
        {
            try
            {
                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [ROLE_TEST] {message}";
                var logPath = Server.MapPath("~/App_Data/Logs/role_test.log");
                
                System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(logPath));
                System.IO.File.AppendAllText(logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // 記錄失敗不應影響主要功能
            }
        }

        #endregion
    }
}