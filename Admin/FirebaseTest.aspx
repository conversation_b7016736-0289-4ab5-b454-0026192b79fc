<%@ Page Title="Firebase 連接測試" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="FirebaseTest.aspx.cs" Inherits="CWDECC_3S.Admin.FirebaseTest" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">

    <div class="row">
        <div class="col-md-12">
            <h2>🔥 Firebase 連接與 CRUD 測試</h2>
            <p class="lead">測試 Firestore 和 Authentication 連接，驗證 CRUD 操作功能</p>
        </div>
    </div>

    <!-- 連接狀態檢查 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">🔗 連接狀態檢查</h3>
                </div>
                <div class="panel-body">
                    <asp:Button ID="btnTestConnection" runat="server" 
                        Text="測試 Firebase 連接" 
                        CssClass="btn btn-primary" 
                        OnClick="btnTestConnection_Click" />
                    
                    <asp:Button ID="btnTestCredentials" runat="server" 
                        Text="測試憑證配置" 
                        CssClass="btn btn-info" 
                        OnClick="btnTestCredentials_Click" />
                    
                    <hr />
                    <asp:Panel ID="pnlConnectionStatus" runat="server" CssClass="well">
                        <asp:Literal ID="ltlConnectionStatus" runat="server"></asp:Literal>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>

    <!-- CRUD 操作測試 -->
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h3 class="panel-title">📝 CRUD 操作測試</h3>
                </div>
                <div class="panel-body">
                    <h4>測試資料輸入</h4>
                    <div class="form-group">
                        <label>文件 ID:</label>
                        <asp:TextBox ID="txtDocumentId" runat="server" CssClass="form-control" 
                            Text="test_doc_001"></asp:TextBox>
                    </div>
                    <div class="form-group">
                        <label>測試內容:</label>
                        <asp:TextBox ID="txtTestContent" runat="server" CssClass="form-control" 
                            TextMode="MultiLine" Rows="3"
                            Text="測試數據 - Firebase CRUD 操作驗證"></asp:TextBox>
                    </div>
                    
                    <div class="btn-group-vertical btn-block">
                        <asp:Button ID="btnAddDocument" runat="server" 
                            Text="新增文件 (AddDocument)" 
                            CssClass="btn btn-success" 
                            OnClick="btnAddDocument_Click" />
                        
                        <asp:Button ID="btnGetDocument" runat="server" 
                            Text="取得文件 (GetDocument)" 
                            CssClass="btn btn-info" 
                            OnClick="btnGetDocument_Click" />
                        
                        <asp:Button ID="btnUpdateDocument" runat="server" 
                            Text="更新文件 (UpdateDocument)" 
                            CssClass="btn btn-warning" 
                            OnClick="btnUpdateDocument_Click" />
                        
                        <asp:Button ID="btnDeleteDocument" runat="server" 
                            Text="刪除文件 (DeleteDocument)" 
                            CssClass="btn btn-danger" 
                            OnClick="btnDeleteDocument_Click" />
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">📊 操作結果</h3>
                </div>
                <div class="panel-body">
                    <asp:Panel ID="pnlOperationResults" runat="server" CssClass="well" style="min-height: 300px;">
                        <asp:Literal ID="ltlOperationResults" runat="server"></asp:Literal>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>

    <!-- 集合操作測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">📚 集合操作測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h4>批量操作</h4>
                            <asp:Button ID="btnCreateTestData" runat="server" 
                                Text="建立測試數據" 
                                CssClass="btn btn-primary btn-block" 
                                OnClick="btnCreateTestData_Click" />
                            
                            <asp:Button ID="btnGetCollection" runat="server" 
                                Text="取得整個集合 (GetCollection)" 
                                CssClass="btn btn-info btn-block" 
                                OnClick="btnGetCollection_Click" />
                            
                            <asp:Button ID="btnClearTestData" runat="server" 
                                Text="清除測試數據" 
                                CssClass="btn btn-danger btn-block" 
                                OnClick="btnClearTestData_Click" 
                                OnClientClick="return confirm('確定要清除所有測試數據嗎？');" />
                        </div>
                        
                        <div class="col-md-8">
                            <h4>集合內容</h4>
                            <asp:Panel ID="pnlCollectionData" runat="server" CssClass="well" style="max-height: 400px; overflow-y: auto;">
                                <asp:Literal ID="ltlCollectionData" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Authentication 測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">🔐 Authentication 測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>用戶驗證測試</h4>
                            <div class="form-group">
                                <label>模擬 ID Token:</label>
                                <asp:TextBox ID="txtIdToken" runat="server" CssClass="form-control" 
                                    TextMode="MultiLine" Rows="3"
                                    placeholder="輸入 Firebase ID Token 進行驗證測試..."></asp:TextBox>
                            </div>
                            
                            <asp:Button ID="btnTestAuthentication" runat="server" 
                                Text="測試用戶驗證 (IsAuthenticated)" 
                                CssClass="btn btn-warning" 
                                OnClick="btnTestAuthentication_Click" />
                            
                            <asp:Button ID="btnGetUserInfo" runat="server" 
                                Text="取得用戶資訊" 
                                CssClass="btn btn-info" 
                                OnClick="btnGetUserInfo_Click" />
                        </div>
                        
                        <div class="col-md-6">
                            <h4>驗證結果</h4>
                            <asp:Panel ID="pnlAuthResults" runat="server" CssClass="well">
                                <asp:Literal ID="ltlAuthResults" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <strong>提示：</strong> 
                        <ul>
                            <li>空的 ID Token 將測試未登入狀態</li>
                            <li>無效的 Token 將測試錯誤處理</li>
                            <li>有效的 Token 需要從 Firebase Auth 取得</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 錯誤處理測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-danger">
                <div class="panel-heading">
                    <h3 class="panel-title">⚠️ 錯誤處理測試</h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-warning">
                        <strong>注意：</strong> 這些測試將故意觸發錯誤以驗證錯誤處理機制。
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h4>錯誤場景測試</h4>
                            <asp:Button ID="btnTestInvalidCollection" runat="server" 
                                Text="測試無效集合名稱" 
                                CssClass="btn btn-danger btn-block" 
                                OnClick="btnTestInvalidCollection_Click" />
                            
                            <asp:Button ID="btnTestNonExistentDoc" runat="server" 
                                Text="測試不存在的文件" 
                                CssClass="btn btn-danger btn-block" 
                                OnClick="btnTestNonExistentDoc_Click" />
                            
                            <asp:Button ID="btnTestDuplicateAdd" runat="server" 
                                Text="測試重複新增文件" 
                                CssClass="btn btn-danger btn-block" 
                                OnClick="btnTestDuplicateAdd_Click" />
                            
                            <asp:Button ID="btnTestCredentialsError" runat="server" 
                                Text="模擬憑證錯誤" 
                                CssClass="btn btn-danger btn-block" 
                                OnClick="btnTestCredentialsError_Click" />
                        </div>
                        
                        <div class="col-md-6">
                            <h4>錯誤處理結果</h4>
                            <asp:Panel ID="pnlErrorResults" runat="server" CssClass="well">
                                <asp:Literal ID="ltlErrorResults" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">⚡ 性能測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h4>批量操作性能</h4>
                            <div class="form-group">
                                <label>測試數據量:</label>
                                <asp:DropDownList ID="ddlTestDataSize" runat="server" CssClass="form-control">
                                    <asp:ListItem Text="10 筆" Value="10"></asp:ListItem>
                                    <asp:ListItem Text="50 筆" Value="50" Selected="True"></asp:ListItem>
                                    <asp:ListItem Text="100 筆" Value="100"></asp:ListItem>
                                    <asp:ListItem Text="200 筆" Value="200"></asp:ListItem>
                                </asp:DropDownList>
                            </div>
                            
                            <asp:Button ID="btnRunPerformanceTest" runat="server" 
                                Text="執行性能測試" 
                                CssClass="btn btn-default btn-block" 
                                OnClick="btnRunPerformanceTest_Click" />
                        </div>
                        
                        <div class="col-md-8">
                            <h4>性能測試結果</h4>
                            <asp:Panel ID="pnlPerformanceResults" runat="server" CssClass="well">
                                <asp:Literal ID="ltlPerformanceResults" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        // 自動重新整理結果面板
        function refreshResults() {
            location.reload();
        }
        
        // 清除結果面板
        function clearResults() {
            var panels = document.querySelectorAll('.well');
            panels.forEach(function(panel) {
                if (panel.innerHTML.trim() !== '') {
                    panel.innerHTML = '<em>等待測試結果...</em>';
                }
            });
        }
        
        // 頁面載入完成
        $(document).ready(function() {
            // 初始化工具提示
            $('[data-toggle="tooltip"]').tooltip();
            
            // 為按鈕添加載入狀態
            $('input[type="submit"]').click(function() {
                var btn = $(this);
                var originalText = btn.val();
                btn.val('處理中...').prop('disabled', true);
                
                setTimeout(function() {
                    btn.val(originalText).prop('disabled', false);
                }, 10000); // 10秒後恢復
            });
        });
    </script>

</asp:Content>