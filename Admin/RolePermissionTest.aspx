<%@ Page Title="角色權限測試" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="RolePermissionTest.aspx.cs" Inherits="CWDECC_3S.Admin.RolePermissionTest" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">

    <div class="row">
        <div class="col-md-12">
            <h2>🔒 角色權限與導航測試</h2>
            <p class="lead">測試不同角色的權限分配和導航選單顯示功能</p>
        </div>
    </div>

    <!-- 角色切換測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">🎭 角色切換模擬</h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-info">
                        <strong>測試說明：</strong> 選擇不同角色來模擬用戶登入，觀察導航選單的變化
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h4>選擇測試角色</h4>
                            <div class="form-group">
                                <label>用戶角色:</label>
                                <asp:DropDownList ID="ddlTestRole" runat="server" CssClass="form-control">
                                    <asp:ListItem Text="系統管理員 (Administrator)" Value="Administrator"></asp:ListItem>
                                    <asp:ListItem Text="職員 (StaffMember)" Value="StaffMember"></asp:ListItem>
                                    <asp:ListItem Text="導師 (Teacher)" Value="Teacher"></asp:ListItem>
                                    <asp:ListItem Text="義工 (Volunteer)" Value="Volunteer"></asp:ListItem>
                                    <asp:ListItem Text="會員 (Member)" Value="Member"></asp:ListItem>
                                    <asp:ListItem Text="訪客 (Guest)" Value="Guest"></asp:ListItem>
                                </asp:DropDownList>
                            </div>

                            <div class="form-group">
                                <label>模擬用戶名:</label>
                                <asp:TextBox ID="txtTestUsername" runat="server" CssClass="form-control" 
                                    Text="<EMAIL>"></asp:TextBox>
                            </div>

                            <div class="btn-group">
                                <asp:Button ID="btnSimulateLogin" runat="server" 
                                    Text="模擬登入" 
                                    CssClass="btn btn-primary" 
                                    OnClick="btnSimulateLogin_Click" />

                                <asp:Button ID="btnSimulateLogout" runat="server" 
                                    Text="模擬登出" 
                                    CssClass="btn btn-secondary" 
                                    OnClick="btnSimulateLogout_Click" />

                                <asp:Button ID="btnRefreshNavigation" runat="server" 
                                    Text="重新整理導航" 
                                    CssClass="btn btn-info" 
                                    OnClick="btnRefreshNavigation_Click" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h4>當前狀態</h4>
                            <asp:Panel ID="pnlCurrentStatus" runat="server" CssClass="well">
                                <asp:Literal ID="ltlCurrentStatus" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 權限測試矩陣 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h3 class="panel-title">📊 權限測試矩陣</h3>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="permissionMatrix" runat="server">
                            <thead>
                                <tr class="active">
                                    <th>權限項目</th>
                                    <th class="text-center">管理員</th>
                                    <th class="text-center">職員</th>
                                    <th class="text-center">導師</th>
                                    <th class="text-center">義工</th>
                                    <th class="text-center">會員</th>
                                    <th class="text-center">訪客</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 動態生成權限矩陣 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 導航選單分析 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">🧭 當前導航選單分析</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>可見導航項目</h4>
                            <asp:Panel ID="pnlVisibleNavigation" runat="server" CssClass="well" style="min-height: 200px;">
                                <asp:Literal ID="ltlVisibleNavigation" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>

                        <div class="col-md-6">
                            <h4>被隱藏的項目</h4>
                            <asp:Panel ID="pnlHiddenNavigation" runat="server" CssClass="well" style="min-height: 200px;">
                                <asp:Literal ID="ltlHiddenNavigation" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 響應式測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">📱 響應式設計測試</h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-warning">
                        <strong>RWD 測試指南：</strong>
                        <ul class="mb-0">
                            <li>調整瀏覽器視窗大小測試響應式效果</li>
                            <li>在手機/平板瀏覽器中打開此頁面</li>
                            <li>測試導航選單在不同螢幕尺寸下的行為</li>
                            <li>驗證下拉選單在移動端的可用性</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5>🖥️ 桌面端</h5>
                                </div>
                                <div class="panel-body text-center">
                                    <i class="fas fa-desktop fa-3x text-primary"></i>
                                    <p class="mt-2">≥ 992px</p>
                                    <small class="text-muted">完整導航選單</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5>💻 平板端</h5>
                                </div>
                                <div class="panel-body text-center">
                                    <i class="fas fa-tablet-alt fa-3x text-info"></i>
                                    <p class="mt-2">768px - 991px</p>
                                    <small class="text-muted">收縮式導航</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5>📱 手機端</h5>
                                </div>
                                <div class="panel-body text-center">
                                    <i class="fas fa-mobile-alt fa-3x text-success"></i>
                                    <p class="mt-2">< 768px</p>
                                    <small class="text-muted">漢堡選單</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6 col-xs-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5>⌚ 極小螢幕</h5>
                                </div>
                                <div class="panel-body text-center">
                                    <i class="fas fa-mobile fa-3x text-warning"></i>
                                    <p class="mt-2">< 480px</p>
                                    <small class="text-muted">極簡設計</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <asp:Button ID="btnTestResponsive" runat="server" 
                            Text="測試當前視窗資訊" 
                            CssClass="btn btn-warning" 
                            OnClientClick="testViewportInfo(); return false;" />
                    </div>

                    <div id="viewportInfo" class="alert alert-info mt-3" style="display: none;">
                        <!-- JavaScript 將在此顯示視窗資訊 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 測試功能 -->
    <script type="text/javascript">
        function testViewportInfo() {
            var viewportWidth = window.innerWidth || document.documentElement.clientWidth;
            var viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            var devicePixelRatio = window.devicePixelRatio || 1;
            var userAgent = navigator.userAgent;
            
            var deviceType = "";
            if (viewportWidth >= 992) {
                deviceType = "🖥️ 桌面端";
            } else if (viewportWidth >= 768) {
                deviceType = "💻 平板端";
            } else if (viewportWidth >= 480) {
                deviceType = "📱 手機端";
            } else {
                deviceType = "⌚ 極小螢幕";
            }
            
            var info = `
                <h5>📏 視窗資訊</h5>
                <ul>
                    <li><strong>視窗寬度:</strong> ${viewportWidth}px</li>
                    <li><strong>視窗高度:</strong> ${viewportHeight}px</li>
                    <li><strong>設備類型:</strong> ${deviceType}</li>
                    <li><strong>像素比例:</strong> ${devicePixelRatio}</li>
                    <li><strong>用戶代理:</strong> ${userAgent.substring(0, 100)}...</li>
                </ul>
            `;
            
            document.getElementById('viewportInfo').innerHTML = info;
            document.getElementById('viewportInfo').style.display = 'block';
        }

        // 視窗大小變化監聽
        window.addEventListener('resize', function() {
            var viewportInfo = document.getElementById('viewportInfo');
            if (viewportInfo.style.display !== 'none') {
                testViewportInfo();
            }
        });

        // 頁面載入完成後執行
        $(document).ready(function() {
            // 初始化權限測試
            initializePermissionTest();
            
            // 測試導航選單響應式行為
            testNavigationResponsive();
        });

        function initializePermissionTest() {
            console.log('權限測試頁面已初始化');
            
            // 檢查當前用戶狀態
            var userInfo = {
                isAuthenticated: '<%= Request.IsAuthenticated %>',
                userName: '<%= Context.User?.Identity?.Name ?? "未登入" %>'
            };
            
            console.log('當前用戶狀態:', userInfo);
        }

        function testNavigationResponsive() {
            // 測試導航選單的響應式行為
            var navbar = $('.navbar-collapse');
            var toggleButton = $('.navbar-toggler');
            
            console.log('導航選單元素:', {
                navbar: navbar.length,
                toggleButton: toggleButton.length,
                isCollapsed: !navbar.hasClass('show')
            });
        }
    </script>

</asp:Content>