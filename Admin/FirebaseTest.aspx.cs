using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI;
using CWDECC_3S.Data;

namespace CWDECC_3S.Admin
{
    public partial class FirebaseTest : Page
    {
        private const string TEST_COLLECTION = "test_collection";
        private EnhancedFirebaseService firebaseService;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                InitializePage();
            }
        }

        private void InitializePage()
        {
            try
            {
                // 初始化 Firebase 服務
                firebaseService = new EnhancedFirebaseService();
                
                ltlConnectionStatus.Text = "<em>等待連接測試...</em>";
                ltlOperationResults.Text = "<em>等待 CRUD 操作...</em>";
                ltlCollectionData.Text = "<em>等待集合操作...</em>";
                ltlAuthResults.Text = "<em>等待驗證測試...</em>";
                ltlErrorResults.Text = "<em>等待錯誤測試...</em>";
                ltlPerformanceResults.Text = "<em>等待性能測試...</em>";
            }
            catch (Exception ex)
            {
                ShowError($"頁面初始化失敗: {ex.Message}", ltlConnectionStatus);
            }
        }

        #region 連接測試

        protected async void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                var status = await firebaseService.TestConnection();
                
                var result = new StringBuilder();
                result.AppendLine("<h5>🔗 Firebase 連接測試結果</h5>");
                
                var alertClass = status.IsConnected ? "alert-success" : "alert-danger";
                result.AppendLine($"<div class='alert {alertClass}'>");
                result.AppendLine($"<strong>整體狀態:</strong> {(status.IsConnected ? "✅ 連接成功" : "❌ 連接失敗")}<br/>");
                result.AppendLine($"<strong>Firestore:</strong> {(status.FirestoreConnected ? "✅ 正常" : "❌ 失敗")}<br/>");
                result.AppendLine($"<strong>Authentication:</strong> {(status.AuthConnected ? "✅ 正常" : "❌ 失敗")}<br/>");
                result.AppendLine($"<strong>訊息:</strong> {status.Message}");
                
                if (status.Error != null)
                {
                    result.AppendLine($"<br/><strong>錯誤詳情:</strong> {status.Error.Message}");
                }
                
                result.AppendLine("</div>");
                
                ltlConnectionStatus.Text = result.ToString();
                LogTestEvent($"連接測試: {(status.IsConnected ? "成功" : "失敗")}");
            }
            catch (Exception ex)
            {
                ShowError($"連接測試失敗: {ex.Message}", ltlConnectionStatus);
            }
        }

        protected void btnTestCredentials_Click(object sender, EventArgs e)
        {
            try
            {
                var result = new StringBuilder();
                result.AppendLine("<h5>🔑 憑證配置測試</h5>");
                
                try
                {
                    firebaseService = new EnhancedFirebaseService();
                    
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine("<strong>✅ 憑證配置正確</strong><br/>");
                    result.AppendLine("Service Account 憑證檔案已找到並驗證通過<br/>");
                    result.AppendLine("Firebase 專案 ID 配置正確");
                    result.AppendLine("</div>");
                }
                catch (FirebaseCredentialsException ex)
                {
                    result.AppendLine("<div class='alert alert-danger'>");
                    result.AppendLine($"<strong>❌ 憑證錯誤</strong><br/>");
                    result.AppendLine($"錯誤詳情: {ex.Message}");
                    result.AppendLine("</div>");
                }
                catch (FileNotFoundException ex)
                {
                    result.AppendLine("<div class='alert alert-warning'>");
                    result.AppendLine($"<strong>⚠️ 憑證檔案未找到</strong><br/>");
                    result.AppendLine($"請確認 Service Account 憑證檔案路徑: {ex.Message}");
                    result.AppendLine("</div>");
                }
                
                ltlConnectionStatus.Text = result.ToString();
            }
            catch (Exception ex)
            {
                ShowError($"憑證測試失敗: {ex.Message}", ltlConnectionStatus);
            }
        }

        #endregion

        #region CRUD 操作測試

        protected async void btnAddDocument_Click(object sender, EventArgs e)
        {
            try
            {
                string docId = txtDocumentId.Text.Trim();
                string content = txtTestContent.Text.Trim();
                
                if (string.IsNullOrEmpty(docId) || string.IsNullOrEmpty(content))
                {
                    ShowError("請輸入文件 ID 和測試內容", ltlOperationResults);
                    return;
                }

                var testData = new
                {
                    id = docId,
                    content = content,
                    timestamp = DateTime.UtcNow,
                    operation = "AddDocument",
                    testType = "CRUD"
                };

                firebaseService = new EnhancedFirebaseService();
                bool success = await firebaseService.AddDocument(TEST_COLLECTION, docId, testData);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>📝 AddDocument 操作結果</h5>");
                
                if (success)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 文件新增成功</strong><br/>");
                    result.AppendLine($"集合: {TEST_COLLECTION}<br/>");
                    result.AppendLine($"文件 ID: {docId}<br/>");
                    result.AppendLine($"內容: {content}<br/>");
                    result.AppendLine($"時間戳: {testData.timestamp:yyyy-MM-dd HH:mm:ss} UTC");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-danger'>");
                    result.AppendLine("<strong>❌ 文件新增失敗</strong>");
                    result.AppendLine("</div>");
                }

                ltlOperationResults.Text = result.ToString();
                LogTestEvent($"AddDocument: {docId} - {(success ? "成功" : "失敗")}");
            }
            catch (FirebaseOperationException ex)
            {
                ShowError($"AddDocument 操作錯誤: {ex.Message}", ltlOperationResults);
            }
            catch (Exception ex)
            {
                ShowError($"AddDocument 失敗: {ex.Message}", ltlOperationResults);
            }
        }

        protected async void btnGetDocument_Click(object sender, EventArgs e)
        {
            try
            {
                string docId = txtDocumentId.Text.Trim();
                
                if (string.IsNullOrEmpty(docId))
                {
                    ShowError("請輸入文件 ID", ltlOperationResults);
                    return;
                }

                firebaseService = new EnhancedFirebaseService();
                var document = await firebaseService.GetDocument<dynamic>(TEST_COLLECTION, docId);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>📖 GetDocument 操作結果</h5>");
                
                if (document != null)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 文件讀取成功</strong><br/>");
                    result.AppendLine($"集合: {TEST_COLLECTION}<br/>");
                    result.AppendLine($"文件 ID: {docId}<br/>");
                    result.AppendLine($"文件內容:<br/>");
                    result.AppendLine($"<pre>{Newtonsoft.Json.JsonConvert.SerializeObject(document, Newtonsoft.Json.Formatting.Indented)}</pre>");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-warning'>");
                    result.AppendLine($"<strong>⚠️ 文件不存在</strong><br/>");
                    result.AppendLine($"找不到 ID 為 '{docId}' 的文件");
                    result.AppendLine("</div>");
                }

                ltlOperationResults.Text = result.ToString();
                LogTestEvent($"GetDocument: {docId} - {(document != null ? "成功" : "文件不存在")}");
            }
            catch (Exception ex)
            {
                ShowError($"GetDocument 失敗: {ex.Message}", ltlOperationResults);
            }
        }

        protected async void btnUpdateDocument_Click(object sender, EventArgs e)
        {
            try
            {
                string docId = txtDocumentId.Text.Trim();
                string content = txtTestContent.Text.Trim();
                
                if (string.IsNullOrEmpty(docId) || string.IsNullOrEmpty(content))
                {
                    ShowError("請輸入文件 ID 和測試內容", ltlOperationResults);
                    return;
                }

                var updateData = new
                {
                    id = docId,
                    content = content + " (已更新)",
                    timestamp = DateTime.UtcNow,
                    operation = "UpdateDocument",
                    lastModified = DateTime.UtcNow,
                    testType = "CRUD"
                };

                firebaseService = new EnhancedFirebaseService();
                bool success = await firebaseService.UpdateDocument(TEST_COLLECTION, docId, updateData);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>✏️ UpdateDocument 操作結果</h5>");
                
                if (success)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 文件更新成功</strong><br/>");
                    result.AppendLine($"集合: {TEST_COLLECTION}<br/>");
                    result.AppendLine($"文件 ID: {docId}<br/>");
                    result.AppendLine($"更新內容: {updateData.content}<br/>");
                    result.AppendLine($"更新時間: {updateData.lastModified:yyyy-MM-dd HH:mm:ss} UTC");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-danger'>");
                    result.AppendLine("<strong>❌ 文件更新失敗</strong>");
                    result.AppendLine("</div>");
                }

                ltlOperationResults.Text = result.ToString();
                LogTestEvent($"UpdateDocument: {docId} - {(success ? "成功" : "失敗")}");
            }
            catch (FirebaseOperationException ex)
            {
                ShowError($"UpdateDocument 操作錯誤: {ex.Message}", ltlOperationResults);
            }
            catch (Exception ex)
            {
                ShowError($"UpdateDocument 失敗: {ex.Message}", ltlOperationResults);
            }
        }

        protected async void btnDeleteDocument_Click(object sender, EventArgs e)
        {
            try
            {
                string docId = txtDocumentId.Text.Trim();
                
                if (string.IsNullOrEmpty(docId))
                {
                    ShowError("請輸入文件 ID", ltlOperationResults);
                    return;
                }

                firebaseService = new EnhancedFirebaseService();
                bool success = await firebaseService.DeleteDocument(TEST_COLLECTION, docId);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>🗑️ DeleteDocument 操作結果</h5>");
                
                if (success)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 文件刪除成功</strong><br/>");
                    result.AppendLine($"集合: {TEST_COLLECTION}<br/>");
                    result.AppendLine($"文件 ID: {docId}<br/>");
                    result.AppendLine($"刪除時間: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-warning'>");
                    result.AppendLine($"<strong>⚠️ 文件不存在</strong><br/>");
                    result.AppendLine($"找不到 ID 為 '{docId}' 的文件");
                    result.AppendLine("</div>");
                }

                ltlOperationResults.Text = result.ToString();
                LogTestEvent($"DeleteDocument: {docId} - {(success ? "成功" : "文件不存在")}");
            }
            catch (Exception ex)
            {
                ShowError($"DeleteDocument 失敗: {ex.Message}", ltlOperationResults);
            }
        }

        #endregion

        #region 集合操作測試

        protected async void btnCreateTestData_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                int successCount = 0;
                var errors = new List<string>();

                for (int i = 1; i <= 5; i++)
                {
                    try
                    {
                        var testData = new
                        {
                            id = $"test_batch_{i:D3}",
                            content = $"批量測試數據 #{i}",
                            batchNumber = i,
                            timestamp = DateTime.UtcNow,
                            operation = "CreateTestData",
                            testType = "Batch"
                        };

                        bool success = await firebaseService.AddDocument(TEST_COLLECTION, testData.id, testData);
                        if (success) successCount++;
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"第 {i} 筆: {ex.Message}");
                    }
                }

                var result = new StringBuilder();
                result.AppendLine("<h5>📦 批量建立測試數據結果</h5>");
                
                if (successCount == 5)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 全部成功</strong><br/>");
                    result.AppendLine($"成功建立 {successCount}/5 筆測試數據");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-warning'>");
                    result.AppendLine($"<strong>⚠️ 部分成功</strong><br/>");
                    result.AppendLine($"成功: {successCount}/5 筆<br/>");
                    if (errors.Count > 0)
                    {
                        result.AppendLine("錯誤:<br/>");
                        foreach (var error in errors)
                        {
                            result.AppendLine($"• {error}<br/>");
                        }
                    }
                    result.AppendLine("</div>");
                }

                ltlCollectionData.Text = result.ToString();
                LogTestEvent($"CreateTestData: {successCount}/5 成功");
            }
            catch (Exception ex)
            {
                ShowError($"建立測試數據失敗: {ex.Message}", ltlCollectionData);
            }
        }

        protected async void btnGetCollection_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                var documents = await firebaseService.GetAllDocuments<dynamic>(TEST_COLLECTION);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>📚 GetCollection 操作結果</h5>");
                
                if (documents.Count > 0)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine($"<strong>✅ 成功讀取集合</strong><br/>");
                    result.AppendLine($"集合: {TEST_COLLECTION}<br/>");
                    result.AppendLine($"文件數量: {documents.Count}<br/>");
                    result.AppendLine("</div>");
                    
                    result.AppendLine("<div class='panel panel-default'>");
                    result.AppendLine("<div class='panel-heading'><strong>集合內容</strong></div>");
                    result.AppendLine("<div class='panel-body'>");
                    
                    foreach (var doc in documents)
                    {
                        result.AppendLine("<div class='well well-sm'>");
                        result.AppendLine($"<pre>{Newtonsoft.Json.JsonConvert.SerializeObject(doc, Newtonsoft.Json.Formatting.Indented)}</pre>");
                        result.AppendLine("</div>");
                    }
                    
                    result.AppendLine("</div>");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-info'>");
                    result.AppendLine($"<strong>ℹ️ 集合為空</strong><br/>");
                    result.AppendLine($"集合 '{TEST_COLLECTION}' 中沒有文件");
                    result.AppendLine("</div>");
                }

                ltlCollectionData.Text = result.ToString();
                LogTestEvent($"GetCollection: {documents.Count} 個文件");
            }
            catch (Exception ex)
            {
                ShowError($"讀取集合失敗: {ex.Message}", ltlCollectionData);
            }
        }

        protected async void btnClearTestData_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                var documents = await firebaseService.GetAllDocuments<dynamic>(TEST_COLLECTION);
                
                int deletedCount = 0;
                var errors = new List<string>();

                foreach (var doc in documents)
                {
                    try
                    {
                        string docId = doc.id?.ToString();
                        if (!string.IsNullOrEmpty(docId))
                        {
                            bool deleted = await firebaseService.DeleteDocument(TEST_COLLECTION, docId);
                            if (deleted) deletedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"刪除 {doc.id}: {ex.Message}");
                    }
                }

                var result = new StringBuilder();
                result.AppendLine("<h5>🗑️ 清除測試數據結果</h5>");
                
                result.AppendLine("<div class='alert alert-info'>");
                result.AppendLine($"<strong>🧹 清除完成</strong><br/>");
                result.AppendLine($"成功刪除: {deletedCount} 個文件<br/>");
                result.AppendLine($"總文件數: {documents.Count}");
                
                if (errors.Count > 0)
                {
                    result.AppendLine("<br/>錯誤:<br/>");
                    foreach (var error in errors)
                    {
                        result.AppendLine($"• {error}<br/>");
                    }
                }
                
                result.AppendLine("</div>");

                ltlCollectionData.Text = result.ToString();
                LogTestEvent($"ClearTestData: 刪除 {deletedCount}/{documents.Count} 個文件");
            }
            catch (Exception ex)
            {
                ShowError($"清除測試數據失敗: {ex.Message}", ltlCollectionData);
            }
        }

        #endregion

        #region Authentication 測試

        protected async void btnTestAuthentication_Click(object sender, EventArgs e)
        {
            try
            {
                string idToken = txtIdToken.Text.Trim();
                
                firebaseService = new EnhancedFirebaseService();
                bool isAuthenticated = await firebaseService.IsAuthenticated(idToken);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>🔐 IsAuthenticated 測試結果</h5>");
                
                if (string.IsNullOrEmpty(idToken))
                {
                    result.AppendLine("<div class='alert alert-info'>");
                    result.AppendLine("<strong>ℹ️ 未登入用戶測試</strong><br/>");
                    result.AppendLine($"驗證結果: {(isAuthenticated ? "❌ 意外通過" : "✅ 正確拒絕")}<br/>");
                    result.AppendLine("空 Token 應該被正確拒絕");
                    result.AppendLine("</div>");
                }
                else
                {
                    var alertClass = isAuthenticated ? "alert-success" : "alert-warning";
                    result.AppendLine($"<div class='alert {alertClass}'>");
                    result.AppendLine($"<strong>驗證結果: {(isAuthenticated ? "✅ 已驗證" : "❌ 未驗證")}</strong><br/>");
                    result.AppendLine($"Token 長度: {idToken.Length}<br/>");
                    result.AppendLine($"Token 預覽: {(idToken.Length > 20 ? idToken.Substring(0, 20) + "..." : idToken)}");
                    result.AppendLine("</div>");
                }

                ltlAuthResults.Text = result.ToString();
                LogTestEvent($"IsAuthenticated: {(isAuthenticated ? "通過" : "失敗")} - Token長度: {idToken.Length}");
            }
            catch (FirebaseAuthenticationException ex)
            {
                var result = new StringBuilder();
                result.AppendLine("<h5>🔐 IsAuthenticated 測試結果</h5>");
                result.AppendLine("<div class='alert alert-danger'>");
                result.AppendLine($"<strong>❌ 驗證錯誤</strong><br/>");
                result.AppendLine($"錯誤訊息: {ex.Message}");
                result.AppendLine("</div>");
                
                ltlAuthResults.Text = result.ToString();
            }
            catch (Exception ex)
            {
                ShowError($"驗證測試失敗: {ex.Message}", ltlAuthResults);
            }
        }

        protected async void btnGetUserInfo_Click(object sender, EventArgs e)
        {
            try
            {
                string idToken = txtIdToken.Text.Trim();
                
                if (string.IsNullOrEmpty(idToken))
                {
                    ShowError("請輸入有效的 ID Token", ltlAuthResults);
                    return;
                }

                firebaseService = new EnhancedFirebaseService();
                var userInfo = await firebaseService.GetUserInfo(idToken);
                
                var result = new StringBuilder();
                result.AppendLine("<h5>👤 GetUserInfo 測試結果</h5>");
                
                if (userInfo != null)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine("<strong>✅ 用戶資訊讀取成功</strong><br/>");
                    result.AppendLine($"UID: {userInfo.Uid}<br/>");
                    result.AppendLine($"Email: {userInfo.Email}<br/>");
                    result.AppendLine($"顯示名稱: {userInfo.DisplayName}<br/>");
                    result.AppendLine($"Email 已驗證: {(userInfo.EmailVerified ? "✅" : "❌")}<br/>");
                    result.AppendLine($"建立時間: {userInfo.CreationTimestamp}<br/>");
                    result.AppendLine($"最後登入: {userInfo.LastSignInTimestamp}");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-warning'>");
                    result.AppendLine("<strong>⚠️ 無法取得用戶資訊</strong><br/>");
                    result.AppendLine("Token 可能無效或已過期");
                    result.AppendLine("</div>");
                }

                ltlAuthResults.Text = result.ToString();
                LogTestEvent($"GetUserInfo: {(userInfo != null ? "成功" : "失敗")}");
            }
            catch (FirebaseAuthenticationException ex)
            {
                var result = new StringBuilder();
                result.AppendLine("<h5>👤 GetUserInfo 測試結果</h5>");
                result.AppendLine("<div class='alert alert-danger'>");
                result.AppendLine($"<strong>❌ 用戶資訊取得失敗</strong><br/>");
                result.AppendLine($"錯誤訊息: {ex.Message}");
                result.AppendLine("</div>");
                
                ltlAuthResults.Text = result.ToString();
            }
            catch (Exception ex)
            {
                ShowError($"取得用戶資訊失敗: {ex.Message}", ltlAuthResults);
            }
        }

        #endregion

        #region 錯誤處理測試

        protected async void btnTestInvalidCollection_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                
                // 測試無效的集合名稱
                await firebaseService.AddDocument("", "test", new { test = "data" });
            }
            catch (ArgumentException ex)
            {
                ShowErrorTestResult("無效集合名稱", ex.Message, "✅ 正確拋出 ArgumentException", ltlErrorResults);
            }
            catch (Exception ex)
            {
                ShowErrorTestResult("無效集合名稱", ex.Message, "⚠️ 拋出了其他例外", ltlErrorResults);
            }
        }

        protected async void btnTestNonExistentDoc_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                
                // 測試讀取不存在的文件
                var doc = await firebaseService.GetDocument<dynamic>(TEST_COLLECTION, "non_existent_doc_12345");
                
                var result = doc == null ? "✅ 正確返回 null" : "❌ 應該返回 null";
                ShowErrorTestResult("不存在的文件", "GetDocument 測試", result, ltlErrorResults);
            }
            catch (Exception ex)
            {
                ShowErrorTestResult("不存在的文件", ex.Message, "❌ 不應該拋出例外", ltlErrorResults);
            }
        }

        protected async void btnTestDuplicateAdd_Click(object sender, EventArgs e)
        {
            try
            {
                firebaseService = new EnhancedFirebaseService();
                string testDocId = "duplicate_test_doc";
                
                // 先新增一個文件
                await firebaseService.AddDocument(TEST_COLLECTION, testDocId, new { test = "first" });
                
                // 嘗試新增相同 ID 的文件
                await firebaseService.AddDocument(TEST_COLLECTION, testDocId, new { test = "duplicate" });
                
                ShowErrorTestResult("重複新增文件", "應該拋出例外", "❌ 沒有拋出例外", ltlErrorResults);
            }
            catch (FirebaseOperationException ex)
            {
                ShowErrorTestResult("重複新增文件", ex.Message, "✅ 正確拋出 FirebaseOperationException", ltlErrorResults);
                
                // 清理測試文件
                try
                {
                    await firebaseService.DeleteDocument(TEST_COLLECTION, "duplicate_test_doc");
                }
                catch { }
            }
            catch (Exception ex)
            {
                ShowErrorTestResult("重複新增文件", ex.Message, "⚠️ 拋出了其他例外", ltlErrorResults);
            }
        }

        protected void btnTestCredentialsError_Click(object sender, EventArgs e)
        {
            try
            {
                // 嘗試使用無效的憑證路徑初始化
                var invalidService = new EnhancedFirebaseService("test-project", "/invalid/path/credentials.json");
            }
            catch (FileNotFoundException ex)
            {
                ShowErrorTestResult("憑證錯誤", ex.Message, "✅ 正確拋出 FileNotFoundException", ltlErrorResults);
            }
            catch (FirebaseCredentialsException ex)
            {
                ShowErrorTestResult("憑證錯誤", ex.Message, "✅ 正確拋出 FirebaseCredentialsException", ltlErrorResults);
            }
            catch (Exception ex)
            {
                ShowErrorTestResult("憑證錯誤", ex.Message, "⚠️ 拋出了其他例外", ltlErrorResults);
            }
        }

        #endregion

        #region 性能測試

        protected async void btnRunPerformanceTest_Click(object sender, EventArgs e)
        {
            try
            {
                int testSize = int.Parse(ddlTestDataSize.SelectedValue);
                firebaseService = new EnhancedFirebaseService();
                
                var result = new StringBuilder();
                result.AppendLine($"<h5>⚡ 性能測試結果 ({testSize} 筆數據)</h5>");
                
                // 新增性能測試
                var stopwatch = Stopwatch.StartNew();
                int addSuccessCount = 0;
                
                for (int i = 1; i <= testSize; i++)
                {
                    try
                    {
                        var testData = new
                        {
                            id = $"perf_test_{i:D6}",
                            content = $"性能測試數據 #{i}",
                            index = i,
                            timestamp = DateTime.UtcNow,
                            operation = "PerformanceTest"
                        };

                        bool success = await firebaseService.AddDocument(TEST_COLLECTION, testData.id, testData);
                        if (success) addSuccessCount++;
                    }
                    catch
                    {
                        // 忽略個別錯誤
                    }
                }
                
                var addTime = stopwatch.ElapsedMilliseconds;
                stopwatch.Restart();
                
                // 讀取性能測試
                var allDocs = await firebaseService.GetAllDocuments<dynamic>(TEST_COLLECTION);
                var readTime = stopwatch.ElapsedMilliseconds;
                stopwatch.Restart();
                
                // 刪除性能測試
                int deleteSuccessCount = 0;
                for (int i = 1; i <= testSize; i++)
                {
                    try
                    {
                        string docId = $"perf_test_{i:D6}";
                        bool deleted = await firebaseService.DeleteDocument(TEST_COLLECTION, docId);
                        if (deleted) deleteSuccessCount++;
                    }
                    catch
                    {
                        // 忽略個別錯誤
                    }
                }
                
                var deleteTime = stopwatch.ElapsedMilliseconds;
                
                // 計算性能指標
                double addThroughput = testSize / (addTime / 1000.0);
                double readThroughput = allDocs.Count / (readTime / 1000.0);
                double deleteThroughput = testSize / (deleteTime / 1000.0);
                
                result.AppendLine("<div class='alert alert-info'>");
                result.AppendLine("<strong>📊 性能指標</strong><br/>");
                result.AppendLine($"新增時間: {addTime} ms ({addSuccessCount}/{testSize} 成功)<br/>");
                result.AppendLine($"讀取時間: {readTime} ms ({allDocs.Count} 個文件)<br/>");
                result.AppendLine($"刪除時間: {deleteTime} ms ({deleteSuccessCount}/{testSize} 成功)<br/>");
                result.AppendLine($"新增吞吐量: {addThroughput:F1} 筆/秒<br/>");
                result.AppendLine($"讀取吞吐量: {readThroughput:F1} 筆/秒<br/>");
                result.AppendLine($"刪除吞吐量: {deleteThroughput:F1} 筆/秒");
                result.AppendLine("</div>");

                ltlPerformanceResults.Text = result.ToString();
                LogTestEvent($"性能測試完成: {testSize} 筆, 新增 {addTime}ms, 讀取 {readTime}ms, 刪除 {deleteTime}ms");
            }
            catch (Exception ex)
            {
                ShowError($"性能測試失敗: {ex.Message}", ltlPerformanceResults);
            }
        }

        #endregion

        #region 工具方法

        private void ShowError(string message, System.Web.UI.WebControls.Literal targetControl)
        {
            var errorHtml = $"<div class='alert alert-danger'><strong>錯誤:</strong> {message}</div>";
            targetControl.Text = errorHtml;
        }

        private void ShowErrorTestResult(string testName, string errorMessage, string result, System.Web.UI.WebControls.Literal targetControl)
        {
            var resultHtml = new StringBuilder();
            resultHtml.AppendLine($"<h5>⚠️ {testName} 測試結果</h5>");
            resultHtml.AppendLine("<div class='alert alert-info'>");
            resultHtml.AppendLine($"<strong>測試結果:</strong> {result}<br/>");
            resultHtml.AppendLine($"<strong>錯誤訊息:</strong> {errorMessage}");
            resultHtml.AppendLine("</div>");
            
            targetControl.Text = resultHtml.ToString();
        }

        private void LogTestEvent(string message)
        {
            try
            {
                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [FIREBASE_TEST] {message}";
                var logPath = Server.MapPath("~/App_Data/Logs/firebase_test.log");
                
                System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(logPath));
                System.IO.File.AppendAllText(logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // 記錄失敗不應影響主要功能
            }
        }

        protected override void OnUnload(EventArgs e)
        {
            try
            {
                firebaseService?.Dispose();
            }
            catch
            {
                // 忽略釋放錯誤
            }
            
            base.OnUnload(e);
        }

        #endregion
    }
}