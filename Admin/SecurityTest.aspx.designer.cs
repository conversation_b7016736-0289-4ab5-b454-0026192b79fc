//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace CWDECC_3S.Admin
{
    public partial class SecurityTest
    {
        // 密鑰管理控制項
        protected global::System.Web.UI.WebControls.Button btnGenerateKey;
        protected global::System.Web.UI.WebControls.Button btnTestKeyValidation;
        protected global::System.Web.UI.WebControls.Label lblKeyInfo;
        protected global::System.Web.UI.WebControls.Literal ltlEncryptionStats;

        // 基本測試控制項
        protected global::System.Web.UI.WebControls.TextBox txtChineseTest;
        protected global::System.Web.UI.WebControls.TextBox txtEnglishTest;
        protected global::System.Web.UI.WebControls.TextBox txtNumberTest;
        protected global::System.Web.UI.WebControls.TextBox txtMixedTest;
        protected global::System.Web.UI.WebControls.Button btnRunBasicTests;
        protected global::System.Web.UI.WebControls.Panel pnlTestResults;
        protected global::System.Web.UI.WebControls.Literal ltlTestResults;

        // 安全會員測試控制項
        protected global::System.Web.UI.WebControls.TextBox txtMemberName;
        protected global::System.Web.UI.WebControls.TextBox txtMemberHKID;
        protected global::System.Web.UI.WebControls.TextBox txtMemberPhone;
        protected global::System.Web.UI.WebControls.TextBox txtMemberAddress;
        protected global::System.Web.UI.WebControls.Button btnCreateSecureMember;
        protected global::System.Web.UI.WebControls.Panel pnlEncryptedData;
        protected global::System.Web.UI.WebControls.Literal ltlEncryptedData;
        protected global::System.Web.UI.WebControls.Panel pnlMaskedData;
        protected global::System.Web.UI.WebControls.Literal ltlMaskedData;

        // Firebase 測試控制項
        protected global::System.Web.UI.WebControls.Button btnTestFirebaseWrite;
        protected global::System.Web.UI.WebControls.Button btnTestFirebaseRead;
        protected global::System.Web.UI.WebControls.Panel pnlFirebaseResults;
        protected global::System.Web.UI.WebControls.Literal ltlFirebaseResults;

        // 密鑰安全性測試控制項
        protected global::System.Web.UI.WebControls.Button btnTestKeyRotation;
        protected global::System.Web.UI.WebControls.Panel pnlKeyRotationResults;
        protected global::System.Web.UI.WebControls.Literal ltlKeyRotationResults;

        // 性能測試控制項
        protected global::System.Web.UI.WebControls.DropDownList ddlBatchSize;
        protected global::System.Web.UI.WebControls.Button btnRunPerformanceTest;
        protected global::System.Web.UI.WebControls.Panel pnlPerformanceResults;
        protected global::System.Web.UI.WebControls.Literal ltlPerformanceResults;
    }
}