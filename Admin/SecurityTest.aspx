<%@ Page Title="安全加密測試" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="SecurityTest.aspx.cs" Inherits="CWDECC_3S.Admin.SecurityTest" %>

<asp:Content ID="BodyContent" ContentPlaceHolderID="MainContent" runat="server">

    <div class="row">
        <div class="col-md-12">
            <h2>🔒 AES-256 加密系統測試</h2>
            <p class="lead">驗證敏感資料加密/解密功能及安全性要求</p>
        </div>
    </div>

    <!-- 密鑰管理 -->
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">🔑 密鑰管理</h3>
                </div>
                <div class="panel-body">
                    <asp:Button ID="btnGenerateKey" runat="server" 
                        Text="產生新主密鑰" 
                        CssClass="btn btn-warning" 
                        OnClick="btnGenerateKey_Click" />
                    
                    <asp:Button ID="btnTestKeyValidation" runat="server" 
                        Text="驗證密鑰設定" 
                        CssClass="btn btn-info" 
                        OnClick="btnTestKeyValidation_Click" />
                    
                    <hr />
                    <asp:Label ID="lblKeyInfo" runat="server" CssClass="small"></asp:Label>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h3 class="panel-title">📊 加密統計</h3>
                </div>
                <div class="panel-body">
                    <asp:Literal ID="ltlEncryptionStats" runat="server"></asp:Literal>
                </div>
            </div>
        </div>
    </div>

    <!-- 基本加密測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">🧪 基本加密/解密測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>測試數據輸入</h4>
                            <div class="form-group">
                                <label>中文測試:</label>
                                <asp:TextBox ID="txtChineseTest" runat="server" CssClass="form-control" 
                                    Text="張志明 - 香港九龍旺角彌敦道123號"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label>英文測試:</label>
                                <asp:TextBox ID="txtEnglishTest" runat="server" CssClass="form-control" 
                                    Text="John Smith - 123 Nathan Road, Mong Kok, Kowloon, Hong Kong"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label>數字測試:</label>
                                <asp:TextBox ID="txtNumberTest" runat="server" CssClass="form-control" 
                                    Text="9123-4567 | A123456(7) | 1234567890"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label>混合測試:</label>
                                <asp:TextBox ID="txtMixedTest" runat="server" CssClass="form-control" 
                                    Text="李小芳 Li Siu-fong (李小芳) 📱9876-5432 🏠中環德輔道中456號"></asp:TextBox>
                            </div>
                            
                            <asp:Button ID="btnRunBasicTests" runat="server" 
                                Text="執行基本測試" 
                                CssClass="btn btn-primary btn-block" 
                                OnClick="btnRunBasicTests_Click" />
                        </div>
                        
                        <div class="col-md-6">
                            <h4>測試結果</h4>
                            <asp:Panel ID="pnlTestResults" runat="server" CssClass="well">
                                <asp:Literal ID="ltlTestResults" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 安全會員模型測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">👤 安全會員模型測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h4>建立測試會員</h4>
                            <div class="form-group">
                                <label>姓名:</label>
                                <asp:TextBox ID="txtMemberName" runat="server" CssClass="form-control" 
                                    Text="陳大文"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label>身份證:</label>
                                <asp:TextBox ID="txtMemberHKID" runat="server" CssClass="form-control" 
                                    Text="A123456(7)"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label>電話:</label>
                                <asp:TextBox ID="txtMemberPhone" runat="server" CssClass="form-control" 
                                    Text="9123-4567"></asp:TextBox>
                            </div>
                            <div class="form-group">
                                <label>地址:</label>
                                <asp:TextBox ID="txtMemberAddress" runat="server" CssClass="form-control" 
                                    Text="香港港島中環德輔道中789號ABC大廈15樓A室"></asp:TextBox>
                            </div>
                            
                            <asp:Button ID="btnCreateSecureMember" runat="server" 
                                Text="建立安全會員" 
                                CssClass="btn btn-success btn-block" 
                                OnClick="btnCreateSecureMember_Click" />
                        </div>
                        
                        <div class="col-md-4">
                            <h4>加密後數據</h4>
                            <asp:Panel ID="pnlEncryptedData" runat="server" CssClass="well">
                                <asp:Literal ID="ltlEncryptedData" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                        
                        <div class="col-md-4">
                            <h4>遮罩顯示</h4>
                            <asp:Panel ID="pnlMaskedData" runat="server" CssClass="well">
                                <asp:Literal ID="ltlMaskedData" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase 整合測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">🔥 Firebase 整合測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Firebase 寫入測試</h4>
                            <p>測試加密資料寫入 Firestore</p>
                            <asp:Button ID="btnTestFirebaseWrite" runat="server" 
                                Text="測試 Firebase 寫入" 
                                CssClass="btn btn-warning" 
                                OnClick="btnTestFirebaseWrite_Click" />
                        </div>
                        
                        <div class="col-md-6">
                            <h4>Firebase 讀取測試</h4>
                            <p>測試從 Firestore 讀取並解密</p>
                            <asp:Button ID="btnTestFirebaseRead" runat="server" 
                                Text="測試 Firebase 讀取" 
                                CssClass="btn btn-info" 
                                OnClick="btnTestFirebaseRead_Click" />
                        </div>
                    </div>
                    
                    <hr />
                    <asp:Panel ID="pnlFirebaseResults" runat="server" CssClass="well">
                        <asp:Literal ID="ltlFirebaseResults" runat="server"></asp:Literal>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>

    <!-- 密鑰輪換測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-danger">
                <div class="panel-heading">
                    <h3 class="panel-title">🔄 密鑰安全性測試</h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-warning">
                        <strong>警告:</strong> 此測試會模擬密鑰替換，確保舊數據不可解密。
                    </div>
                    
                    <asp:Button ID="btnTestKeyRotation" runat="server" 
                        Text="測試密鑰替換安全性" 
                        CssClass="btn btn-danger" 
                        OnClick="btnTestKeyRotation_Click" 
                        OnClientClick="return confirm('確定要執行密鑰安全性測試嗎？這會驗證密鑰替換後的安全性。');" />
                    
                    <hr />
                    <asp:Panel ID="pnlKeyRotationResults" runat="server" CssClass="well">
                        <asp:Literal ID="ltlKeyRotationResults" runat="server"></asp:Literal>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能測試 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">⚡ 性能測試</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label>測試批次大小:</label>
                            <asp:DropDownList ID="ddlBatchSize" runat="server" CssClass="form-control">
                                <asp:ListItem Text="100 筆" Value="100"></asp:ListItem>
                                <asp:ListItem Text="500 筆" Value="500" Selected="True"></asp:ListItem>
                                <asp:ListItem Text="1000 筆" Value="1000"></asp:ListItem>
                                <asp:ListItem Text="5000 筆" Value="5000"></asp:ListItem>
                            </asp:DropDownList>
                            <br />
                            <asp:Button ID="btnRunPerformanceTest" runat="server" 
                                Text="執行性能測試" 
                                CssClass="btn btn-default" 
                                OnClick="btnRunPerformanceTest_Click" />
                        </div>
                        
                        <div class="col-md-6">
                            <asp:Panel ID="pnlPerformanceResults" runat="server" CssClass="well">
                                <asp:Literal ID="ltlPerformanceResults" runat="server"></asp:Literal>
                            </asp:Panel>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        // 自動重新整理某些結果面板
        function refreshResults() {
            // 可以加入 AJAX 自動更新功能
        }
        
        // 頁面載入完成後執行
        $(document).ready(function() {
            // 初始化工具提示
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>

</asp:Content>