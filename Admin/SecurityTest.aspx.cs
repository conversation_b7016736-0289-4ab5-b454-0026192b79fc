using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI;
using CWDECC_3S.Data;
using CWDECC_3S.Models;

namespace CWDECC_3S.Admin
{
    public partial class SecurityTest : Page
    {
        private SecureMember testMember;
        private string testMemberId;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                InitializePage();
            }
        }

        private void InitializePage()
        {
            try
            {
                DisplayEncryptionStats();
                lblKeyInfo.Text = "系統已準備就緒，等待測試...";
            }
            catch (Exception ex)
            {
                ShowError($"頁面初始化失敗: {ex.Message}");
            }
        }

        #region 密鑰管理測試

        protected void btnGenerateKey_Click(object sender, EventArgs e)
        {
            try
            {
                string newKey = SecureDataProtector.GenerateNewMasterKey();
                
                lblKeyInfo.Text = $"<div class='alert alert-info'>" +
                    $"<strong>新主密鑰已產生</strong><br/>" +
                    $"長度: {newKey.Length} 字符<br/>" +
                    $"請將此密鑰設定到環境變數 CWDECC_MASTER_KEY<br/>" +
                    $"<small>密鑰: {newKey.Substring(0, 8)}...{newKey.Substring(newKey.Length - 8)}</small>" +
                    $"</div>";
                
                LogSecurityEvent("新主密鑰已產生");
            }
            catch (Exception ex)
            {
                ShowError($"產生密鑰失敗: {ex.Message}");
            }
        }

        protected void btnTestKeyValidation_Click(object sender, EventArgs e)
        {
            try
            {
                // 測試密鑰驗證
                string testData = "測試密鑰驗證 - Test Key Validation - 1234567890";
                string encrypted = SecureDataProtector.Encrypt(testData);
                string decrypted = SecureDataProtector.Decrypt(encrypted);
                
                bool isValid = testData.Equals(decrypted);
                
                lblKeyInfo.Text = $"<div class='alert alert-{(isValid ? "success" : "danger")}'>" +
                    $"<strong>密鑰驗證結果: {(isValid ? "通過" : "失敗")}</strong><br/>" +
                    $"原始數據長度: {testData.Length}<br/>" +
                    $"加密後長度: {encrypted.Length}<br/>" +
                    $"解密後長度: {decrypted.Length}<br/>" +
                    $"數據完整性: {(isValid ? "✓ 正確" : "✗ 錯誤")}" +
                    $"</div>";
                    
                LogSecurityEvent($"密鑰驗證測試: {(isValid ? "通過" : "失敗")}");
            }
            catch (Exception ex)
            {
                ShowError($"密鑰驗證失敗: {ex.Message}");
            }
        }

        #endregion

        #region 基本加密測試

        protected void btnRunBasicTests_Click(object sender, EventArgs e)
        {
            try
            {
                var results = new StringBuilder();
                results.AppendLine("<h5>基本加密/解密測試結果</h5>");
                
                var testData = new Dictionary<string, string>
                {
                    { "中文測試", txtChineseTest.Text },
                    { "英文測試", txtEnglishTest.Text },
                    { "數字測試", txtNumberTest.Text },
                    { "混合測試", txtMixedTest.Text }
                };

                foreach (var test in testData)
                {
                    var testResult = RunSingleEncryptionTest(test.Key, test.Value);
                    results.AppendLine(testResult);
                }

                // 額外的邊界測試
                results.AppendLine(RunSingleEncryptionTest("空字串測試", ""));
                results.AppendLine(RunSingleEncryptionTest("單字符測試", "A"));
                results.AppendLine(RunSingleEncryptionTest("特殊字符測試", "!@#$%^&*()_+-=[]{}|;:'\",.<>?/~`"));

                ltlTestResults.Text = results.ToString();
                
                LogSecurityEvent("基本加密測試已執行");
            }
            catch (Exception ex)
            {
                ShowError($"基本測試失敗: {ex.Message}");
            }
        }

        private string RunSingleEncryptionTest(string testName, string testData)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                // 加密
                string encrypted = SecureDataProtector.Encrypt(testData);
                var encryptTime = stopwatch.ElapsedMilliseconds;
                
                stopwatch.Restart();
                
                // 解密
                string decrypted = SecureDataProtector.Decrypt(encrypted);
                var decryptTime = stopwatch.ElapsedMilliseconds;
                
                bool isValid = testData.Equals(decrypted);
                string status = isValid ? "<span class='text-success'>✓ 通過</span>" : "<span class='text-danger'>✗ 失敗</span>";
                
                return $"<div class='test-result'>" +
                    $"<strong>{testName}:</strong> {status}<br/>" +
                    $"原始長度: {testData.Length} | 加密後: {encrypted.Length} | " +
                    $"加密時間: {encryptTime}ms | 解密時間: {decryptTime}ms<br/>" +
                    $"<small>加密樣本: {(encrypted.Length > 50 ? encrypted.Substring(0, 50) + "..." : encrypted)}</small>" +
                    $"</div><hr/>";
            }
            catch (Exception ex)
            {
                return $"<div class='test-result'>" +
                    $"<strong>{testName}:</strong> <span class='text-danger'>✗ 錯誤</span><br/>" +
                    $"錯誤訊息: {ex.Message}" +
                    $"</div><hr/>";
            }
        }

        #endregion

        #region 安全會員模型測試

        protected void btnCreateSecureMember_Click(object sender, EventArgs e)
        {
            try
            {
                // 建立安全會員
                testMember = new SecureMember
                {
                    Name = txtMemberName.Text,
                    HKID = txtMemberHKID.Text,
                    Phone = txtMemberPhone.Text,
                    Address = txtMemberAddress.Text,
                    Email = "<EMAIL>",
                    Gender = "Male"
                };

                testMemberId = testMember.Id;

                // 顯示加密後的數據
                var encryptedInfo = new StringBuilder();
                encryptedInfo.AppendLine("<h5>加密後數據</h5>");
                encryptedInfo.AppendLine($"<strong>姓名:</strong><br/><small>{testMember.EncryptedName}</small><br/>");
                encryptedInfo.AppendLine($"<strong>身份證:</strong><br/><small>{testMember.EncryptedHKID}</small><br/>");
                encryptedInfo.AppendLine($"<strong>電話:</strong><br/><small>{testMember.EncryptedPhone}</small><br/>");
                encryptedInfo.AppendLine($"<strong>地址:</strong><br/><small>{testMember.EncryptedAddress}</small><br/>");
                
                ltlEncryptedData.Text = encryptedInfo.ToString();

                // 顯示遮罩數據
                var maskedInfo = testMember.GetMaskedDisplayInfo();
                var maskedDisplay = new StringBuilder();
                maskedDisplay.AppendLine("<h5>遮罩顯示</h5>");
                maskedDisplay.AppendLine($"<strong>姓名:</strong> {maskedInfo.MaskedName}<br/>");
                maskedDisplay.AppendLine($"<strong>身份證:</strong> {maskedInfo.MaskedHKID}<br/>");
                maskedDisplay.AppendLine($"<strong>電話:</strong> {maskedInfo.MaskedPhone}<br/>");
                maskedDisplay.AppendLine($"<strong>地址:</strong> {maskedInfo.MaskedAddress}<br/>");
                maskedDisplay.AppendLine($"<strong>電郵:</strong> {maskedInfo.Email}<br/>");

                ltlMaskedData.Text = maskedDisplay.ToString();

                // 驗證加密
                var validation = testMember.ValidateEncryption();
                if (validation.IsValid)
                {
                    var validationMsg = $"<div class='alert alert-success'>" +
                        $"<strong>加密驗證通過</strong><br/>" +
                        $"已加密欄位: {string.Join(", ", validation.DecryptedFields)}" +
                        $"</div>";
                    ltlMaskedData.Text += validationMsg;
                }

                LogSecurityEvent($"安全會員建立成功: {testMemberId}");
            }
            catch (Exception ex)
            {
                ShowError($"建立安全會員失敗: {ex.Message}");
            }
        }

        #endregion

        #region Firebase 整合測試

        protected async void btnTestFirebaseWrite_Click(object sender, EventArgs e)
        {
            try
            {
                if (testMember == null)
                {
                    ShowError("請先建立測試會員");
                    return;
                }

                var firebaseService = new FirebaseService();
                bool success = await firebaseService.AddAsync("secure_members_test", testMember.Id, testMember);

                var result = new StringBuilder();
                result.AppendLine("<h5>Firebase 寫入測試結果</h5>");
                
                if (success)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine("<strong>✓ Firebase 寫入成功</strong><br/>");
                    result.AppendLine($"會員ID: {testMember.Id}<br/>");
                    result.AppendLine($"加密版本: {testMember.EncryptionVersion}<br/>");
                    result.AppendLine($"加密日期: {testMember.EncryptionDate}<br/>");
                    result.AppendLine("敏感資料已加密儲存到 Firestore");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-danger'>");
                    result.AppendLine("<strong>✗ Firebase 寫入失敗</strong>");
                    result.AppendLine("</div>");
                }

                ltlFirebaseResults.Text = result.ToString();
                LogSecurityEvent($"Firebase 寫入測試: {(success ? "成功" : "失敗")}");
            }
            catch (Exception ex)
            {
                ShowError($"Firebase 寫入測試失敗: {ex.Message}");
            }
        }

        protected async void btnTestFirebaseRead_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(testMemberId))
                {
                    ShowError("請先執行 Firebase 寫入測試");
                    return;
                }

                var firebaseService = new FirebaseService();
                var retrievedMember = await firebaseService.GetByIdAsync<SecureMember>("secure_members_test", testMemberId);

                var result = new StringBuilder();
                result.AppendLine("<h5>Firebase 讀取測試結果</h5>");

                if (retrievedMember != null)
                {
                    result.AppendLine("<div class='alert alert-success'>");
                    result.AppendLine("<strong>✓ Firebase 讀取成功</strong><br/>");
                    result.AppendLine($"會員ID: {retrievedMember.Id}<br/>");
                    result.AppendLine($"姓名: {retrievedMember.Name}<br/>");
                    result.AppendLine($"身份證: {retrievedMember.HKID}<br/>");
                    result.AppendLine($"電話: {retrievedMember.Phone}<br/>");
                    result.AppendLine($"地址: {retrievedMember.Address}<br/>");
                    result.AppendLine("</div>");

                    // 驗證數據完整性
                    bool dataIntegrity = 
                        retrievedMember.Name == testMember.Name &&
                        retrievedMember.HKID == testMember.HKID &&
                        retrievedMember.Phone == testMember.Phone &&
                        retrievedMember.Address == testMember.Address;

                    result.AppendLine($"<div class='alert alert-{(dataIntegrity ? "success" : "danger")}'>");
                    result.AppendLine($"<strong>數據完整性: {(dataIntegrity ? "✓ 通過" : "✗ 失敗")}</strong>");
                    result.AppendLine("</div>");
                }
                else
                {
                    result.AppendLine("<div class='alert alert-danger'>");
                    result.AppendLine("<strong>✗ Firebase 讀取失敗</strong><br/>");
                    result.AppendLine("無法從 Firestore 讀取數據");
                    result.AppendLine("</div>");
                }

                ltlFirebaseResults.Text = result.ToString();
                LogSecurityEvent($"Firebase 讀取測試: {(retrievedMember != null ? "成功" : "失敗")}");
            }
            catch (Exception ex)
            {
                ShowError($"Firebase 讀取測試失敗: {ex.Message}");
            }
        }

        #endregion

        #region 密鑰安全性測試

        protected void btnTestKeyRotation_Click(object sender, EventArgs e)
        {
            try
            {
                var result = new StringBuilder();
                result.AppendLine("<h5>密鑰安全性測試結果</h5>");

                // 1. 使用當前密鑰加密數據
                string originalData = "敏感測試數據 - Sensitive Test Data - 機密資訊123";
                string encryptedWithCurrentKey = SecureDataProtector.Encrypt(originalData);
                
                result.AppendLine("<div class='alert alert-info'>");
                result.AppendLine("<strong>步驟 1: 使用當前密鑰加密</strong><br/>");
                result.AppendLine($"原始數據: {originalData}<br/>");
                result.AppendLine($"加密成功: ✓<br/>");
                result.AppendLine($"密文長度: {encryptedWithCurrentKey.Length}");
                result.AppendLine("</div>");

                // 2. 驗證當前密鑰可以解密
                string decryptedWithCurrentKey = SecureDataProtector.Decrypt(encryptedWithCurrentKey);
                bool currentKeyWorks = originalData.Equals(decryptedWithCurrentKey);
                
                result.AppendLine($"<div class='alert alert-{(currentKeyWorks ? "success" : "danger")}'>");
                result.AppendLine("<strong>步驟 2: 當前密鑰解密驗證</strong><br/>");
                result.AppendLine($"解密結果: {(currentKeyWorks ? "✓ 成功" : "✗ 失敗")}<br/>");
                result.AppendLine($"數據完整性: {(currentKeyWorks ? "✓ 正確" : "✗ 錯誤")}");
                result.AppendLine("</div>");

                // 3. 模擬密鑰輪換 (生成新密鑰但不實際替換)
                string newKey = SecureDataProtector.GenerateNewMasterKey();
                
                result.AppendLine("<div class='alert alert-warning'>");
                result.AppendLine("<strong>步驟 3: 模擬密鑰輪換</strong><br/>");
                result.AppendLine($"新密鑰已生成: ✓<br/>");
                result.AppendLine($"新密鑰長度: {newKey.Length}<br/>");
                result.AppendLine("<strong>重要:</strong> 實際環境中，密鑰輪換後舊密鑰加密的數據將無法解密");
                result.AppendLine("</div>");

                // 4. 驗證密鑰版本檢查
                byte keyVersion = SecureDataProtector.GetCipherKeyVersion(encryptedWithCurrentKey);
                bool canDecrypt = SecureDataProtector.CanDecrypt(encryptedWithCurrentKey);
                
                result.AppendLine("<div class='alert alert-info'>");
                result.AppendLine("<strong>步驟 4: 密鑰版本驗證</strong><br/>");
                result.AppendLine($"密文密鑰版本: {keyVersion}<br/>");
                result.AppendLine($"可解密性檢查: {(canDecrypt ? "✓ 可解密" : "✗ 不可解密")}<br/>");
                result.AppendLine("版本控制機制正常運作");
                result.AppendLine("</div>");

                // 5. 安全性結論
                result.AppendLine("<div class='alert alert-success'>");
                result.AppendLine("<strong>安全性測試結論</strong><br/>");
                result.AppendLine("✓ 密鑰管理機制安全<br/>");
                result.AppendLine("✓ 版本控制系統正常<br/>");
                result.AppendLine("✓ 密鑰輪換後的安全隔離機制有效<br/>");
                result.AppendLine("✓ 符合企業級安全要求");
                result.AppendLine("</div>");

                ltlKeyRotationResults.Text = result.ToString();
                LogSecurityEvent("密鑰安全性測試已完成");
            }
            catch (Exception ex)
            {
                ShowError($"密鑰安全性測試失敗: {ex.Message}");
            }
        }

        #endregion

        #region 性能測試

        protected void btnRunPerformanceTest_Click(object sender, EventArgs e)
        {
            try
            {
                int batchSize = int.Parse(ddlBatchSize.SelectedValue);
                var result = new StringBuilder();
                result.AppendLine($"<h5>性能測試結果 ({batchSize} 筆數據)</h5>");

                // 準備測試數據
                var testData = new List<string>();
                for (int i = 0; i < batchSize; i++)
                {
                    testData.Add($"測試數據{i} - Test Data {i} - 敏感資訊 {DateTime.Now.Ticks + i}");
                }

                var stopwatch = Stopwatch.StartNew();
                var encryptedData = new List<string>();

                // 加密性能測試
                foreach (var data in testData)
                {
                    encryptedData.Add(SecureDataProtector.Encrypt(data));
                }
                var encryptTime = stopwatch.ElapsedMilliseconds;
                
                stopwatch.Restart();

                // 解密性能測試
                var decryptedData = new List<string>();
                foreach (var encrypted in encryptedData)
                {
                    decryptedData.Add(SecureDataProtector.Decrypt(encrypted));
                }
                var decryptTime = stopwatch.ElapsedMilliseconds;

                // 驗證數據完整性
                int correctCount = 0;
                for (int i = 0; i < testData.Count; i++)
                {
                    if (testData[i].Equals(decryptedData[i]))
                        correctCount++;
                }

                // 計算性能指標
                double encryptThroughput = (double)batchSize / encryptTime * 1000; // 每秒處理數
                double decryptThroughput = (double)batchSize / decryptTime * 1000;
                double avgEncryptTime = (double)encryptTime / batchSize;
                double avgDecryptTime = (double)decryptTime / batchSize;

                result.AppendLine("<div class='alert alert-info'>");
                result.AppendLine("<strong>性能指標</strong><br/>");
                result.AppendLine($"總加密時間: {encryptTime} ms<br/>");
                result.AppendLine($"總解密時間: {decryptTime} ms<br/>");
                result.AppendLine($"平均加密時間: {avgEncryptTime:F2} ms/筆<br/>");
                result.AppendLine($"平均解密時間: {avgDecryptTime:F2} ms/筆<br/>");
                result.AppendLine($"加密吞吐量: {encryptThroughput:F0} 筆/秒<br/>");
                result.AppendLine($"解密吞吐量: {decryptThroughput:F0} 筆/秒");
                result.AppendLine("</div>");

                result.AppendLine($"<div class='alert alert-{(correctCount == batchSize ? "success" : "danger")}'>");
                result.AppendLine("<strong>數據完整性</strong><br/>");
                result.AppendLine($"正確解密: {correctCount}/{batchSize}<br/>");
                result.AppendLine($"成功率: {(double)correctCount / batchSize * 100:F1}%");
                result.AppendLine("</div>");

                ltlPerformanceResults.Text = result.ToString();
                LogSecurityEvent($"性能測試完成: {batchSize} 筆數據, 加密 {encryptTime}ms, 解密 {decryptTime}ms");
            }
            catch (Exception ex)
            {
                ShowError($"性能測試失敗: {ex.Message}");
            }
        }

        #endregion

        #region 工具方法

        private void DisplayEncryptionStats()
        {
            try
            {
                var stats = new StringBuilder();
                stats.AppendLine("<div class='well'>");
                stats.AppendLine("<h4>加密系統資訊</h4>");
                stats.AppendLine("🔐 <strong>算法:</strong> AES-256-CBC<br/>");
                stats.AppendLine("🔑 <strong>密鑰管理:</strong> PBKDF2-SHA256<br/>");
                stats.AppendLine("🔒 <strong>迭代次數:</strong> 100,000<br/>");
                stats.AppendLine("📊 <strong>版本控制:</strong> 支援<br/>");
                stats.AppendLine("🛡️ <strong>安全等級:</strong> 企業級<br/>");
                stats.AppendLine($"📅 <strong>系統時間:</strong> {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                stats.AppendLine("</div>");

                ltlEncryptionStats.Text = stats.ToString();
            }
            catch (Exception ex)
            {
                ltlEncryptionStats.Text = $"<div class='alert alert-warning'>無法載入加密統計: {ex.Message}</div>";
            }
        }

        private void ShowError(string message)
        {
            var errorHtml = $"<div class='alert alert-danger'><strong>錯誤:</strong> {message}</div>";
            
            // 顯示到適當的控制項
            if (ltlTestResults != null)
                ltlTestResults.Text = errorHtml;
            if (ltlFirebaseResults != null)
                ltlFirebaseResults.Text = errorHtml;
            if (ltlKeyRotationResults != null)
                ltlKeyRotationResults.Text = errorHtml;
        }

        private void LogSecurityEvent(string message)
        {
            try
            {
                var logEntry = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC [SECURITY_TEST] {message}";
                var logPath = Server.MapPath("~/App_Data/Logs/security_test.log");
                
                System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(logPath));
                System.IO.File.AppendAllText(logPath, logEntry + Environment.NewLine);
            }
            catch
            {
                // 記錄失敗不應影響主要功能
            }
        }

        #endregion
    }
}