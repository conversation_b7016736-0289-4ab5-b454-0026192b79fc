//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace CWDECC_3S.Admin
{
    public partial class FirebaseTest
    {
        // 連接測試控制項
        protected global::System.Web.UI.WebControls.Button btnTestConnection;
        protected global::System.Web.UI.WebControls.Button btnTestCredentials;
        protected global::System.Web.UI.WebControls.Panel pnlConnectionStatus;
        protected global::System.Web.UI.WebControls.Literal ltlConnectionStatus;

        // CRUD 操作控制項
        protected global::System.Web.UI.WebControls.TextBox txtDocumentId;
        protected global::System.Web.UI.WebControls.TextBox txtTestContent;
        protected global::System.Web.UI.WebControls.Button btnAddDocument;
        protected global::System.Web.UI.WebControls.Button btnGetDocument;
        protected global::System.Web.UI.WebControls.Button btnUpdateDocument;
        protected global::System.Web.UI.WebControls.Button btnDeleteDocument;
        protected global::System.Web.UI.WebControls.Panel pnlOperationResults;
        protected global::System.Web.UI.WebControls.Literal ltlOperationResults;

        // 集合操作控制項
        protected global::System.Web.UI.WebControls.Button btnCreateTestData;
        protected global::System.Web.UI.WebControls.Button btnGetCollection;
        protected global::System.Web.UI.WebControls.Button btnClearTestData;
        protected global::System.Web.UI.WebControls.Panel pnlCollectionData;
        protected global::System.Web.UI.WebControls.Literal ltlCollectionData;

        // Authentication 控制項
        protected global::System.Web.UI.WebControls.TextBox txtIdToken;
        protected global::System.Web.UI.WebControls.Button btnTestAuthentication;
        protected global::System.Web.UI.WebControls.Button btnGetUserInfo;
        protected global::System.Web.UI.WebControls.Panel pnlAuthResults;
        protected global::System.Web.UI.WebControls.Literal ltlAuthResults;

        // 錯誤處理測試控制項
        protected global::System.Web.UI.WebControls.Button btnTestInvalidCollection;
        protected global::System.Web.UI.WebControls.Button btnTestNonExistentDoc;
        protected global::System.Web.UI.WebControls.Button btnTestDuplicateAdd;
        protected global::System.Web.UI.WebControls.Button btnTestCredentialsError;
        protected global::System.Web.UI.WebControls.Panel pnlErrorResults;
        protected global::System.Web.UI.WebControls.Literal ltlErrorResults;

        // 性能測試控制項
        protected global::System.Web.UI.WebControls.DropDownList ddlTestDataSize;
        protected global::System.Web.UI.WebControls.Button btnRunPerformanceTest;
        protected global::System.Web.UI.WebControls.Panel pnlPerformanceResults;
        protected global::System.Web.UI.WebControls.Literal ltlPerformanceResults;
    }
}